from typing import List, Dict, Generator
from types import SimpleNamespace
from sqlalchemy.orm import Session
from Dimensions.Product.product_model import ProductModel
from Dimensions.Book.book_model import BookModel
from Facts.RealizationService.realization_book_model import RealizationBookModel
from Facts.RealizationService.realization_service_base import RealizationServiceBase
from Facts.RealizationService.service_queries import Queries

class RealizationBookService(RealizationServiceBase):
    def fetch_entities(self):
        with Session(self.database.engine) as session:
            products = session.query(ProductModel).all()
            books = session.query(BookModel).all()
        return books, products
    
    def get_sells_generator(self, book_id: int, product_id: int) -> Generator[Dict, None, None]:
        raw_results = self._execute_query(Queries.open_sells_book(book_id, product_id))
        converted_results = self._convert_decimal_to_float(raw_results)
        for sell in converted_results:
            yield sell

    def get_buys_generator(self, book_id: int, product_id: int) -> Generator[Dict, None, None]:
        raw_results = self._execute_query(Queries.open_buys_book(book_id, product_id))
        converted_results = self._convert_decimal_to_float(raw_results)
        for buy in converted_results:
            yield buy

    def get_sells(self, book_id: int, product_id: int) -> List[Dict]:
        return list(self.get_sells_generator(book_id, product_id))

    def get_buys(self, book_id: int, product_id: int) -> List[Dict]:
        return list(self.get_buys_generator(book_id, product_id))

    def get_model(self):
        return RealizationBookModel