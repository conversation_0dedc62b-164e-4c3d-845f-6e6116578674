from sqlalchemy import  Column, Integer, String
from setup_global import SetupGlobal
from libs.mixin import AuditMixin

class LimitGroupModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'limit_group'
    id = Column(Integer, primary_key=True)
    name = Column(String(50))
    description = Column(String(100))


    def __init__(self,
                name,
                description):

        self.name = name
        self.description = description
