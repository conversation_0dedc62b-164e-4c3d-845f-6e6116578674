from sqlalchemy import  <PERSON><PERSON>an, <PERSON>umn, Integer, String
from setup_global import SetupGlobal
from libs.mixin import AuditMixin

class TraderModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'trader'
    id = Column(Integer, primary_key=True)
    trader_name = Column(String(100))
    department = Column(String(50))
    business_unit = Column(String(50))
    active = Column(Boolean)
    contact_info = Column(String(50))
    trader_classification = Column(String(50))


    def __init__(self,
                trader_name,
                department,
                business_unit,
                active,
                contact_info,
                trader_classification):

        self.trader_name = trader_name
        self.department = department
        self.business_unit = business_unit
        self.active = active
        self.contact_info = contact_info
        self.trader_classification = trader_classification
