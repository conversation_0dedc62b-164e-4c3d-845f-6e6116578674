from datetime import timedelta, datetime
from Dimensions.Holiday.holiday_model import HolidayModel
from Dimensions.Country.country_model import CountryModel
from pyexcel_io.database.querysets import QuerysetsReader
from libs.setup_orm_utility import SetupOrmUtility
from libs.setup_holiday_utility import SetupHolidayUtility

import logging

logger = logging.getLogger(__name__)

class HolidayImporter():

    def __init__(self, config_name):
        self.setup_orm_utility = SetupOrmUtility(config_name)


        query_sets = self.setup_orm_utility.codes_session.query(HolidayModel).distinct("country_id").all()
        reader = QuerysetsReader(query_sets, ["country_id"])
        self.existing_countries = list(reader.to_array())

    def import_data(self):
        self.rows_inserted = 0

        logger.info('Import started')

        start_date = datetime(2020, 1, 1)
        end_date = datetime(2040, 1, 1)

        data = self._get_data(start_date, end_date)
        self.setup_orm_utility.import_data(self.holiday_init_func, 'holiday',HolidayModel, data)

        logger.info('Import ended. Inserted %s rows', self.rows_inserted)

    def _get_data(self, start_date, end_date):

        result  = [
            [
                'date',
                'country_id',
                'is_work_free_day',
                'is_pre_work_free_day',
                'is_post_work_free_day',
                'is_bridge']]

        query_sets = self.setup_orm_utility.codes_session.query(CountryModel).all()
        reader = QuerysetsReader(query_sets, ["id","country_code2"])
        countries = list(reader.to_array())

        for country in countries[1:]:
            if self.country_holiday_exists(country):
                continue

            holiday_util = SetupHolidayUtility(country[1])

            if not holiday_util.is_initialized():
                continue

            current_date = start_date
            while current_date < end_date:
                result.append([
                            current_date,
                            country[0],
                            holiday_util.is_holiday(current_date),
                            holiday_util.is_pre_work_free_day(current_date),
                            holiday_util.is_post_work_free_day(current_date),
                            holiday_util.is_holiday_bridge(current_date),
                ])
                current_date += timedelta(days = 1)


        return {'holiday' : result}

    def holiday_init_func(self,row):

        self.rows_inserted += 1

        return HolidayModel(
                row['date'],
                row['country_id'],
                row['is_work_free_day'],
                row['is_pre_work_free_day'],
                row['is_post_work_free_day'],
                row['is_bridge'])

    def country_holiday_exists(self, country):
        for existing_country in self.existing_countries:
                if len(existing_country) > 0 and existing_country[0] == country[0]:
                    return True

        return False