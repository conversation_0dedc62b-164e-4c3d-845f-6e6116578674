from Facts.Utils.database_handler import DatabaseHandler
import pandas as pd
from libs.setup_orm_utility import SetupOrmUtility
from sqlalchemy.orm import Session
from sqlalchemy import text

class Timeseries:
    def __init__(self, config_name):
        self.config_name = config_name

    @staticmethod
    def get_hpfc_price_by_interval(dt_from, dt_to, country, database, inclusive_end_date, update: bool = False, type: str = 'Power', cond: str = 'and 1 = 1'):
        # Determine end date condition
        end_date_condition = "date_time_value <= :dt_to" if inclusive_end_date else "date_time_value < :dt_to"
        # Set up type-specific parameters
        params = {
            'dt_from': dt_from,
            'dt_to': dt_to,
            'country': country,
            'type': type
        }
        if type == "Power":
            name = f"HPFC {country}"
            geo = f"{country}|Country"
            category = "Power|Price|Synthetic|Day Ahead"
        elif type == "Natural Gas":
            name = f"GAS HPFC {country}"
            geo = f"{country}|Country"
            category = "Natural Gas|Price|Synthetic|Day Ahead"
            if cond == "USDMMBTU":
                cond = "and name like :usdmmbtu"
                name = f"GAS HPFC USDMMBTU {country}"
                params['usdmmbtu'] = '%USDMMBTU%'
            else:
                cond = "and name not like :usdmmbtu"
                params['usdmmbtu'] = '%USDMMBTU%'
        else:
            name = "CO2 PFC EU"
            geo = "EU|Region"
            category = "CO2|Price|Synthetic|Day Ahead"

        params.update({
            'geo': geo,
            'category': category
        })
        if name == "CO2 PFC EU":
            params['dt_from'] = dt_from.replace(year=dt_from.year - 1)
            # Base query with parameters
            base_query = """
                WITH max_tick AS (
                    SELECT MAX(date_time_tick) as date_time_tick
                    FROM timeseries.time_series_value tsv
                    WHERE time_series_id = (
                        SELECT b.id 
                        FROM timeseries.level_relation_view b
                        WHERE category = :category
                        AND geography_from = :geo
                        {cond}
                    )
                ),
                data_pfc AS (
                    SELECT date_time_value + INTERVAL '1 YEAR' as date_time_value,
                        date_time_to_value + INTERVAL '1 YEAR' as date_time_to_value,
                        date_time_tick,
                        numeric_value
                    FROM timeseries.time_series_value a
                    WHERE a.time_series_id = (
                        SELECT b.id 
                        FROM timeseries.level_relation_view b
                        WHERE category = :category
                        AND geography_from = :geo
                        {cond}
                    )
                    AND date_time_value >= :dt_from 
                    AND {end_date_condition}
                )
            """
        else:
            # Base query with parameters
            base_query = """
                WITH max_tick AS (
                    SELECT MAX(date_time_tick) as date_time_tick
                    FROM timeseries.time_series_value tsv
                    WHERE time_series_id = (
                        SELECT b.id 
                        FROM timeseries.level_relation_view b
                        WHERE category = :category
                        AND geography_from = :geo
                        {cond}
                    )
                ),
                data_pfc AS (
                    SELECT date_time_value,
                        date_time_to_value,
                        date_time_tick,
                        numeric_value
                    FROM timeseries.time_series_value a
                    WHERE a.time_series_id = (
                        SELECT b.id 
                        FROM timeseries.level_relation_view b
                        WHERE category = :category
                        AND geography_from = :geo
                        {cond}
                    )
                    AND date_time_value >= :dt_from 
                    AND {end_date_condition}
                )
            """
        if update:
            query = base_query + """
                SELECT a.* 
                FROM data_pfc a, max_tick b
                WHERE a.date_time_tick = b.date_time_tick
            """
        else:
            query = base_query + """
                ,dw_prices AS (
                    SELECT DT.date_hour_minute_start
                    FROM trading_dw.prices P
                    INNER JOIN trading_dw.dim_time DT ON DT.id = P.time_id
                    WHERE P.price_curve_id = (
                        SELECT id 
                        FROM trading_dw.price_curve pc 
                        WHERE pc.price_curve_name = :name
                    )
                    AND DT.date_hour_minute_end >= :dt_from 
                    AND DT.date_hour_minute_end <= :dt_to
                )
                SELECT a.* 
                FROM data_pfc a, max_tick b
                WHERE a.date_time_tick = b.date_time_tick
                AND a.date_time_value NOT IN (
                    SELECT date_hour_minute_start 
                    FROM dw_prices
                )
            """
            params['name'] = name

        # Format query with conditions
        query = query.format(
            cond=cond,
            end_date_condition=end_date_condition
        )

        with Session(database.engine) as session:
            return pd.read_sql(text(query), session.bind, params=params)