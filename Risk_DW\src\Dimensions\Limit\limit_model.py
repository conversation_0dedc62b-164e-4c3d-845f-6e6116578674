from sqlalchemy import Column, Integer, Numeric, String
from setup_global import SetupGlobal
from libs.mixin import AuditMixin

class LimitModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'limit'
    id = Column(Integer, primary_key=True)
    name = Column(String(50))
    limit_group_id = Column(Integer)
    limit_type_id = Column(Integer)
    subject_id = Column(Integer)
    product_type_id = Column(Integer)
    time_period = Column(String(50))
    area = Column(String(10))
    unit = Column(String(10))
    limit_value = Column(Numeric(18, 2))


    def __init__(self,
                 name,
                 limit_group_id,
                 limit_type_id,
                 subject_id,
                 time_period,
                 area,
                 unit,
                 limit_value,
                 product_type_id=None):
        self.name = name
        self.limit_group_id = limit_group_id
        self.limit_type_id = limit_type_id
        self.subject_id = subject_id
        self.product_type_id = product_type_id
        self.time_period = time_period
        self.area = area
        self.unit = unit
        self.limit_value = limit_value

