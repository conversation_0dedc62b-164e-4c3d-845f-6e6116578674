from sqlalchemy import  Column, Integer, String, Text
from setup_global import SetupGlobal
from libs.mixin import AuditMixin

class ProductModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'product'
    id = Column(Integer, primary_key=True)
    name = Column(String(100))
    product_type_id = Column(Integer)
    load_profile_id = Column(Integer)
    market_type_id = Column(Integer)
    commodity_id = Column(Integer)
    trading_period_id = Column(Integer)
    por = Column(String(50))
    pod = Column(String(50))



    def __init__(self,
                name,
                product_type_id,
                load_profile_id,
                market_type_id,
                commodity_id,
                trading_period_id):

        self.name = name
        self.product_type_id = product_type_id
        self.load_profile_id = load_profile_id
        self.market_type_id = market_type_id
        self.commodity_id = commodity_id
        self.trading_period_id = trading_period_id
