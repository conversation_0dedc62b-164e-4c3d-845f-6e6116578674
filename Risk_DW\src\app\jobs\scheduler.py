import schedule
import time
from app.jobs.data_imports import import_prices, import_trades, update_prices
from app.jobs.realization_calculations import calculate_company_realization, calculate_realization_cross
from app.jobs.realization_calculations import calculate_realization_for_book, calculate_realization_for_portfolio, calculate_realization_for_trader
from app.jobs.mtm_calculations import calculate_mtm_for_company, calculate_mtm_for_trader, calculate_mtm_for_book
from app.jobs.calc_history import calculation_history
from Config.ConfigReader import Config<PERSON>eader

def run_scheduled_tasks(container):
    scheduler_config = ConfigReader.get_config_section(container.config.get("config_name"), "scheduler")

    schedule.every().day.at(scheduler_config['update_prices']).do(update_prices, container=container)
    schedule.every().day.at(scheduler_config['realizations']).do(calculate_company_realization, container=container)
    schedule.every().day.at(scheduler_config['realizations']).do(calculate_realization_cross, container=container)
    schedule.every().day.at(scheduler_config['realizations']).do(calculate_realization_for_book, container=container)
    schedule.every().day.at(scheduler_config['realizations']).do(calculate_realization_for_trader, container=container)
    # schedule.every().day.at(scheduler_config['realizations']).do(calculate_realization_for_portfolio, container=container)

    schedule.every().day.at(scheduler_config['mtms']).do(calculate_mtm_for_company, container=container)
    schedule.every().day.at(scheduler_config['mtms']).do(calculate_mtm_for_trader, container=container)
    schedule.every().day.at(scheduler_config['mtms']).do(calculate_mtm_for_book, container=container)

    schedule.every().day.at(scheduler_config['history']).do(calculation_history, container=container)

    schedule.every().hour.do(import_trades, container=container)

    while True:
        schedule.run_pending()
        time.sleep(1)
