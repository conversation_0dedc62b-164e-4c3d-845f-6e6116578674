from ksLib.api.NotificationsAPI import NotificationsAP<PERSON>, NotificationEmail, NotificationEmailRecipient
from decimal import Decimal
import logging

logger = logging.getLogger(__name__)

class MailService:

    def send_mail(self, subject, body, receipiants):
        logging.info("Sending mail.")

        try:
            for receipiant in receipiants:
                email = NotificationEmail(
                to=[NotificationEmailRecipient(address=receipiant)],
                subject=subject,
                text_body=body
                )
                api = NotificationsAPI()
                api.send_email(email)

        except Exception as e:
            logging.error(f"Error in processing mail: {e}")


