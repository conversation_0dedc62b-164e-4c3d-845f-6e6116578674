from sqlalchemy import  Column, Integer, String, Text
from setup_global import SetupGlobal
from libs.mixin import AuditMixin

class MarketTypeModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'market_type'
    id = Column(Integer, primary_key=True)
    code = Column(String(10))
    name = Column(String(50))
    description = Column(Text)


    def __init__(self,
                code,
                name,
                description):

        self.code = code
        self.name = name
        self.description = description
