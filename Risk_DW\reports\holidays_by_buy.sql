SELECT
  SUM(VALUE) AS buy,
  'is_work_free_day' AS holiday_status
FROM
  trading_dw.report_trades rt
  INNER JOIN trading_dw.country c ON c.country_code2 = rt.country_code
  INNER JOIN trading_dw.holiday h ON rt."date" = h."date"
  AND c.id = h.country_id
WHERE
  position_type = 'Buy'
  AND h.is_work_free_day = TRUE
UNION
SELECT
  SUM(VALUE) AS buy,
  'is_pre_work_free_day' AS holiday_status
FROM
  trading_dw.report_trades rt
  INNER JOIN trading_dw.country c ON c.country_code2 = rt.country_code
  INNER JOIN trading_dw.holiday h ON rt."date" = h."date"
  AND c.id = h.country_id
WHERE
  position_type = 'Buy'
  AND h.is_pre_work_free_day = TRUE
UNION
SELECT
  SUM(VALUE) AS buy,
  'is_post_work_free_day' AS holiday_status
FROM
  trading_dw.report_trades rt
  INNER JOIN trading_dw.country c ON c.country_code2 = rt.country_code
  INNER JOIN trading_dw.holiday h ON rt."date" = h."date"
  AND c.id = h.country_id
WHERE
  position_type = 'Buy'
  AND h.is_post_work_free_day = TRUE
UNION
SELECT
  SUM(VALUE) AS buy,
  'is_bridge' AS holiday_status
FROM
  trading_dw.report_trades rt
  INNER JOIN trading_dw.country c ON c.country_code2 = rt.country_code
  INNER JOIN trading_dw.holiday h ON rt."date" = h."date"
  AND c.id = h.country_id
WHERE
  position_type = 'Buy'
  AND h.is_bridge = TRUE
UNION
SELECT
  SUM(VALUE) AS buy,
  'ordinary_day' AS holiday_status
FROM
  trading_dw.report_trades rt
  INNER JOIN trading_dw.country c ON c.country_code2 = rt.country_code
  INNER JOIN trading_dw.holiday h ON rt."date" = h."date"
  AND c.id = h.country_id
WHERE
  position_type = 'Buy'
  AND h.is_work_free_day = FALSE
  AND h.is_pre_work_free_day = FALSE
  AND h.is_post_work_free_day = FALSE
  AND h.is_bridge = FALSE