# Use a slim Debian Bullseye image with Python 3.11
FROM python:3.13-slim-bullseye

# Set metadata for the image
LABEL maintainer="<PERSON><PERSON><PERSON> <dusan.k<PERSON><PERSON><PERSON>@kolektor.com>"

# Copy pip configuration file to the container
COPY ./pip.ini /etc/pip.conf

# Set environment variables to control Python behavior
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    USER=app \
    ENVIRONMENT=DEV

ENV HOME=/home/<USER>
    PATH=/usr/local/bin:$PATH \
    RUN_MAIN=true

# Create a non-root user and set permissions for the home directory
RUN adduser --uid 2000 --gecos "" --disabled-password $USER \
    && mkdir -p $HOME/src \
    && chown -R $USER:$USER $HOME

# Install system dependencies and clean up
RUN apt-get update \
    && apt-get install -y --no-install-recommends gcc libpq-dev wget \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Download and install Azure CLI using wget
#RUN wget -qO- https://aka.ms/InstallAzureCLIDeb | bash

# Switch to non-root user and set working directory
USER $USER
WORKDIR $HOME/src

COPY ./src .

RUN python -m pip install --upgrade pip \
    && pip install -U --no-cache-dir --no-warn-script-location -r ./requirements.txt

EXPOSE 8125

CMD ["python", "./main.py"]