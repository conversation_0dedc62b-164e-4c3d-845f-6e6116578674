WITH
  sum_buy AS (
    SELECT
      SUM(VALUE) AS VALUE,
      date
    FROM
      trading_dw.report_trades
    WHERE
      position_type = 'Buy'
    GROUP BY
      date
  ),
  sum_sell AS (
    SELECT
      -1 * SUM(VALUE) AS VALUE,
      date
    FROM
      trading_dw.report_trades
    WHERE
      position_type = 'Sell'
    GROUP BY
      date
  )
SELECT
  VALUE,
  date
FROM
  sum_buy
UNION
SELECT
  VALUE,
  date
FROM
  sum_sell