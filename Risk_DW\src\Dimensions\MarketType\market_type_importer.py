from pyexcel_io.database.querysets import QuerysetsReader
from libs.setup_orm_utility import SetupOrmUtility
from Dimensions.MarketType.market_type_model import MarketTypeModel

import logging

logger = logging.getLogger(__name__)

class MarketTypeImporter():

    def __init__(self, config_name):
        self.setup_orm_utility = SetupOrmUtility(config_name)


        query_sets = self.setup_orm_utility.codes_session.query(MarketTypeModel).all()
        reader = QuerysetsReader(query_sets, ["name"])
        self.existing_market_types = list(reader.to_array())

    def import_data(self):
        self.rows_inserted = 0

        logger.info('Import started')

        self.setup_orm_utility.import_data(self.market_type_init_func, 'market_type', MarketTypeModel)

        logger.info('Import ended. Inserted %s rows', self.rows_inserted)

    def market_type_init_func(self,row):

        if self.setup_orm_utility.key_exists (self.existing_market_types, row['name']):
            return None

        self.rows_inserted += 1

        return MarketTypeModel(
            row['code'],
            row['name'],
            row['description'])
