from Facts.MTM.mtm_book_model import MtmBookModel
from typing import List, Dict
from Facts.MTM.mtm_service_queries import Queries
from Facts.MTM.base_mtm import BaseMTM

class BookMTM(BaseMTM):
    def _get_mtm_type(self) -> str:
        return "book"

    def _get_mtm_model(self):
        return MtmBookModel

    def _get_buys(self) -> List[Dict]:
        raw_results = self._execute_query(Queries.open_buys_book())
        return self._convert_decimal_values(raw_results)

    def _get_sells(self) -> List[Dict]:
        raw_results = self._execute_query(Queries.open_sells_book())
        return self._convert_decimal_values(raw_results)

    def _create_new_mtm(self, trade: dict, mtm: float, pfc: dict) -> dict:
        base_mtm = self._create_base_mtm(trade, mtm, pfc)
        base_mtm["book_id"] = trade["book_id"]
        return base_mtm