from sqlalchemy import  Column, Integer, Numeric, String, Text, Boolean
from setup_global import SetupGlobal
from libs.mixin import AuditMixin

class PortfolioModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'portfolio'
    id = Column(Integer, primary_key=True)
    portfolio_code = Column(String(50))
    portfolio_name = Column(String(100))
    portfolio_type = Column(String(50))
    risk_profile = Column(String(50))
    parent_portfolio_id = Column(Integer)
    business_unit = Column(String(100))
    strategy =Column(String(100))
    currency_id = Column(Integer)
    is_active = Column(Boolean)
    var_limit = Column(Numeric(18,2))
    position_limit = Column(Numeric(18,2))
    description = Column(Text)

    def __init__ (self,
                portfolio_code,
                portfolio_name,
                portfolio_type,
                risk_profile,
                business_unit,
                strategy,
                currency_id,
                is_active,
                var_limit,
                position_limit,
                description,
                parent_portfolio_id = None):

        self.portfolio_code = portfolio_code
        self.portfolio_name = portfolio_name
        self.portfolio_type = portfolio_type
        self.risk_profile = risk_profile
        self.parent_portfolio_id = parent_portfolio_id
        self.business_unit = business_unit
        self.strategy = strategy
        self.currency_id = currency_id
        self.is_active = is_active
        self.var_limit = var_limit
        self.position_limit = position_limit
        self.description = description
