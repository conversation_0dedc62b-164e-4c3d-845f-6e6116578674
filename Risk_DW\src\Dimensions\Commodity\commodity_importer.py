from pyexcel_io.database.querysets import QuerysetsReader
from libs.setup_orm_utility import SetupOrmUtility
from Dimensions.Commodity.commodity_model import CommodityModel
from Dimensions.Currency.currency_model import CurrencyModel

import logging

logger = logging.getLogger(__name__)

class CommodityImporter():

    def __init__(self, config_name):
        self.setup_orm_utility = SetupOrmUtility(config_name)


        query_sets = self.setup_orm_utility.codes_session.query(CommodityModel).all()
        reader = QuerysetsReader(query_sets, ["name"])
        self.existing_commodities = list(reader.to_array())

    def import_data(self):
        self.rows_inserted = 0

        logger.info('Import started')

        self.setup_orm_utility.import_data(self.commodity_init_func, 'commodity', CommodityModel)

        logger.info('Import ended. Inserted %s rows', self.rows_inserted)

    def commodity_init_func(self,row):

        if self.setup_orm_utility.key_exists (self.existing_commodities, row['name']):
            return None

        currency = self.setup_orm_utility.codes_session.query(CurrencyModel).filter_by(currency_code=row['currency_id']).first()
        if currency is None:
            logger.error('Missing currency code "%s"', row['currency_id'])
            return None

        self.rows_inserted += 1

        return CommodityModel(
            row['name'],
            row['type'],
            row['description'],
            row['unit'],
            currency.id)
