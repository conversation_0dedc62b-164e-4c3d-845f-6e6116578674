from sqlalchemy import  Column, Integer, String
from setup_global import SetupGlobal
from libs.mixin import AuditMixin

class TradingPeriodModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'trading_period'
    id = Column(Integer, primary_key=True)
    name = Column(String(50))
    description = Column(String(255))
    resolution_seconds = Column(Integer)


    def __init__(self,
                name,
                description,
                resolution_seconds):

        self.name = name
        self.description = description
        self.resolution_seconds = resolution_seconds
