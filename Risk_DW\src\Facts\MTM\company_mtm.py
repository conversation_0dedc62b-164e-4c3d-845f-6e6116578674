from Facts.MTM.mtm_company_model import MtmCompanyModel
from typing import List, Dict
from Facts.MTM.mtm_service_queries import Queries
from Facts.MTM.base_mtm import BaseMTM

class CompanyMTM(BaseMTM):
    def _get_mtm_type(self) -> str:
        return "company"

    def _get_mtm_model(self):
        return MtmCompanyModel

    def _get_buys(self) -> List[Dict]:
        raw_results = self._execute_query(Queries.open_buys())
        return self._convert_decimal_values(raw_results)

    def _get_sells(self) -> List[Dict]:
        raw_results = self._execute_query(Queries.open_sells())
        return self._convert_decimal_values(raw_results)

    def _create_new_mtm(self, trade: dict, mtm: float, pfc: dict) -> dict:
        return self._create_base_mtm(trade, mtm, pfc)