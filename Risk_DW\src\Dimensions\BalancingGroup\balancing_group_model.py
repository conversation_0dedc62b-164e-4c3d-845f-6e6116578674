from sqlalchemy import Column, Integer, String
from sqlalchemy import Foreign<PERSON>ey
from setup_global import SetupGlobal
from libs.mixin import AuditMixin


class BalancingGroupModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'balancing_group'
    id = Column(Integer, primary_key=True)
    balancing_group_name = Column(String(100))
    balancing_group_eic = Column(String(50))
    balancing_subgroup_name = Column(String(255))
    balancing_subgroup_eic = Column(String(50))
    control_area_id = Column(Integer, ForeignKey('control_area.id'))



    def __init__(self,
            balancing_group_name,
            balancing_group_eic,
            balancing_subgroup_name,
            balancing_subgroup_eic,
            control_area_id):

        self.balancing_group_name = balancing_group_name
        self.balancing_group_eic = balancing_group_eic
        self.balancing_subgroup_name = balancing_subgroup_name
        self.balancing_subgroup_eic = balancing_subgroup_eic
        self.control_area_id = control_area_id
