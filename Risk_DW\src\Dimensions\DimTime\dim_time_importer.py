from Dimensions.DimTime.dim_time_model import DimTimeModel
from libs.setup_orm_utility import SetupOrmUtility
from libs.setup_date_utility import SetupDateUtility
from sqlalchemy import func
from datetime import  timedelta
from datetime import datetime

import logging

logger = logging.getLogger(__name__)

class DimTimeImporter():

    def __init__(self, config_name):
        self.setup_orm_utility = SetupOrmUtility(config_name)


    def import_data(self, start_date, end_date):
        self.rows_inserted = 0

        logger.info('Import started')

        current_end_date = self.setup_orm_utility.codes_session.query(func.max(DimTimeModel.date)).scalar()

        if current_end_date is not None:
            start_date = datetime.combine(current_end_date + timedelta(days=1), datetime.min.time())

        dates = self._get_data(start_date, end_date)
        self.setup_orm_utility.import_data(self.dim_time_init_func, 'dim_time',DimTimeModel, dates)

        logger.info('Import ended. Inserted %s rows', self.rows_inserted)

    def dim_time_init_func(self,row):

        self.rows_inserted += 1

        return DimTimeModel(
            row['date'],
            row['hour_interval'],
            row['date_hour_start'],
            row['date_hour_end'],
            row['date_hour_minute_start'],
            row['date_hour_minute_end'],
            row['date_hour_minute_start_end'],
            row['hour_minute_start'],
            row['hour_minute_end'],
            row['hour_minute_start_end'],
            row['minute_start'],
            row['minute_end'],
            row['minute_start_end'],
            row['year'],
            row['half_year'],
            row['half_year_number'],
            row['quarter'],
            row['quarter_number'],
            row['month'],
            row['month_number'],
            row['month_name'],
            row['week_iso'],
            row['week_eex'],
            row['day_of_month'],
            row['day_of_week'],
            row['is_weekend']
        )

    def _get_data(self, start_date, end_date):

        result  = [
            [
                'date',
                'hour_interval',
                'date_hour_start',
                'date_hour_end',
                'date_hour_minute_start',
                'date_hour_minute_end',
                'date_hour_minute_start_end',
                'hour_minute_start',
                'hour_minute_end',
                'hour_minute_start_end',
                'minute_start',
                'minute_end',
                'minute_start_end',
                'year',
                'half_year',
                'half_year_number',
                'quarter',
                'quarter_number',
                'month',
                'month_number',
                'month_name',
                'week_iso',
                'week_eex',
                'day_of_month',
                'day_of_week',
                'is_weekend']
            ]

        date_util = SetupDateUtility(start_date)

        while date_util.current_date < end_date:
            result.append(
                [
                date_util.current_date,
                date_util.current_date.hour + 1,
                date_util.get_date_hour_start(),
                date_util.get_date_hour_end(),
                date_util.get_date_hour_minute_start(),
                date_util.get_date_hour_minute_end(),
                date_util.get_date_hour_minute_start_end(),
                date_util.get_hour_minute_start(),
                date_util.get_hour_minute_end(),
                date_util.get_hour_minute_start_end(),
                date_util.get_minute_start(),
                date_util.get_minute_end(),
                date_util.get_minute_start_end(),
                date_util.current_date.year,
                date_util.get_half_year(),
                date_util.get_half_year_number(),
                date_util.get_quarter(),
                date_util.get_quarter_number(),
                date_util.get_month(),
                date_util.get_month_number(),
                date_util.get_month_name(),
                date_util.get_iso_week(),
                date_util.get_eex_week(),
                date_util.current_date.day,
                date_util.current_date.weekday() + 1,
                date_util.is_weekend()
            ])
            date_util.increment_minutes(15)

        return {'dim_time' : result}
