class Queries:
    @staticmethod
    def calc():
        query = f"""
        with calc as
        (
        select
        CASE
            WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
                (SELECT x.id
                FROM trading_dw.dim_date x
                WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
            ELSE
                (SELECT x.id
                FROM trading_dw.dim_date x
                WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
        END AS calculation_date_id,
        1 as calculation_type_id,
        4 as calculation_code_id,
        2 as object_id,
        coalesce (sum(realization) , 0) as value
        from trading_dw.realization_company rc
        union all
        select
        CASE
            WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
                (SELECT x.id
                FROM trading_dw.dim_date x
                WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
            ELSE
                (SELECT x.id
                FROM trading_dw.dim_date x
                WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
        END AS calculation_date_id,
        1 as calculation_type_id,
        5 as calculation_code_id,
        rc.trader_id as object_id,
        coalesce (sum(realization) , 0) as value
        from trading_dw.realization_trader rc
        group by rc.trader_id
        union all
        select
        CASE
            WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
                (SELECT x.id
                FROM trading_dw.dim_date x
                WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
            ELSE
                (SELECT x.id
                FROM trading_dw.dim_date x
                WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
        END AS calculation_date_id,
        1 as calculation_type_id,
        6 as calculation_code_id,
        rc.book_id as object_id,
        coalesce (sum(realization) , 0) as value
        from trading_dw.realization_book rc
        group by rc.book_id
        union all
        select
        CASE
            WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
                (SELECT x.id
                FROM trading_dw.dim_date x
                WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
            ELSE
                (SELECT x.id
                FROM trading_dw.dim_date x
                WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
        END AS calculation_date_id,
        1 as calculation_type_id,
        7 as calculation_code_id,
        rp.portfolio_id as object_id,
        coalesce (sum(realization) , 0) as value
        from trading_dw.realization_portfolio rp
        group by rp.portfolio_id
        union all
        select
        CASE
            WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
                (SELECT x.id
                FROM trading_dw.dim_date x
                WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
            ELSE
                (SELECT x.id
                FROM trading_dw.dim_date x
                WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
        END AS calculation_date_id,
        2 as calculation_type_id,
        4 as calculation_code_id,
        2 as object_id,
        coalesce (sum(mtm) , 0) as value
        from trading_dw.mtm_company mc where mc.calculation_date_id = (CASE
                                                                            WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
                                                                                (SELECT x.id
                                                                                FROM trading_dw.dim_date x
                                                                                WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
                                                                            ELSE
                                                                                (SELECT x.id
                                                                                FROM trading_dw.dim_date x
                                                                                WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
                                                                        END
                                                                        )
        union all
        select
        CASE
            WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
                (SELECT x.id
                FROM trading_dw.dim_date x
                WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
            ELSE
                (SELECT x.id
                FROM trading_dw.dim_date x
                WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
        END AS calculation_date_id,
        2 as calculation_type_id,
        5 as calculation_code_id,
        mc.trader_id as object_id,
        coalesce (sum(mtm), 0) as value
        from trading_dw.mtm_trader mc where mc.calculation_date_id = (CASE
                                                                            WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
                                                                                (SELECT x.id
                                                                                FROM trading_dw.dim_date x
                                                                                WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
                                                                            ELSE
                                                                                (SELECT x.id
                                                                                FROM trading_dw.dim_date x
                                                                                WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
                                                                        END
                                                                        )
        group by mc.trader_id
        union all
        select
        CASE
            WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
                (SELECT x.id
                FROM trading_dw.dim_date x
                WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
            ELSE
                (SELECT x.id
                FROM trading_dw.dim_date x
                WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
        END AS calculation_date_id,
        2 as calculation_type_id,
        6 as calculation_code_id,
        mc.book_id as object_id,
        coalesce (sum(mtm) , 0) as value
        from trading_dw.mtm_book mc where mc.calculation_date_id = (CASE
                                                                            WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
                                                                                (SELECT x.id
                                                                                FROM trading_dw.dim_date x
                                                                                WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
                                                                            ELSE
                                                                                (SELECT x.id
                                                                                FROM trading_dw.dim_date x
                                                                                WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
                                                                        END
                                                                        )
        group by mc.book_id
        union all
        select
        CASE
            WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
                (SELECT x.id
                FROM trading_dw.dim_date x
                WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
            ELSE
                (SELECT x.id
                FROM trading_dw.dim_date x
                WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
        END AS calculation_date_id,
        2 as calculation_type_id,
        7 as calculation_code_id,
        mc.portfolio_id as object_id,
        coalesce (sum(mtm) , 0) as value
        from trading_dw.mtm_portfolio mc where mc.calculation_date_id = (CASE
                                                                            WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
                                                                                (SELECT x.id
                                                                                FROM trading_dw.dim_date x
                                                                                WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
                                                                            ELSE
                                                                                (SELECT x.id
                                                                                FROM trading_dw.dim_date x
                                                                                WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
                                                                        END
                                                                        )
        group by mc.portfolio_id
        )
        select * from calc
        union
        select calculation_date_id, 3 as calculation_type_id, calculation_code_id, object_id, sum(value) from calc
        group by calculation_date_id, calculation_code_id, object_id
        order by 2, 3
        """
        return query

    @staticmethod
    def pnl():
        query = f"""
            select round(value,2) as pnl from trading_dw.calculation_history a
            where a.calculation_type_id = 3
            and a.calculation_code_id = 4
            and a.object_id = 2
            and a.calculation_date_id = (select max(b.calculation_date_id) from trading_dw.calculation_history b)
        """
        return query
