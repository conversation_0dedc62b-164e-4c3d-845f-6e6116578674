# Import dimenzij

* Skopiraj datoteko z šifranti v trading_dw_data direktorij:
trading_dw_data/trading_dw_data.xlsx<br>
* zaženi import_dimensions()
* v bazo so prenešeni samo novi zapisi. Update zapisev trenutno ni implementiran

# Scheduler servisi

Sintaksa za dodajanje novega servisa, ki se proži periodično je:
```schedule.every().day.at(scheduler_config['import_prices']).do(run_threaded, import_prices, 100)```<br><br>
Interval se nastavi poljubno, v kodi so primeri kako se določajo intervali ob določeni uri<br>

Onemogočen je zagon servisa v primeru če teče še stara instanca.


## import_prices()
Servis ki prenese cene iz timeseries baze v lokalno trading_dw.prices tabelo. Prenos je narejen v batchu in ne po posameznih cenah

## calculate_realization()
Servis ki sprocesira realizacijo

## update_prices()
Servis ki posodobi lokalne cene s cenami v timeseries bazi

## import_trades()
Servis ki prenese posle iz Blue Traderja.<br>
V bazo se zapiše kot bulk insert.<br>
Vsi datumski podatki iz BT se morajo nastavit na CET.<br>

## processes for calculation
Primer:
Dan obdelave: 23.01.2025
Calculation_date: 22.01.2025
PFC date: 22.01.2025
Trade-i v obdelavi: vsi, ki so MANJŠI ALI ENAKi 23.01.2025 00:00:00 - transaction date v BT
 
danes smo 24.1 in danes delamo:
POROČILO za kateri datum: 24.01.2025
uporabljamo PFC-je katerega datuma: 23.01.2025
do katerega datuma (vključno) morajo biti posli zajeti: vsi, ki so MANJŠI ALI ENAKi 24.01.2025 00:00:00
 
caffeinate -i python src/app/jobs/tests/test.py