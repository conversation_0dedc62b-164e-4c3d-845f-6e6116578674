from Dimensions.ControlArea.control_area_model import ControlAreaModel
from pyexcel_io.database.querysets import QuerysetsReader
from libs.setup_orm_utility import SetupOrmUtility

import logging

logger = logging.getLogger(__name__)

class ControlAreaImporter():

    def __init__(self, config_name):
        self.setup_orm_utility = SetupOrmUtility(config_name)
        query_sets = self.setup_orm_utility.codes_session.query(ControlAreaModel).all()


        reader = QuerysetsReader(query_sets, ["control_area_name"])
        self.existing_control_areas = list(reader.to_array())

    def import_data(self):
        self.rows_inserted = 0

        logger.info('Import started')

        self.setup_orm_utility.import_data(self.control_area_init_func, 'control_area',ControlAreaModel)

        logger.info('Import ended. Inserted %s rows', self.rows_inserted)

    def control_area_init_func(self,row):

        if self.setup_orm_utility.key_exists (self.existing_control_areas, row['control_area_name']):
            return None

        self.rows_inserted += 1

        return ControlAreaModel(row['country_name'],
                        row['control_area_name'],
                        row['zone_id'],
                        row['zone_name'],
                        row['reference_zone_rt'],
                        row['default_price_curve'])