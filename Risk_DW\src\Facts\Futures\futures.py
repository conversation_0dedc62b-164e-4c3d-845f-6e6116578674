import decimal

from Dimensions.PriceCurve.price_curve_model import PriceCurveModel
from Facts.Trade.trade_model import TradeModel
from Facts.TradePrices.trade_prices_model import TradePricesModel
from libs.dw_utility import DwUtility
from sqlalchemy import text, update
from datetime import timedelta
import logging
import numpy as np
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)


class Futures:

    def __init__(self, config_name, database):
        self.config_name = config_name

        self.database = database

    def execute(self):
        logger.info('Futures processing started')

        logger.info('Saving previous day mtm for trading prices')

        update_trade_prices_mtm_previous_day_query = '''
                                            UPDATE
                                                trading_dw.trade_prices tp
                                            SET
                                                mtm_previous_day = tp.mtm
                                        '''

        with self.database.engine.connect() as conn:
            conn.execute(text(update_trade_prices_mtm_previous_day_query))
            conn.commit()

        for price_curve_name in self._get_pfc_price_curves():
            futures = self._get_futures(price_curve_name=price_curve_name)

            if len(futures) > 0:
                current_future_cnt = 0
                trade_price_updates = []
                trade_updates = []

                pfc_prices = self._get_pfc_prices(
                    price_curve_name=price_curve_name,
                    min_interval=min(futures, key=lambda f: f[0])[0].date(),
                    max_interval=max(futures, key=lambda f: f[0])[0].date() + timedelta(days=1))

                for future in futures:
                    # pfc prices are hourly, round the filter to the nearest hour
                    pfc_date_time_filter = future.date_hour_minute_start.replace(second=0, minute=0)

                    hpfc_price = next((x for x in pfc_prices if x.date_hour_minute_start == pfc_date_time_filter),
                                      None)

                    if hpfc_price is None:
                        logger.warning('PFC price for "%s", "%s" not found. Skipping...', pfc_date_time_filter,
                                       price_curve_name)
                        continue

                    recalculated_hpfc_price = 0
                    if future.date_hour_minute_start.minute > 0 or future.date_hour_minute_start.second > 0:
                        next_hpfc_price = next((x for x in pfc_prices if
                                                x.date_hour_minute_start == hpfc_price.date_hour_minute_start + timedelta(
                                                    hours=1)),
                                               None)

                        if next_hpfc_price is None:
                            logger.warning('PFC price for "%s", "%s" not found. Skipping...',
                                           hpfc_price.date_hour_minute_start + timedelta(hours=1),
                                           price_curve_name)
                            continue

                        recalculated_hpfc_price = float(np.interp(int(future.date_hour_minute_start.minute / 15),
                                                                  np.array([0, 4]),
                                                                  np.array([float(hpfc_price.price),
                                                                            float(next_hpfc_price.price)])))

                    mtm = DwUtility.get_mtm(
                        price=float(future.tp_price),
                        position_type=future.position_type,
                        market_price=float(hpfc_price.price),
                        quantity=float(future.quantity)
                    )

                    trade_price_updates.append(
                        {
                            "id": future.trade_price_id,
                            "market_value": (
                                                recalculated_hpfc_price if recalculated_hpfc_price > 0 else float(
                                                    hpfc_price.price)) * float(future.quantity),
                            "mtm": mtm,
                            "mtm_previous_day": float(future.mtm)
                        }
                    )

                    if not any(t['identification_description'] == future.identification_description for t in
                               trade_updates):
                        trade_updates.append(
                            {
                                "identification_description": future.identification_description,
                                "reserved_flag": True
                            }
                        )

                    logger.info('Updating mtm for trade price %s: %s -> %s', future.identification_description,
                                future.mtm, mtm)

                    if current_future_cnt % 100 == 0 and current_future_cnt > 0:
                        logger.info('%s futures inserted into update list', current_future_cnt)

                    current_future_cnt += 1

                if len(trade_price_updates) > 0:
                    with Session(self.database.engine) as session:
                        logger.info('Updating trade prices started...')
                        session.execute(update(TradePricesModel), trade_price_updates)

                        logger.info('Updating trades started...')
                        session.execute(update(TradeModel), trade_updates)

                        logger.info('Commiting changes to DW...')
                        session.commit()
                else:
                    logger.info('No "%s" futures to update...', price_curve_name)

            else:
                logger.info('Skipping futures for price curve "%s". No records for update...', price_curve_name)

        self._update_trade_mtm()

    def _get_pfc_price_curves(self):

        with Session(self.database.engine) as session:
            price_curves = session.query(PriceCurveModel).all()

        return [
            price_curve.price_curve_name
            for price_curve in price_curves
            if price_curve.price_curve_name.startswith('HPFC ')
        ]

    def _get_futures(self, price_curve_name):
        logger.info('Start fetching "%s" futures...', price_curve_name)

        get_futures_query = f"""
            SELECT
                start_interval_time.date_hour_minute_start,
                p.id AS price_id,
                tp.id AS trade_price_id,
                p.price AS pfc_price,
                tp.identification_description,
                tp.quantity,
                tp.value,
                tp.market_value,
                tp.price AS tp_price,
                tp.mtm,
                t.position_type
            FROM
                trading_dw.trade_prices tp
            INNER JOIN
                trading_dw.trade t ON t.identification_description = tp.identification_description
            INNER JOIN
                trading_dw.prices p ON tp.market_price_id = p.id
            INNER JOIN
                trading_dw.dim_date delivery_day ON tp.delivery_day_id = delivery_day.id
            INNER JOIN
                trading_dw.trading_interval ti ON ti.id = tp.trading_interval_id
            INNER JOIN
                trading_dw.dim_time start_interval_time ON ti.start_time_id = start_interval_time.id
            INNER JOIN
                trading_dw.price_curve pc on pc.id = p.price_curve_id
            WHERE
                pc.price_curve_name = '{price_curve_name}' AND start_interval_time.date_hour_minute_start > NOW()
        """

        with self.database.engine.connect() as conn:
            return conn.execute(text(get_futures_query)).all()

    def _get_pfc_prices(self, price_curve_name, min_interval, max_interval):
        logger.info('Start fetching "%s" pfc prices...', price_curve_name)

        country = price_curve_name.replace('HPFC_', 'HPFC ')
        get_prices_query = f"""
            SELECT p.price, dt.date_hour_minute_start
            FROM trading_dw.prices p
            INNER JOIN trading_dw.dim_time dt ON p.time_id = dt.id
            INNER JOIN trading_dw.price_curve pc ON p.price_curve_id = pc.id
            WHERE pc.price_curve_name = '{country}'
            AND dt.date_hour_minute_start >= '{min_interval}'
            AND dt.date_hour_minute_start <= '{max_interval}'
        """
        with self.database.engine.connect() as conn:
            return conn.execute(text(get_prices_query)).all()

    def _update_trade_mtm(self):

        logger.info('Saving previous day mtm for trades')

        update_trade_mtm_previous_day_query = '''
                            UPDATE
                                trading_dw.trade t
                            SET
                                mtm_previous_day = t.mtm
                        '''

        with self.database.engine.connect() as conn:
            conn.execute(text(update_trade_mtm_previous_day_query))
            conn.commit()

        logger.info('Updating mtm for trades')
        update_trade_mtm_query = '''
                    UPDATE trading_dw.trade t
                    SET
                        mtm = tp.mtm,
                        reserved_flag = false
                    FROM
                    (
                        SELECT tp.identification_description, SUM(mtm) as mtm
                        FROM trading_dw.trade_prices tp
                        GROUP BY tp.identification_description
                    ) tp
                    WHERE t.identification_description = tp.identification_description AND t.reserved_flag = true
                '''

        with self.database.engine.connect() as conn:
            conn.execute(text(update_trade_mtm_query))
            conn.commit()
