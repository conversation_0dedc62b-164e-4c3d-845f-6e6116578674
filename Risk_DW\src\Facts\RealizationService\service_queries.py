class Queries:
    @staticmethod
    def open_buys():
        query = f"""
        with unrealized as
	        (
        	select a.identification_description_buy,
        		   a.trading_interval_start_id,
        		   min(a.quantity_reminder_buy) reminder
        	from trading_dw.realization_cross a
        		group by a.identification_description_buy,
        		   	     a.trading_interval_start_id
        		having min(a.quantity_reminder_buy) > 0
	        ),
        	buys as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, b.quantity as quantity_reminder_buy, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Buy'
            and a.trade_date_id = c.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '5 DAY')) - interval '1 hour'
            and not exists (select 1 from trading_dw.realization_cross x
            				where x.identification_description_buy = a.identification_description
            					and x.trading_interval_start_id = d.id)
            ),
            unrealized_buys as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, x.reminder as quantity_reminder_buy, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g,
                unrealized x
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Buy'
            and a.trade_date_id = c.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --        with unrealized as
	        (
        	select a.identification_description_buy,
        		   a.trading_interval_start_id,
        		   min(a.quantity_reminder_buy) reminder
        	from trading_dw.realization_cross a
        		group by a.identification_description_buy,
        		   	     a.trading_interval_start_id
        		having min(a.quantity_reminder_buy) > 0
	        ),
        	buys as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, b.quantity as quantity_reminder_buy, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Buy'
            and a.trade_date_id = c.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '5 DAY')) - interval '1 hour'
            and not exists (select 1 from trading_dw.realization_cross x
            				where x.identification_description_buy = a.identification_description
            					and x.trading_interval_start_id = d.id)
            ),
            unrealized_buys as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, x.reminder as quantity_reminder_buy, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g,
                unrealized x
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Buy'
            and a.trade_date_id = c.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '5 DAY')) - interval '1 hour'
            and x.identification_description_buy = a.identification_description
            and x.trading_interval_start_id = d.id
            )
            select * from buys
            union all
            select * from unrealized_buys
            order by 2, 1, 3, 4
            and x.identification_description_buy = a.identification_description
            and x.trading_interval_start_id = d.id
            )
            select * from buys
            union all
            select * from unrealized_buys
            order by 2, 1, 3, 4
        """
        return query

    @staticmethod
    def open_sells():
        query = f"""
        with unrealized as
	        (
        	select a.identification_description_sell,
        		   a.trading_interval_start_id,
        		   min(a.quantity_reminder_sell) reminder
        	from trading_dw.realization_cross a
        		group by a.identification_description_sell,
        		   	     a.trading_interval_start_id
        		having min(a.quantity_reminder_sell) > 0
	        ),
        	sells as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, b.quantity as quantity_reminder_sell, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Sell'
            and a.trade_date_id = c.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '5 DAY')) - interval '1 hour'
            and not exists (select 1 from trading_dw.realization_cross x
            				where x.identification_description_sell = a.identification_description
            					and x.trading_interval_start_id = d.id)
            ),
            unrealized_sells as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, x.reminder as quantity_reminder_sell, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g,
                unrealized x
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Sell'
            and a.trade_date_id = c.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '5 DAY')) - interval '1 hour'
            and x.identification_description_sell = a.identification_description
            and x.trading_interval_start_id = d.id
            )
            select * from sells
            union all
            select * from unrealized_sells
            order by 2, 1, 3, 4
        """
        return query


#-------------------------KONEC CROSS--------------------------------
    @staticmethod
    def open_buys_trader(trader_id, product_id):
        query = f"""
        with unrealized as
	        (
        	select a.identification_description_buy,
        		   a.trading_interval_start_id,
        		   min(a.quantity_reminder_buy) reminder
        	from trading_dw.realization_trader a
        		where a.product_id_buy = {product_id}
        		group by a.identification_description_buy,
        		   	     a.trading_interval_start_id
        		having min(a.quantity_reminder_buy) > 0
	        ),
        	buys as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, b.quantity as quantity_reminder_buy, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id,
                a.trader_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Buy'
            and a.trade_date_id = c.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '5 DAY')) - interval '1 hour'
            and a.product_id = {product_id}
            and a.trader_id = {trader_id}
            and not exists (select 1 from trading_dw.realization_trader x
            				where x.identification_description_buy = a.identification_description
            					and x.trading_interval_start_id = d.id
                                and x.trader_id = a.trader_id)
            ),
            unrealized_buys as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, x.reminder as quantity_reminder_buy, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id,
                a.trader_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g,
                unrealized x
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Buy'
            and a.trade_date_id = c.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '5 DAY')) - interval '1 hour'
            and a.product_id = {product_id}
            and a.trader_id = {trader_id}
            and x.identification_description_buy = a.identification_description
            and x.trading_interval_start_id = d.id
            )
            select * from buys
            union all
            select * from unrealized_buys
            order by 2, 1, 3, 4
        """
        return query

    @staticmethod
    def open_sells_trader(trader_id, product_id):
        query = f"""
        with unrealized as
	        (
        	select a.identification_description_sell,
        		   a.trading_interval_start_id,
        		   min(a.quantity_reminder_sell) reminder
        	from trading_dw.realization_trader a
        		where a.product_id_buy = {product_id}
        		group by a.identification_description_sell,
        		   	     a.trading_interval_start_id
        		having min(a.quantity_reminder_sell) > 0
	        ),
        	sells as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, b.quantity as quantity_reminder_sell, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id,
                a.trader_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Sell'
            and a.trade_date_id = c.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '5 DAY')) - interval '1 hour'
            and a.product_id = {product_id}
            and a.trader_id = {trader_id}
            and not exists (select 1 from trading_dw.realization_trader x
            				where x.identification_description_sell = a.identification_description
            					and x.trading_interval_start_id = d.id
                                and x.trader_id = a.trader_id)
            ),
            unrealized_sells as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, x.reminder as quantity_reminder_sell, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id,
                a.trader_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g,
                unrealized x
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Sell'
            and a.trade_date_id = c.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '5 DAY')) - interval '1 hour'
            and a.product_id = {product_id}
            and a.trader_id = {trader_id}
            and x.identification_description_sell = a.identification_description
            and x.trading_interval_start_id = d.id
            )
            select * from sells
            union all
            select * from unrealized_sells
            order by 2, 1, 3, 4
        """
        return query

#-------------------------KONEC TRADER--------------------------------

    @staticmethod
    def open_buys_product(product_id):
        query = f"""
        with unrealized as
	        (
        	select a.identification_description_buy,
        		   a.trading_interval_start_id,
        		   min(a.quantity_reminder_buy) reminder
        	from trading_dw.realization_company a
        		where a.product_id_buy = {product_id}
        		group by a.identification_description_buy,
        		   	     a.trading_interval_start_id
        		having min(a.quantity_reminder_buy) > 0
	        ),
        	buys as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, b.quantity as quantity_reminder_buy, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Buy'
            and a.trade_date_id = c.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '5 DAY')) - interval '1 hour'
            and a.product_id = {product_id}
            and not exists (select 1 from trading_dw.realization_company x
            				where x.identification_description_buy = a.identification_description
            					and x.trading_interval_start_id = d.id)
            ),
            unrealized_buys as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, x.reminder as quantity_reminder_buy, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g,
                unrealized x
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Buy'
            and a.trade_date_id = c.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '5 DAY')) - interval '1 hour'
            and a.product_id = {product_id}
            and x.identification_description_buy = a.identification_description
            and x.trading_interval_start_id = d.id
            )
            select * from buys
            union all
            select * from unrealized_buys
            order by 2, 1, 3, 4
        """
        return query

    @staticmethod
    def open_sells_product(product_id):
        query = f"""
        with unrealized as
	        (
        	select a.identification_description_sell,
        		   a.trading_interval_start_id,
        		   min(a.quantity_reminder_sell) reminder
        	from trading_dw.realization_company a
        		where a.product_id_buy = {product_id}
        		group by a.identification_description_sell,
        		   	     a.trading_interval_start_id
        		having min(a.quantity_reminder_sell) > 0
	        ),
        	sells as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, b.quantity as quantity_reminder_sell, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Sell'
            and a.trade_date_id = c.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '5 DAY')) - interval '1 hour'
            and a.product_id = {product_id}
            and not exists (select 1 from trading_dw.realization_company x
            				where x.identification_description_sell = a.identification_description
            					and x.trading_interval_start_id = d.id)
            ),
            unrealized_sells as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, x.reminder as quantity_reminder_sell, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g,
                unrealized x
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Sell'
            and a.trade_date_id = c.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '5 DAY')) - interval '1 hour'
            and a.product_id = {product_id}
            and x.identification_description_sell = a.identification_description
            and x.trading_interval_start_id = d.id
            )
            select * from sells
            union all
            select * from unrealized_sells
            order by 2, 1, 3, 4
        """
        return query

#-------------------------KONEC PRODUCT--------------------------------

    @staticmethod
    def open_buys_portfolio(portfolio_id, product_id):
        query = f"""
        with unrealized as
	        (
        	select a.identification_description_buy,
        		   a.trading_interval_start_id,
        		   min(a.quantity_reminder_buy) reminder
        	from trading_dw.realization_portfolio a
        		where a.product_id_buy = {product_id}
        		group by a.identification_description_buy,
        		   	     a.trading_interval_start_id
        		having min(a.quantity_reminder_buy) > 0
	        ),
        	buys as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, b.quantity as quantity_reminder_buy, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id,
                a.portfolio_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Buy'
            and a.trade_date_id = c.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '5 DAY')) - interval '1 hour'
            and a.product_id = {product_id}
            and a.portfolio_id = {portfolio_id}
            and not exists (select 1 from trading_dw.realization_portfolio x
            				where x.identification_description_buy = a.identification_description
            					and x.trading_interval_start_id = d.id
                                and x.portfolio_id = a.portfolio_id)
            ),
            unrealized_buys as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, x.reminder as quantity_reminder_buy, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id,
                a.portfolio_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g,
                unrealized x
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Buy'
            and a.trade_date_id = c.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '5 DAY')) - interval '1 hour'
            and a.product_id = {product_id}
            and a.portfolio_id = {portfolio_id}
            and x.identification_description_buy = a.identification_description
            and x.trading_interval_start_id = d.id
            )
            select * from buys
            union all
            select * from unrealized_buys
            order by 2, 1, 3, 4
        """
        return query

    @staticmethod
    def open_sells_portfolio(portfolio_id, product_id):
        query = f"""
        with unrealized as
	        (
        	select a.identification_description_sell,
        		   a.trading_interval_start_id,
        		   min(a.quantity_reminder_sell) reminder
        	from trading_dw.realization_portfolio a
        		where a.product_id_buy = {product_id}
        		group by a.identification_description_sell,
        		   	     a.trading_interval_start_id
        		having min(a.quantity_reminder_sell) > 0
	        ),
        	sells as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, b.quantity as quantity_reminder_sell, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id,
                a.portfolio_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Sell'
            and a.trade_date_id = c.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '5 DAY')) - interval '1 hour'
            and a.product_id = {product_id}
            and a.portfolio_id = {portfolio_id}
            and not exists (select 1 from trading_dw.realization_portfolio x
            				where x.identification_description_sell = a.identification_description
            					and x.trading_interval_start_id = d.id
                                and x.portfolio_id = a.portfolio_id)
            ),
            unrealized_sells as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, x.reminder as quantity_reminder_sell, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id,
                a.portfolio_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g,
                unrealized x
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Sell'
            and a.trade_date_id = c.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '5 DAY')) - interval '1 hour'
            and a.product_id = {product_id}
            and a.portfolio_id = {portfolio_id}
            and x.identification_description_sell = a.identification_description
            and x.trading_interval_start_id = d.id
            )
            select * from sells
            union all
            select * from unrealized_sells
            order by 2, 1, 3, 4
        """
        return query


#-------------------------KONEC PORTFOLIO--------------------------------

    @staticmethod
    def open_buys_book(book_id, product_id):
        query = f"""
        with unrealized as
	        (
        	select a.identification_description_buy,
        		   a.trading_interval_start_id,
        		   min(a.quantity_reminder_buy) reminder
        	from trading_dw.realization_book a
        		where a.product_id_buy = {product_id}
        		group by a.identification_description_buy,
        		   	     a.trading_interval_start_id
        		having min(a.quantity_reminder_buy) > 0
	        ),
        	buys as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, b.quantity as quantity_reminder_buy, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id,
                a.trade_book_id as book_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Buy'
            and a.trade_date_id = c.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '5 DAY')) - interval '1 hour'
            and a.product_id = {product_id}
            and a.trade_book_id = {book_id}
            and not exists (select 1 from trading_dw.realization_book x
            				where x.identification_description_buy = a.identification_description
            					and x.trading_interval_start_id = d.id
                                and x.book_id = a.trade_book_id)
            ),
            unrealized_buys as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, x.reminder as quantity_reminder_buy, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id,
                a.trade_book_id as book_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g,
                unrealized x
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Buy'
            and a.trade_date_id = c.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '5 DAY')) - interval '1 hour'
            and a.product_id = {product_id}
            and a.trade_book_id = {book_id}
            and x.identification_description_buy = a.identification_description
            and x.trading_interval_start_id = d.id
            )
            select * from buys
            union all
            select * from unrealized_buys
            order by 2, 1, 3, 4
        """
        return query

    @staticmethod
    def open_sells_book(book_id, product_id):
        query = f"""
        with unrealized as
	        (
        	select a.identification_description_sell,
        		   a.trading_interval_start_id,
        		   min(a.quantity_reminder_sell) reminder
        	from trading_dw.realization_book a
        		where a.product_id_buy = {product_id}
        		group by a.identification_description_sell,
        		   	     a.trading_interval_start_id
        		having min(a.quantity_reminder_sell) > 0
	        ),
        	sells as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, b.quantity as quantity_reminder_sell, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id,
                a.trade_book_id as book_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Sell'
            and a.trade_date_id = c.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '5 DAY')) - interval '1 hour'
            and a.product_id = {product_id}
            and a.trade_book_id = {book_id}
            and not exists (select 1 from trading_dw.realization_book x
            				where x.identification_description_sell = a.identification_description
            					and x.trading_interval_start_id = d.id
                                and x.book_id = a.trade_book_id)
            ),
            unrealized_sells as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, x.reminder as quantity_reminder_sell, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id,
                a.trade_book_id as book_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g,
                unrealized x
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Sell'
            and a.trade_date_id = c.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '5 DAY')) - interval '1 hour'
            and a.product_id = {product_id}
            and a.trade_book_id = {book_id}
            and x.identification_description_sell = a.identification_description
            and x.trading_interval_start_id = d.id
            )
            select * from sells
            union all
            select * from unrealized_sells
            order by 2, 1, 3, 4
        """
        return query


#-------------------------KONEC BOOK--------------------------------
