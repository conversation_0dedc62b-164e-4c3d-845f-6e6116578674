from sqlalchemy import  Column, Integer, String
from setup_global import SetupGlobal
from libs.mixin import AuditMixin

class ControlAreaModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'control_area'
    id = Column(Integer, primary_key=True)
    country_name = Column(String(100))
    control_area_name = Column(String(100))
    zone_id = Column(String(50))
    zone_name = Column(String(100))
    reference_zone_rt = Column(String(100))
    default_price_curve = Column(String(100))


    def __init__(self, country_name,
                control_area_name,
                zone_id,
                zone_name,
                reference_zone_rt,
                default_price_curve):

        self.country_name = country_name
        self.control_area_name = control_area_name
        self.zone_id = zone_id
        self.zone_name = zone_name
        self.reference_zone_rt = reference_zone_rt
        self.default_price_curve = default_price_curve
