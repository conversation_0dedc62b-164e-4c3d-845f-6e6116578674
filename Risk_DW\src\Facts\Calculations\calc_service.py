from sqlalchemy import text
from sqlalchemy.orm import Session
from Facts.Calculations.calculation_history_model import CalculationHistoryModel
from Facts.Calculations.calc_service_queries import Queries
from decimal import Decimal
import logging

logger = logging.getLogger(__name__)

class CalculationHistory:
    def __init__(self, database):
        self.database = database

    def process(self):
        logging.info("Starting calculation history process.")

        try:
            histories = self._get_history()
            if histories:
                self._commit_histories_to_db(histories)
                logging.info("Process completed successfully.")
            else:
                logging.info("No calculation history to process.")
        except Exception as e:
            logging.error(f"Error in processing calculation history: {e}")

    def _commit_histories_to_db(self, histories):
        logging.info("Committing calculation history to the database.")
        with Session(self.database.engine) as session:
            try:
                session.bulk_insert_mappings(CalculationHistoryModel, histories)
                session.commit()
                logging.info("Calculation history committed successfully.")
            except Exception as e:
                logging.error(f"Failed to commit calculation history: {e}")

    def _get_history(self):
        query = Queries.calc()
        logging.debug(f"Executing query: {query}")
        with self.database.engine.connect() as conn:
            try:
                result = conn.execute(text(query))
                columns = result.keys()
                rows = result.fetchall()
                return [
                    {key: float(value) if isinstance(value, Decimal) else value for key, value in zip(columns, row)}
                    for row in rows
                ]
            except Exception as e:
                logging.error(f"Query execution failed: {e}")
                return []

    def get_last_pnl(self):
        query = Queries.pnl()
        logging.debug(f"Executing query: {query}")
        with self.database.engine.connect() as conn:
            try:
                result = conn.execute(text(query))
                columns = result.keys()
                rows = result.fetchall()
                return [
                    {key: float(value) if isinstance(value, Decimal) else value for key, value in zip(columns, row)}
                    for row in rows
                ]
            except Exception as e:
                logging.error(f"Query execution failed: {e}")
                return []
