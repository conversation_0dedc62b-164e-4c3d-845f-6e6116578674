from datetime import timedelta
from Models.pfc_prices_model import PfcPricesModel
import numpy as np
import logging

logger = logging.getLogger(__name__)

class PfcPrices:

    def __init__(self, start_interval_time, end_interval_time, hpfc_prices, resolution_seconds):
        self.start_interval_time = start_interval_time
        self.end_interval_time = end_interval_time
        self.hpfc_prices = hpfc_prices
        self.resolution_seconds = resolution_seconds

    def get_market_price(self):
        prices = []
        slices = self._get_sliced_hours()
        if slices is None:
            return None

        for slice in slices:
            if slice[0].minute == 0 and slice[1].minute == 0:
                hpfc_price = self._get_hpfc_price(slice[0])
                if hpfc_price is None:
                    logger.error('Missing pfc price for slice "%s"', slice[1])
                    return None

                prices.append (
                    PfcPricesModel(
                        id = hpfc_price.id,
                        price = float(hpfc_price.price),
                        start_interval = slice[0],
                        end_interval = slice[1]
                    )
                )

            else:
                start_hour = slice[0].replace(second=0, microsecond=0, minute=0, hour = slice[0].hour)

                hpfc_start_price_record  = self._get_hpfc_price(start_hour)
                hpfc_end_price_record  = self._get_hpfc_price(start_hour + timedelta(hours=1))

                hpfc_price = float(np.interp(int(slice[1].minute / 15),
                                             np.array([0, 4]),
                                             np.array([float(hpfc_start_price_record.price), float(hpfc_end_price_record.price)])))

                prices.append(
                    PfcPricesModel(
                        id = hpfc_start_price_record.id,
                        price = hpfc_price,
                        start_interval = slice[0],
                        end_interval = slice[1]
                    )
                )

        return prices

    def _get_hpfc_price(self, hpfc_prices_date_time):
        prices_record = next((x for x in self.hpfc_prices if x.date_hour_minute_start == hpfc_prices_date_time), None)
        if prices_record is None:
            logger.error('Missing pfc price for "%s"', hpfc_prices_date_time)
            return None

        return prices_record

    def _get_sliced_hours(self):
        slices = []

        interval = self.resolution_seconds / 60

        current_interval_time = self.start_interval_time
        while current_interval_time < self.end_interval_time:

            start_hour = current_interval_time.replace(
                second = 0,
                microsecond = 0,
                minute = current_interval_time.minute,
                hour = current_interval_time.hour)

            end_hour = start_hour + timedelta(minutes = interval)
            slices.append([start_hour, end_hour])
            current_interval_time = end_hour

        slices[0][0] = self.start_interval_time
        slices[len(slices)-1][1] = self.end_interval_time
        return slices