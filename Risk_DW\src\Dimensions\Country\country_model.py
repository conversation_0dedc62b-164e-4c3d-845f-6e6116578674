from sqlalchemy import Column, Integer, String
from setup_global import SetupGlobal
from libs.mixin import AuditMixin

class CountryModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'country'
    id = Column(Integer, primary_key=True)
    country_name = Column(String(100))
    official_country_name = Column(String(100))
    local_country_name = Column(String(100))
    country_name_in_slovene = Column(String(100))
    country_code2 = Column(String(2))
    country_code3 = Column(String(3))
    country_numeric_code = Column(Integer)
    currency_numeric_code = Column(Integer)
    currency_code = Column(String(3))


    def __init__(self,
                country_name,
                official_country_name,
                local_country_name,
                country_name_in_slovene,
                country_code2,
                country_code3,
                country_numeric_code,
                currency_numeric_code,
                currency_code):

        self.country_name = country_name
        self.official_country_name = official_country_name
        self.local_country_name = local_country_name
        self.country_name_in_slovene = country_name_in_slovene
        self.country_code2 = country_code2
        self.country_code3 = country_code3
        self.country_numeric_code = country_numeric_code
        self.currency_numeric_code = currency_numeric_code
        self.currency_code = currency_code
