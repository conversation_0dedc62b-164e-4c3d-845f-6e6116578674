from sqlalchemy import  Column, Integer, String, Numeric, DateTime
from setup_global import SetupGlobal
from libs.mixin import AuditMixin

class ExchangeRateModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'exchange_rate'
    id = Column(Integer, primary_key=True)
    currency_id = Column(Integer)
    rate = Column(Numeric(10,4))
    exchange_date_id = Column(Integer)


    def __init__(self,
                currency_id,
                rate,
                exchange_date_id):

        self.currency_id = currency_id
        self.rate = rate
        self.exchange_date_id = exchange_date_id
