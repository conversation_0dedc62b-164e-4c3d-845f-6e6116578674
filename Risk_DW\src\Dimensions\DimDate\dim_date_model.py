from sqlalchemy import  Column, Integer, String, Date, Boolean
from setup_global import SetupGlobal
from libs.mixin import AuditMixin

class DimDateModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'dim_date'
    id = Column(Integer, primary_key=True)
    date = Column(Date)
    year = Column(Integer)
    half_year = Column(String(2))
    half_year_number = Column(Integer)
    quarter = Column(String(2))
    quarter_number = Column(Integer)
    month = Column(String(3))
    month_number = Column(Integer)
    month_name = Column(String(20))
    week_iso = Column(String(3))
    week_eex = Column(String(4))
    day_of_month = Column(Integer)
    day_of_week = Column(String(10))
    is_weekend = Column(Boolean)


    def __init__ (self,
                date,
                year,
                half_year,
                half_year_number,
                quarter,
                quarter_number,
                month,
                month_number,
                month_name,
                week_iso,
                week_eex,
                day_of_month,
                day_of_week,
                is_weekend):

        self.date = date
        self.year = year
        self.half_year = half_year
        self.half_year_number = half_year_number
        self.quarter = quarter
        self.quarter_number = quarter_number
        self.month = month
        self.month_number = month_number
        self.month_name = month_name
        self.week_iso = week_iso
        self.week_eex = week_eex
        self.day_of_month = day_of_month
        self.day_of_week = day_of_week
        self.is_weekend = is_weekend
