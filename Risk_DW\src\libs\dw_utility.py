from datetime import datetime, timedelta, timezone
from sqlalchemy import text
import pandas as pd
from Models.interval_model import IntervalModel
from Models.period_model import PeriodModel
from Models.trading_periods_model import TradingPeriodsModel
from sqlalchemy.orm import Session

import logging
from dateutil import tz

logger = logging.getLogger(__name__)


class DwUtility:

    @staticmethod
    def get_mapped_market_type(product_code):
        if '_OTC_' in product_code:
            return 'FWD'
        if '_FIN_' in product_code:
            return 'FUT'
        return None

    @staticmethod
    def get_price_curve_from_product(product_country_code, price_curves):
        price_curve_name = 'HPFC ' + product_country_code

        if isinstance(price_curves, dict):
            price_curves = price_curves.values()

        price_curve_record = next((x for x in price_curves if x.price_curve_name == price_curve_name), None)

        if price_curve_record is None:
            logger.error('Missing price curve for "%s"', price_curve_name)
            return None

        return price_curve_record

    @staticmethod
    def get_pfc_prices_from_dw(database, price_curve_id, date_from, date_to):
        with database.engine.connect() as conn:
            pfc_prices_query = f"""
                SELECT DT.date_hour_minute_start, P.*
                FROM trading_dw.prices P
                INNER JOIN trading_dw.dim_time DT ON DT.id = P.time_id
                WHERE
                    P.price_curve_id = {price_curve_id} AND
                    DT.date_hour_minute_end >= '{date_from}' AND
                    DT.date_hour_minute_end <= '{date_to}'
                """
            return conn.execute(text(pfc_prices_query)).all()

    @staticmethod
    def get_mapped_load_profile(load_profile):
        return 'Base Load' if load_profile.strip()[:4] == 'Base' else load_profile

    @staticmethod
    def _get_trading_period_record(product_type, interval_time_from, interval_time_to, resolution_seconds, trading_periods):
        interval_duration = datetime.strptime(interval_time_to, '%Y-%m-%dT%H:%M:%SZ') - datetime.strptime(interval_time_from, '%Y-%m-%dT%H:%M:%SZ')

        if isinstance(trading_periods, dict):
            trading_periods = trading_periods.values()

        if product_type == 'Financial':
            if 28 <= interval_duration.days <= 31:
                trading_period_val = 'Monthly'
            elif interval_duration.days == 1:
                trading_period_val = 'Daily'
            elif interval_duration.days == 2:
                trading_period_val = 'Weekend'
            elif interval_duration.days == 7:
                trading_period_val = 'Weekly'
            elif 365 <= interval_duration.days <= 366:
                trading_period_val = 'Yearly'
            elif 90 <= interval_duration.days <= 92:
                trading_period_val = 'Quarterly'
            elif 175 <= interval_duration.days <= 190:
                trading_period_val = 'Seasonal'
            else:
                logger.error("Interval duration %s days is not handled", interval_duration.days)
                return None

            trading_period_record = next((x for x in trading_periods if x.name == trading_period_val), None)
        else:
            trading_period_record = next((x for x in trading_periods if x.resolution_seconds == resolution_seconds), None)

        if trading_period_record is None:
            logger.error('Missing trading_period "%s"', resolution_seconds if product_type != 'Financial' else trading_period_val)
            return None

        return trading_period_record

    @staticmethod
    def get_product(products, product_types, product_type, product_country_code, market_type, load_profile, interval_time_from, interval_time_to, resolution_seconds, trading_periods):
        if isinstance(products, dict):
            products = products.values()
        if isinstance(product_types, dict):
            product_types = product_types.values()

        trading_period_record = DwUtility._get_trading_period_record(
            product_type=product_type,
            interval_time_from=interval_time_from,
            interval_time_to=interval_time_to,
            resolution_seconds=resolution_seconds,
            trading_periods=trading_periods
        )

        if trading_period_record is None:
            return None

        product_type_record = next((x for x in product_types if x.name == product_type), None)
        prefix = product_country_code + '_'
        product = next((x for x in products if
                        x.name.startswith(prefix)
                        and x.market_type_id == market_type.id
                        and x.load_profile_id == load_profile.id
                        and x.product_type_id == product_type_record.id
                        and x.trading_period_id == trading_period_record.id
                        ), None)

        if product is None:
            logger.error(
                'Missing product type: country code (%s), product type (%s), market type (%s), load profile (%s), trading period (%s)',
                product_country_code, product_type_record.name, market_type.code, load_profile.name, trading_period_record.name)
            return None

        return product, trading_period_record

    @staticmethod
    def get_time_intervals(trades_data, type: str = 'Electricity'):
        periods = []
        trade_dates = []
        periods_min_time = datetime.now(timezone.utc) - timedelta(days=5) #zato da tudi fee-je zajamemo
        periods_max_time = None

        if type == 'Electricity':
            for trade in trades_data:
                trade_date = datetime.fromisoformat(trade.get('clearingDateTime')).replace(microsecond=0)
                trade_dates.append(trade_date)

                for period in trade.get('periods', []):
                    period_time_from = datetime.fromisoformat(period['timeIntervalFrom'])
                    period_time_to = datetime.fromisoformat(period['timeIntervalTo'])

                    periods_min_time = min(periods_min_time, period_time_from) if periods_min_time else period_time_from
                    periods_max_time = max(periods_max_time, period_time_to) if periods_max_time else period_time_to

                    intervals = []
                    for interval in period.get('intervals', []):
                        interval_time_from = datetime.fromisoformat(period['timeIntervalFrom'])
                        interval_time_to = interval_time_from + pd.to_timedelta(interval['pos'] * period['resolutionSeconds'], unit='s')
                        intervals.append(IntervalModel(interval_time_from, interval_time_to))

                    periods.append(PeriodModel(period_time_from, period_time_to, period['resolutionSeconds'], intervals))
        else:
            for trade in trades_data:
                trade_date = datetime.fromisoformat(trade.get('clearingDateTime')).replace(microsecond=0)
                trade_dates.append(trade_date)
                trade_time_from = datetime.fromisoformat(trade.get('timeIntervalFrom'))
                trade_time_to = datetime.fromisoformat(trade.get('timeIntervalTo'))

                period_time_from = trade_time_from
                period_time_to = trade_time_to
                if type == 'CO2':
                    resolution = 2592000    # Monthly
                else:
                    resolution = 86400      # Daily

                current_time = period_time_from
                intervals = []
                while current_time < period_time_to:
                    interval_time_from = current_time
                    interval_time_to = current_time + pd.to_timedelta(resolution, unit='s')

                    if interval_time_to > period_time_to:
                        interval_time_to = period_time_to

                    intervals.append(IntervalModel(interval_time_from, interval_time_to))
                    current_time = interval_time_to

                period = PeriodModel(period_time_from, period_time_to, resolution, intervals)
                periods.append(period)
                periods_min_time = min(periods_min_time, period_time_from) if periods_min_time else period_time_from
                periods_max_time = max(periods_max_time, period_time_to) if periods_max_time else period_time_to

        min_trade_dates = min(trade_dates)
        max_trade_dates = max(trade_dates)

        min_time = min(min_trade_dates, periods_min_time)
        max_time = max(max_trade_dates, periods_max_time)

        return TradingPeriodsModel(periods, periods_min_time, periods_max_time,
                                    min_time.replace(second=0, minute=0, hour=0),
                                    max_time.replace(second=59, minute=59, hour=23))

    @staticmethod
    def get_mtm(price, position_type, market_price, quantity):
        if position_type == 'Buy':
            return (market_price - price) * quantity
        else:
            return (price - market_price) * quantity
