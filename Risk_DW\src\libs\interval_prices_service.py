from collections import defaultdict
from datetime import datetime
from operator import index

import pandas as pd
import logging

logger = logging.getLogger(__name__)


class IntervalPricesService:

    def __init__(self, periods_min_time, periods_max_time, blue_trader_api):
        self.cached_index_prices = defaultdict(list)
        self._cache_index_prices(blue_trader_api, periods_min_time, periods_max_time)

    def _cache_index_prices(self, blue_trader_api, periods_min_time, periods_max_time):
        logger.info("Fetching index prices started...")
        prices = blue_trader_api.get_physical_power_spot_prices(periods_min_time, periods_max_time)

        logger.info("Processing index prices started...")
        for price in prices:
            start_interval = datetime.fromisoformat(price['deliveryDate'])
            current_date_interval = start_interval

            for interval in price.get('intervals'):
                self.cached_index_prices[price['code']].append([current_date_interval, interval['price']])

                current_date_interval = start_interval + pd.to_timedelta(
                    interval['pos'] * price['resolutionSeconds'], unit='s')

        logger.info("Processing index prices completed...")

    def get_price(self, trade, price_date_time, previous_price, current_price=None):

        index_price_code = trade['indexPriceCode']
        if index_price_code is not None:
            prices = self.cached_index_prices[index_price_code]

            price_record = next((x for x in prices if
                                 x[0] == price_date_time),
                                None)

            if price_record is not None:
                return float(price_record[1]) + (0 if current_price is None else float(current_price))

            logger.warning("Index price '%s' for '%s' is not handled...", index_price_code, price_date_time)
            return 0

        if current_price is not None:
            return current_price

        if previous_price is not None:
            return previous_price

        logger.warning("Price for '%s' is not handled...", trade['identification_description'])
        return 0
