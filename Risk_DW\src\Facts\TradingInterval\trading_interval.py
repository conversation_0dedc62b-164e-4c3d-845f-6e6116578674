from sqlalchemy import Column, Integer, Numeric, <PERSON>, <PERSON><PERSON><PERSON>, Text, ForeignKey
from sqlalchemy.orm import relationship
from setup_global import SetupGlobal
from libs.mixin import AuditMixin

class TradingIntervalModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'trading_interval'
    id = Column(Integer, primary_key=True)
    trading_period_id = Column(Integer)
    start_date_id = Column(Integer)
    end_date_id = Column(Integer)
    start_time_id = Column(Integer)
    end_time_id = Column(Integer)
    description = Column(Text)

    # Relationship to TradePricesModel
    trade_prices = relationship("TradePricesModel", back_populates="trading_interval", cascade="all, delete-orphan")

    def __init__(self, trading_period_id, start_date_id, end_date_id, start_time_id, end_time_id, description):
        self.trading_period_id = trading_period_id
        self.start_date_id = start_date_id
        self.end_date_id = end_date_id
        self.start_time_id = start_time_id
        self.end_time_id = end_time_id
        self.description = description
