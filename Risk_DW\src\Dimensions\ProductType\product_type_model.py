from sqlalchemy import  Column, Integer, String, Text
from setup_global import SetupGlobal
from libs.mixin import AuditMixin

class ProductTypeModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'product_type'
    id = Column(Integer, primary_key=True)
    name = Column(String(50))
    description = Column(Text)


    def __init__(self,
                name,
                description):

        self.name = name
        self.description = description
