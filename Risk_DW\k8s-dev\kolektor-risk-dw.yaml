apiVersion: apps/v1
kind: Deployment
metadata:
  name: kolektor-risk-dw
  namespace: data-warehouse
spec:
  replicas: 1  # Adjust as needed
  selector:
    matchLabels:
      app: kolektor-risk-dw
  template:
    metadata:
      labels:
        app: kolektor-risk-dw
    spec:
      containers:
      - name: kolektor-risk-dw
        image: kolektorsetup.azurecr.io/fsi/kolektor-risk-dw:kolektor-risk-dw-1.0.0
        imagePullPolicy: Always
        env:
        - name: ENVIRONMENT
          value: "DEV"
        volumeMounts:
          - name: python-environment-secret
            readOnly: true
            mountPath: "/etc/.envSecret"
          - name: python-environment
            mountPath: "/etc/.env"
            readOnly: true    
        ports:
          - containerPort: 8125        
        resources:
          limits:
            cpu: "0.5"  # Set the CPU limit (adjust as needed)
            memory: "512Mi"  # Set the memory limit (adjust as needed)
          requests:
            cpu: "0.1"  # Set the CPU request (adjust as needed)
            memory: "256Mi"  # Set the memory request (adjust as needed)
        # ... other container configurations ...
        livenessProbe:
          httpGet:
            path: /health
            port: 8125
          initialDelaySeconds: 90
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8125
          initialDelaySeconds: 90
          periodSeconds: 5
      imagePullSecrets:
      - name: bofit-secret
      volumes:
        - name: python-environment-secret
          secret:
            secretName: python-environment-secret
        - name: python-environment
          configMap:
            name: python-environment
        - name: bofit-secret
          secret:
            secretName: bofit-secret