from pyexcel_io import save_data
from pyexcel_io.constants import DB_SQL
from pyexcel_io.database.common import SQLTableImporter, SQLTableImportAdapter
from sqlalchemy import MetaData, create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from pyexcel_io._compact import OrderedDict
from pyexcel_xlsx import get_data
from openpyxl import load_workbook
from Config.ConfigReader import ConfigReader
from pathlib import Path
import pandas as pd
import os

import logging

logger = logging.getLogger(__name__)

class SetupOrmUtility:

    def __init__(self, config_name):
        self.config_db = ConfigReader.get_config_section(config_name,"db")
        self.config_db_orm = ConfigReader.get_config_section(config_name,"db_orm")

        self.codes_session = self._get_session()
        try:
            trading_dw_data_file = self.get_trading_data_file()
            self.data = self.get_data(trading_dw_data_file)
        except FileNotFoundError as ex:
            logger.error('Cannot find trading file: %s', trading_dw_data_file)

    def import_data(self, init_fun, table_name, type_name, data = None):
        if not self.data:
            return

        if data is not None:
            headers = data[table_name][0]
            rows = data[table_name][1:]
        else:
            headers = self.data[table_name][0]
            rows = self.data[table_name][1:]
        df = pd.DataFrame(rows, columns=headers)

        session = self._get_session()

        self.rows_inserted = 0
        for _, row in df.iterrows():
            model_instance = init_fun(row)
            if model_instance is not None:
                session.add(model_instance)
                self.rows_inserted += 1

        session.commit()
        session.close()
        self.codes_session.close()

    def _get_session(self):
        Base = declarative_base(metadata=MetaData(schema=self.config_db_orm['schema']))

        engine = create_engine("postgresql+psycopg://{user}:{password}@{host}:{port}/{dbname}"
                            .format(user=self.config_db['user'],
                            password = self.config_db['password'],
                            host=self.config_db['host'],
                            port=self.config_db['port'],
                            dbname=self.config_db['dbname']),
                            connect_args={'connect_timeout': self.config_db['connection_timeout']})

        Base.metadata.create_all(engine)

        Session = sessionmaker(bind = engine)

        return Session()

    def key_exists(self, list, key):
        return any(row and row[0] == key for row in list)


    def get_trading_data_file(self):

        codes_file_path = Path(self.config_db_orm['codes_file_path'])

        script_dir = Path(__file__).resolve().parent.parent
        trading_dw_data_file = (script_dir / codes_file_path).resolve()

        if not trading_dw_data_file.exists():
            raise FileNotFoundError(f"File not found: {trading_dw_data_file}")

        return str(trading_dw_data_file)

    def get_data(self, filepath, file_type=None, **keywords):
        workbook = load_workbook(filepath, data_only=True)
        data = {}
        for sheetname in workbook.sheetnames:
            sheet = workbook[sheetname]
            data[sheetname] = [
                [cell.value for cell in row] for row in sheet.iter_rows()
            ]
        return data