from Config.ConfigReader import ConfigReader
from Facts.Trade.trade_importer import TradeImporter

import logging

from Facts.Futures.futures import Futures

logger = logging.getLogger(__name__)


class FactsManager:

    def __init__(self, config_name, database):
        self.config_name = config_name
        self.database = database

    def import_facts(self, start_date, end_date):
        logger.info('Importing facts')
        config = ConfigReader.get_config_section(self.config_name, "system")

        trade_importer = TradeImporter(self.config_name, self.database)
        trade_importer.import_data(start_date, end_date)
