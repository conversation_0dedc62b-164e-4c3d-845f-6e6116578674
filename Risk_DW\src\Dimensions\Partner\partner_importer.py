from Dimensions.Partner.partner_model import PartnerModel
from Dimensions.Country.country_model import CountryModel
from libs.setup_orm_utility import SetupOrmUtility
from libs.blue_trader_api import BlueTraderAPI
from pyexcel_io.database.querysets import QuerysetsReader

import logging

logger = logging.getLogger(__name__)

class PartnerImporter():

    def __init__(self,config_name):
        self.setup_orm_utility = SetupOrmUtility(config_name)

        self.blue_trader_api =  BlueTraderAPI(config_name)

        query_sets = self.setup_orm_utility.codes_session.query(PartnerModel).all()
        reader = QuerysetsReader(query_sets, ["vat"])
        self.existing_partners = list(reader.to_array())

    def import_data(self):
        self.rows_inserted = 0

        logger.info('Import started')

        data = self._get_data()
        self.setup_orm_utility.import_data(self.partner_init_func, 'partner',PartnerModel, data)

        logger.info('Import ended. Inserted %s rows', self.rows_inserted)

    def partner_init_func(self,row):

        if self.setup_orm_utility.key_exists (self.existing_partners, row['vat']):
            return None

        country = self.setup_orm_utility.codes_session.query(CountryModel).filter_by(country_code2=row['country_code2']).first()
        if country is None:
            logger.error('Missing country code "%s"', row['country_code2'])
            return None

        self.rows_inserted += 1

        return PartnerModel(
            row['long_name'],
            row['short_name'],
            row['vat'],
            None if country is None else country.country_name,
            row['country_code2'],
            row['street'],
            row['city'],
            row['zip'],
            row['entity_type'])

    def _get_data(self):

        parties = self.blue_trader_api.get_parties()

        result  = [['long_name',
                    'short_name',
                    'vat',
                    'country_code2',
                    'street',
                    'city',
                    'zip',
                    'entity_type'
                    ]]
        for party in parties:
            result.append(
                [
                    party['fullCompanyName'],
                    party['displayName'],
                    party['vatCode'],
                    party['countryIsoAlpha2Code'],
                    None if len(party['addresses']) == 0 else party['addresses'][0]['addressLine1'],
                    None if len(party['addresses']) == 0 else party['addresses'][0]['city'],
                    None if len(party['addresses']) == 0 else party['addresses'][0]['postalCode'],
                    party['partyType']
                ]
            )

        return {'partner' : result}


