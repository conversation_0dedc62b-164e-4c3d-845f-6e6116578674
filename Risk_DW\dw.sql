-- DROP SCHEMA trading_dw;

CREATE SCHEMA trading_dw AUTHORIZATION "fmsReadWrite";

-- DROP SEQUENCE trading_dw.balancing_group_id_seq;

CREATE SEQUENCE trading_dw.balancing_group_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.book_id_seq;

CREATE SEQUENCE trading_dw.book_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.calculation_history_id_seq;

CREATE SEQUENCE trading_dw.calculation_history_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.code_id_seq;

CREATE SEQUENCE trading_dw.code_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.code_value_id_seq;

CREATE SEQUENCE trading_dw.code_value_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.commodity_id_seq;

CREATE SEQUENCE trading_dw.commodity_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.contract_id_seq;

CREATE SEQUENCE trading_dw.contract_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.contract_value_id_seq;

CREATE SEQUENCE trading_dw.contract_value_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.control_area_id_seq;

CREATE SEQUENCE trading_dw.control_area_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.country_id_seq;

CREATE SEQUENCE trading_dw.country_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.currency_id_seq;

CREATE SEQUENCE trading_dw.currency_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.dim_date_id_seq;

CREATE SEQUENCE trading_dw.dim_date_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.dim_insurance_issuer_id_seq;

CREATE SEQUENCE trading_dw.dim_insurance_issuer_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.dim_insurance_type_id_seq;

CREATE SEQUENCE trading_dw.dim_insurance_type_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.dim_time_id_seq;

CREATE SEQUENCE trading_dw.dim_time_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.exchange_rate_id_seq;

CREATE SEQUENCE trading_dw.exchange_rate_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.limit_group_id_seq;

CREATE SEQUENCE trading_dw.limit_group_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.limit_id_seq;

CREATE SEQUENCE trading_dw.limit_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.limit_type_id_seq;

CREATE SEQUENCE trading_dw.limit_type_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.load_profile_id_seq;

CREATE SEQUENCE trading_dw.load_profile_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.market_type_id_seq;

CREATE SEQUENCE trading_dw.market_type_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.mtm_book_id_seq;

CREATE SEQUENCE trading_dw.mtm_book_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.mtm_company_id_seq;

CREATE SEQUENCE trading_dw.mtm_company_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.mtm_portfolio_id_seq;

CREATE SEQUENCE trading_dw.mtm_portfolio_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.mtm_trader_id_seq;

CREATE SEQUENCE trading_dw.mtm_trader_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.partner_id_seq;

CREATE SEQUENCE trading_dw.partner_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.partner_insurance_id_seq;

CREATE SEQUENCE trading_dw.partner_insurance_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.portfolio_book_id_seq;

CREATE SEQUENCE trading_dw.portfolio_book_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.portfolio_id_seq;

CREATE SEQUENCE trading_dw.portfolio_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.price_curve_id_seq;

CREATE SEQUENCE trading_dw.price_curve_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.prices_id_seq;

CREATE SEQUENCE trading_dw.prices_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.product_id_seq;

CREATE SEQUENCE trading_dw.product_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.product_type_id_seq;

CREATE SEQUENCE trading_dw.product_type_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.trade_prices_id_seq;

CREATE SEQUENCE trading_dw.trade_prices_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.trade_prices_id_seq1;

CREATE SEQUENCE trading_dw.trade_prices_id_seq1
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.trader_id_seq;

CREATE SEQUENCE trading_dw.trader_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.trading_interval_id_seq;

CREATE SEQUENCE trading_dw.trading_interval_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.trading_period_id_seq;

CREATE SEQUENCE trading_dw.trading_period_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE trading_dw.var_scenarios_id_seq;

CREATE SEQUENCE trading_dw.var_scenarios_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;-- trading_dw.book definition

-- Drop table

-- DROP TABLE trading_dw.book;

CREATE TABLE trading_dw.book (
	id serial4 NOT NULL, -- Unique identifier for each book
	book_code varchar(50) NOT NULL, -- Unique code for the book
	book_name varchar(100) NOT NULL, -- Name of the book
	description text NULL, -- Description of the book
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- Timestamp when the book record was created
	created_by varchar(100) DEFAULT 'System'::character varying NULL, -- User who created the book record
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- Timestamp when the book record was last modified
	modified_by varchar(100) DEFAULT 'System'::character varying NULL, -- User who last modified the book record
	is_deleted bool DEFAULT false NULL, -- Soft delete flag
	CONSTRAINT book_book_code_key UNIQUE (book_code),
	CONSTRAINT book_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE trading_dw.book IS 'Table for books associated with trading portfolios';

-- Column comments

COMMENT ON COLUMN trading_dw.book.id IS 'Unique identifier for each book';
COMMENT ON COLUMN trading_dw.book.book_code IS 'Unique code for the book';
COMMENT ON COLUMN trading_dw.book.book_name IS 'Name of the book';
COMMENT ON COLUMN trading_dw.book.description IS 'Description of the book';
COMMENT ON COLUMN trading_dw.book.created_at IS 'Timestamp when the book record was created';
COMMENT ON COLUMN trading_dw.book.created_by IS 'User who created the book record';
COMMENT ON COLUMN trading_dw.book.modified_at IS 'Timestamp when the book record was last modified';
COMMENT ON COLUMN trading_dw.book.modified_by IS 'User who last modified the book record';
COMMENT ON COLUMN trading_dw.book.is_deleted IS 'Soft delete flag';


-- trading_dw.code definition

-- Drop table

-- DROP TABLE trading_dw.code;

CREATE TABLE trading_dw.code (
	id serial4 NOT NULL,
	code_name varchar(50) NOT NULL,
	description varchar(255) NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
	modified_at timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
	created_by varchar(100) NOT NULL,
	modified_by varchar(100) NOT NULL,
	CONSTRAINT code_code_name_key UNIQUE (code_name),
	CONSTRAINT code_pkey PRIMARY KEY (id)
);


-- trading_dw.contract definition

-- Drop table

-- DROP TABLE trading_dw.contract;

CREATE TABLE trading_dw.contract (
	id serial4 NOT NULL, -- Unique identifier for each contract (auto-increment)
	business_unit varchar(50) NULL, -- Type of business unit (Trading / Sales)
	market_type varchar(20) NULL, -- Wholesale or Retail market type
	contract_type varchar(20) NULL, -- Type of contract: Physical, Financial, Transport
	folder varchar(20) NULL, -- Category specific to Trading contracts (e.g., LONG, SPOT)
	buy_sell varchar(10) NULL, -- Buy or Sell contract
	contract_source_id int4 NULL, -- Unique identifier of the contract source
	source_name varchar(20) NULL, -- Identifier of the source (e.g., IOPT, IS21P)
	contract_label varchar(100) NULL, -- Text label for Sales sources
	uniform_wsc_id varchar(50) NULL, -- Uniform ID for linking Trading and Sales contracts
	transaction_type varchar(20) NULL, -- Real or Fictional transaction type
	note text NULL, -- Optional text note related to the contract details
	contract_date date NULL, -- Date when the contract was concluded
	year_of_contract_date int4 NULL, -- Year of contract date
	month_of_contract_date int4 NULL, -- Month of contract date
	week_iso_of_contract_date int4 NULL, -- ISO week number of contract date
	delivery_period_start date NULL, -- Start date of delivery period
	delivery_period_end date NULL, -- End date of delivery period
	duration_type varchar(20) NULL, -- Fixed or Indefinite duration of the contract
	qty_for_contractual_imbalances numeric(18, 2) NULL, -- Quantity for contractual imbalances
	imbalance_charging varchar(3) NULL, -- Yes or No for imbalance charging
	days_due int4 NULL, -- Number of days for payment
	closing_by_steps varchar(3) NULL, -- Yes or No for closing by steps
	lock_unlock varchar(3) NULL, -- Yes or No for locking/unlocking
	plid varchar(50) NULL, -- Identifier for specific details (depends on contract type)
	execution_phase varchar(20) NULL, -- Phase of contract execution
	financial_instrument_cascading_phase varchar(30) NULL, -- Phase of financial instrument cascading
	open_closed varchar(10) NULL, -- Status of contract: Closed, Open, Mixed
	cash_settlement varchar(3) NULL, -- Yes or No for daily cash settlement
	risky_contract varchar(10) NULL, -- Risky or Not risky contract
	x_nomination varchar(10) NULL, -- Contract nomination type
	currency_clause varchar(3) NULL, -- Yes or No for currency clause
	capacity_provider varchar(20) NULL, -- Who provides capacity
	capacity_type varchar(20) NULL, -- Type of capacity (e.g., Daily, Weekly)
	strategy_contract varchar(3) NULL, -- Yes or No for strategy contract
	time_zone varchar(10) NULL, -- Time zone (e.g., CET, EET)
	contract_segment varchar(10) NULL, -- Segment of the contract (e.g., B, C, I, P, V)
	power varchar(50) NULL, -- Power capacity specified in the contract
	por varchar(50) NULL, -- Point Of Receipt (POR) hub code
	por_type varchar(20) NULL, -- Type of POR (e.g., Border, Internal)
	pod varchar(50) NULL, -- Point Of Delivery (POD) hub code
	pod_type varchar(20) NULL, -- Type of POD (e.g., Border, Internal)
	balancing_group_por varchar(50) NULL, -- EIC code for Balancing Group POR
	balancing_group_pod varchar(50) NULL, -- EIC code for Balancing Group POD
	fix_flex varchar(10) NULL, -- Fix or Flex contract type
	flex_factor_from numeric(5, 2) NULL, -- Interval for flex factor from
	flex_factor_to numeric(5, 2) NULL, -- Interval for flex factor to
	contract_maturity varchar(10) NULL, -- Short or Long contract maturity
	use_end_of_delivery_exchange_rate varchar(3) NULL, -- Yes or No for using end of delivery exchange rate
	hedging_effectiveness_calculation varchar(3) NULL, -- Yes or No for hedging effectiveness calculation
	hedge_effectiveness varchar(20) NULL, -- Range of hedge effectiveness
	prepayment varchar(3) NULL, -- Yes or No for prepayment
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- Timestamp of when the contract record was created
	created_by varchar(100) DEFAULT 'System'::character varying NULL, -- User who created the contract record
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	modified_by varchar(100) DEFAULT 'System'::character varying NULL,
	is_deleted bool DEFAULT false NULL,
	general_agreement_contract_identification varchar(20) NOT NULL,
	CONSTRAINT contract_pkey PRIMARY KEY (id)
);
CREATE INDEX "IX_CONTRACT_GA_CONTRACT_IDENTIFICATION" ON trading_dw.contract USING btree (general_agreement_contract_identification) WITH (deduplicate_items='true');
COMMENT ON TABLE trading_dw.contract IS 'Dimension table for commodities trading contracts with detailed attributes';

-- Column comments

COMMENT ON COLUMN trading_dw.contract.id IS 'Unique identifier for each contract (auto-increment)';
COMMENT ON COLUMN trading_dw.contract.business_unit IS 'Type of business unit (Trading / Sales)';
COMMENT ON COLUMN trading_dw.contract.market_type IS 'Wholesale or Retail market type';
COMMENT ON COLUMN trading_dw.contract.contract_type IS 'Type of contract: Physical, Financial, Transport';
COMMENT ON COLUMN trading_dw.contract.folder IS 'Category specific to Trading contracts (e.g., LONG, SPOT)';
COMMENT ON COLUMN trading_dw.contract.buy_sell IS 'Buy or Sell contract';
COMMENT ON COLUMN trading_dw.contract.contract_source_id IS 'Unique identifier of the contract source';
COMMENT ON COLUMN trading_dw.contract.source_name IS 'Identifier of the source (e.g., IOPT, IS21P)';
COMMENT ON COLUMN trading_dw.contract.contract_label IS 'Text label for Sales sources';
COMMENT ON COLUMN trading_dw.contract.uniform_wsc_id IS 'Uniform ID for linking Trading and Sales contracts';
COMMENT ON COLUMN trading_dw.contract.transaction_type IS 'Real or Fictional transaction type';
COMMENT ON COLUMN trading_dw.contract.note IS 'Optional text note related to the contract details';
COMMENT ON COLUMN trading_dw.contract.contract_date IS 'Date when the contract was concluded';
COMMENT ON COLUMN trading_dw.contract.year_of_contract_date IS 'Year of contract date';
COMMENT ON COLUMN trading_dw.contract.month_of_contract_date IS 'Month of contract date';
COMMENT ON COLUMN trading_dw.contract.week_iso_of_contract_date IS 'ISO week number of contract date';
COMMENT ON COLUMN trading_dw.contract.delivery_period_start IS 'Start date of delivery period';
COMMENT ON COLUMN trading_dw.contract.delivery_period_end IS 'End date of delivery period';
COMMENT ON COLUMN trading_dw.contract.duration_type IS 'Fixed or Indefinite duration of the contract';
COMMENT ON COLUMN trading_dw.contract.qty_for_contractual_imbalances IS 'Quantity for contractual imbalances';
COMMENT ON COLUMN trading_dw.contract.imbalance_charging IS 'Yes or No for imbalance charging';
COMMENT ON COLUMN trading_dw.contract.days_due IS 'Number of days for payment';
COMMENT ON COLUMN trading_dw.contract.closing_by_steps IS 'Yes or No for closing by steps';
COMMENT ON COLUMN trading_dw.contract.lock_unlock IS 'Yes or No for locking/unlocking';
COMMENT ON COLUMN trading_dw.contract.plid IS 'Identifier for specific details (depends on contract type)';
COMMENT ON COLUMN trading_dw.contract.execution_phase IS 'Phase of contract execution';
COMMENT ON COLUMN trading_dw.contract.financial_instrument_cascading_phase IS 'Phase of financial instrument cascading';
COMMENT ON COLUMN trading_dw.contract.open_closed IS 'Status of contract: Closed, Open, Mixed';
COMMENT ON COLUMN trading_dw.contract.cash_settlement IS 'Yes or No for daily cash settlement';
COMMENT ON COLUMN trading_dw.contract.risky_contract IS 'Risky or Not risky contract';
COMMENT ON COLUMN trading_dw.contract.x_nomination IS 'Contract nomination type';
COMMENT ON COLUMN trading_dw.contract.currency_clause IS 'Yes or No for currency clause';
COMMENT ON COLUMN trading_dw.contract.capacity_provider IS 'Who provides capacity';
COMMENT ON COLUMN trading_dw.contract.capacity_type IS 'Type of capacity (e.g., Daily, Weekly)';
COMMENT ON COLUMN trading_dw.contract.strategy_contract IS 'Yes or No for strategy contract';
COMMENT ON COLUMN trading_dw.contract.time_zone IS 'Time zone (e.g., CET, EET)';
COMMENT ON COLUMN trading_dw.contract.contract_segment IS 'Segment of the contract (e.g., B, C, I, P, V)';
COMMENT ON COLUMN trading_dw.contract.power IS 'Power capacity specified in the contract';
COMMENT ON COLUMN trading_dw.contract.por IS 'Point Of Receipt (POR) hub code';
COMMENT ON COLUMN trading_dw.contract.por_type IS 'Type of POR (e.g., Border, Internal)';
COMMENT ON COLUMN trading_dw.contract.pod IS 'Point Of Delivery (POD) hub code';
COMMENT ON COLUMN trading_dw.contract.pod_type IS 'Type of POD (e.g., Border, Internal)';
COMMENT ON COLUMN trading_dw.contract.balancing_group_por IS 'EIC code for Balancing Group POR';
COMMENT ON COLUMN trading_dw.contract.balancing_group_pod IS 'EIC code for Balancing Group POD';
COMMENT ON COLUMN trading_dw.contract.fix_flex IS 'Fix or Flex contract type';
COMMENT ON COLUMN trading_dw.contract.flex_factor_from IS 'Interval for flex factor from';
COMMENT ON COLUMN trading_dw.contract.flex_factor_to IS 'Interval for flex factor to';
COMMENT ON COLUMN trading_dw.contract.contract_maturity IS 'Short or Long contract maturity';
COMMENT ON COLUMN trading_dw.contract.use_end_of_delivery_exchange_rate IS 'Yes or No for using end of delivery exchange rate';
COMMENT ON COLUMN trading_dw.contract.hedging_effectiveness_calculation IS 'Yes or No for hedging effectiveness calculation';
COMMENT ON COLUMN trading_dw.contract.hedge_effectiveness IS 'Range of hedge effectiveness';
COMMENT ON COLUMN trading_dw.contract.prepayment IS 'Yes or No for prepayment';
COMMENT ON COLUMN trading_dw.contract.created_at IS 'Timestamp of when the contract record was created';
COMMENT ON COLUMN trading_dw.contract.created_by IS 'User who created the contract record';


-- trading_dw.control_area definition

-- Drop table

-- DROP TABLE trading_dw.control_area;

CREATE TABLE trading_dw.control_area (
	id serial4 NOT NULL, -- Unique identifier for each control area (auto-increment)
	country_name varchar(100) NOT NULL, -- Name of the country
	control_area_name varchar(100) NOT NULL, -- Name of the control area
	zone_id varchar(50) NULL, -- ID of the zone used to obtain default price curve
	zone_name varchar(100) NULL, -- Name of the zone
	reference_zone_rt varchar(100) NULL, -- Reference zone used for spread calculations in P&L for the selected RT zone
	default_price_curve varchar(100) NULL, -- Default price curve for the zone
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- Timestamp of when the control area record was created
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- Timestamp of when the control area record was last modified
	created_by varchar(100) DEFAULT 'System'::character varying NULL, -- User who created the control area record
	modified_by varchar(100) DEFAULT 'System'::character varying NULL, -- User who last modified the control area record
	CONSTRAINT control_area_pkey PRIMARY KEY (id)
);
CREATE INDEX idx_control_area_id ON trading_dw.control_area USING btree (id);
COMMENT ON TABLE trading_dw.control_area IS 'Dimension table for control areas including zones and reference data';

-- Column comments

COMMENT ON COLUMN trading_dw.control_area.id IS 'Unique identifier for each control area (auto-increment)';
COMMENT ON COLUMN trading_dw.control_area.country_name IS 'Name of the country';
COMMENT ON COLUMN trading_dw.control_area.control_area_name IS 'Name of the control area';
COMMENT ON COLUMN trading_dw.control_area.zone_id IS 'ID of the zone used to obtain default price curve';
COMMENT ON COLUMN trading_dw.control_area.zone_name IS 'Name of the zone';
COMMENT ON COLUMN trading_dw.control_area.reference_zone_rt IS 'Reference zone used for spread calculations in P&L for the selected RT zone';
COMMENT ON COLUMN trading_dw.control_area.default_price_curve IS 'Default price curve for the zone';
COMMENT ON COLUMN trading_dw.control_area.created_at IS 'Timestamp of when the control area record was created';
COMMENT ON COLUMN trading_dw.control_area.modified_at IS 'Timestamp of when the control area record was last modified';
COMMENT ON COLUMN trading_dw.control_area.created_by IS 'User who created the control area record';
COMMENT ON COLUMN trading_dw.control_area.modified_by IS 'User who last modified the control area record';


-- trading_dw.country definition

-- Drop table

-- DROP TABLE trading_dw.country;

CREATE TABLE trading_dw.country (
	id serial4 NOT NULL, -- Unique identifier for each country (auto-increment)
	country_name varchar(100) NOT NULL, -- Name of the country in English
	official_country_name varchar(100) NULL, -- Official name of the country in English
	local_country_name varchar(100) NULL, -- Name of the country in local language
	country_name_in_slovene varchar(100) NULL, -- Name of the country in Slovene
	country_code2 bpchar(2) NOT NULL, -- ISO 3166-1 alpha-2 country code
	country_code3 bpchar(3) NOT NULL, -- ISO 3166-1 alpha-3 country code
	country_numeric_code int4 NULL, -- Numeric ISO country code
	currency_numeric_code int4 NULL, -- Numeric ISO code of the national currency
	currency_code bpchar(3) NULL, -- ISO code of the national currency
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- Timestamp of when the country record was created
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- Timestamp of when the country record was last modified
	created_by varchar(100) DEFAULT 'System'::character varying NULL, -- User who created the country record
	modified_by varchar(100) DEFAULT 'System'::character varying NULL, -- User who last modified the country record
	CONSTRAINT country_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE trading_dw.country IS 'Dimension table for countries including ISO codes and currency information';

-- Column comments

COMMENT ON COLUMN trading_dw.country.id IS 'Unique identifier for each country (auto-increment)';
COMMENT ON COLUMN trading_dw.country.country_name IS 'Name of the country in English';
COMMENT ON COLUMN trading_dw.country.official_country_name IS 'Official name of the country in English';
COMMENT ON COLUMN trading_dw.country.local_country_name IS 'Name of the country in local language';
COMMENT ON COLUMN trading_dw.country.country_name_in_slovene IS 'Name of the country in Slovene';
COMMENT ON COLUMN trading_dw.country.country_code2 IS 'ISO 3166-1 alpha-2 country code';
COMMENT ON COLUMN trading_dw.country.country_code3 IS 'ISO 3166-1 alpha-3 country code';
COMMENT ON COLUMN trading_dw.country.country_numeric_code IS 'Numeric ISO country code';
COMMENT ON COLUMN trading_dw.country.currency_numeric_code IS 'Numeric ISO code of the national currency';
COMMENT ON COLUMN trading_dw.country.currency_code IS 'ISO code of the national currency';
COMMENT ON COLUMN trading_dw.country.created_at IS 'Timestamp of when the country record was created';
COMMENT ON COLUMN trading_dw.country.modified_at IS 'Timestamp of when the country record was last modified';
COMMENT ON COLUMN trading_dw.country.created_by IS 'User who created the country record';
COMMENT ON COLUMN trading_dw.country.modified_by IS 'User who last modified the country record';


-- trading_dw.currency definition

-- Drop table

-- DROP TABLE trading_dw.currency;

CREATE TABLE trading_dw.currency (
	id serial4 NOT NULL, -- Unique identifier for each currency (auto-increment)
	currency_code bpchar(3) NOT NULL, -- Three-letter currency code per ISO standard 4217
	currency_name varchar(100) NOT NULL, -- Full name of the currency
	currency_numeric_code int2 NOT NULL, -- Numeric code of the currency per ISO standard 4217
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- Timestamp of when the currency record was created
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- Timestamp of when the currency record was last modified
	created_by varchar(100) DEFAULT 'System'::character varying NULL, -- User who created the currency record
	modified_by varchar(100) DEFAULT 'System'::character varying NULL, -- User who last modified the currency record
	CONSTRAINT currency_pkey PRIMARY KEY (id)
);
CREATE INDEX "IX_CURRENCY_NUMERIC_CODE" ON trading_dw.currency USING btree (currency_numeric_code) WITH (deduplicate_items='true');
COMMENT ON TABLE trading_dw.currency IS 'Dimension table for currencies used within the system';

-- Column comments

COMMENT ON COLUMN trading_dw.currency.id IS 'Unique identifier for each currency (auto-increment)';
COMMENT ON COLUMN trading_dw.currency.currency_code IS 'Three-letter currency code per ISO standard 4217';
COMMENT ON COLUMN trading_dw.currency.currency_name IS 'Full name of the currency';
COMMENT ON COLUMN trading_dw.currency.currency_numeric_code IS 'Numeric code of the currency per ISO standard 4217';
COMMENT ON COLUMN trading_dw.currency.created_at IS 'Timestamp of when the currency record was created';
COMMENT ON COLUMN trading_dw.currency.modified_at IS 'Timestamp of when the currency record was last modified';
COMMENT ON COLUMN trading_dw.currency.created_by IS 'User who created the currency record';
COMMENT ON COLUMN trading_dw.currency.modified_by IS 'User who last modified the currency record';


-- trading_dw.dim_date definition

-- Drop table

-- DROP TABLE trading_dw.dim_date;

CREATE TABLE trading_dw.dim_date (
	id serial4 NOT NULL,
	"date" date NOT NULL,
	"year" int4 NOT NULL,
	half_year varchar(2) NOT NULL, -- H1 or H2
	half_year_number int2 NOT NULL,
	quarter varchar(2) NOT NULL, -- Q1, Q2, Q3, or Q4
	quarter_number int2 NOT NULL,
	"month" varchar(3) NOT NULL, -- M01, M02, ..., M12
	month_number int2 NOT NULL,
	month_name varchar(20) NOT NULL,
	week_iso varchar(3) NOT NULL, -- W01, W02, ..., based on ISO rules
	week_eex varchar(4) NOT NULL, -- EW01, EW02, ..., based on EEX rules
	day_of_month int2 NOT NULL,
	day_of_week varchar(10) NOT NULL,
	is_weekend bool NOT NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	created_by varchar(100) DEFAULT 'System'::character varying NULL,
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	modified_by varchar(100) DEFAULT 'System'::character varying NULL,
	is_deleted bool DEFAULT false NULL,
	CONSTRAINT dim_date_pkey PRIMARY KEY (id)
);
CREATE INDEX "IX_DATE" ON trading_dw.dim_date USING btree (date) WITH (deduplicate_items='true');
COMMENT ON TABLE trading_dw.dim_date IS 'Dimension table for dates with various attributes as per specification';

-- Column comments

COMMENT ON COLUMN trading_dw.dim_date.half_year IS 'H1 or H2';
COMMENT ON COLUMN trading_dw.dim_date.quarter IS 'Q1, Q2, Q3, or Q4';
COMMENT ON COLUMN trading_dw.dim_date."month" IS 'M01, M02, ..., M12';
COMMENT ON COLUMN trading_dw.dim_date.week_iso IS 'W01, W02, ..., based on ISO rules';
COMMENT ON COLUMN trading_dw.dim_date.week_eex IS 'EW01, EW02, ..., based on EEX rules';


-- trading_dw.dim_time definition

-- Drop table

-- DROP TABLE trading_dw.dim_time;

CREATE TABLE trading_dw.dim_time (
	id serial4 NOT NULL,
	"date" date NOT NULL,
	hour_interval int2 NOT NULL, -- Hour block of the day, values from 1 to 24
	date_hour_start varchar(13) NOT NULL, -- Timestamp of the start of the hour block
	date_hour_end varchar(13) NOT NULL, -- Timestamp of the end of the hour block
	date_hour_minute_start_end varchar(50) NOT NULL, -- Combined timestamps of the start and end of the time interval
	hour_minute_start time NOT NULL, -- Time (hour+minute) of the start of the time interval
	hour_minute_end time NOT NULL, -- Time (hour+minute) of the end of the time interval
	hour_minute_start_end varchar(11) NOT NULL, -- Combined times of the start and end of the time interval
	minute_start int2 NOT NULL, -- Minute of the start of the time interval
	minute_end int2 NOT NULL, -- Minute of the end of the time interval
	minute_start_end varchar(5) NOT NULL, -- Combined minutes of the start and end of the time interval
	"year" int4 NOT NULL,
	half_year varchar(2) NOT NULL,
	half_year_number int2 NOT NULL,
	quarter varchar(2) NOT NULL,
	quarter_number int2 NOT NULL,
	"month" varchar(3) NOT NULL,
	month_number int2 NOT NULL,
	month_name varchar(20) NOT NULL,
	week_iso varchar(3) NOT NULL,
	week_eex varchar(4) NOT NULL,
	day_of_month int2 NOT NULL,
	day_of_week varchar(10) NOT NULL,
	is_weekend bool NOT NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	created_by varchar(100) DEFAULT 'System'::character varying NULL,
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	modified_by varchar(100) DEFAULT 'System'::character varying NULL,
	is_deleted bool DEFAULT false NULL,
	date_hour_minute_start timestamptz NULL,
	date_hour_minute_end timestamptz NULL,
	CONSTRAINT dim_time_pkey PRIMARY KEY (id)
);
CREATE INDEX "IX_DATE_HOUR_MINUTE_START" ON trading_dw.dim_time USING btree (date_hour_minute_start) WITH (deduplicate_items='true');
COMMENT ON TABLE trading_dw.dim_time IS 'Dimension table for time attributes including 15-minute intervals';

-- Column comments

COMMENT ON COLUMN trading_dw.dim_time.hour_interval IS 'Hour block of the day, values from 1 to 24';
COMMENT ON COLUMN trading_dw.dim_time.date_hour_start IS 'Timestamp of the start of the hour block';
COMMENT ON COLUMN trading_dw.dim_time.date_hour_end IS 'Timestamp of the end of the hour block';
COMMENT ON COLUMN trading_dw.dim_time.date_hour_minute_start_end IS 'Combined timestamps of the start and end of the time interval';
COMMENT ON COLUMN trading_dw.dim_time.hour_minute_start IS 'Time (hour+minute) of the start of the time interval';
COMMENT ON COLUMN trading_dw.dim_time.hour_minute_end IS 'Time (hour+minute) of the end of the time interval';
COMMENT ON COLUMN trading_dw.dim_time.hour_minute_start_end IS 'Combined times of the start and end of the time interval';
COMMENT ON COLUMN trading_dw.dim_time.minute_start IS 'Minute of the start of the time interval';
COMMENT ON COLUMN trading_dw.dim_time.minute_end IS 'Minute of the end of the time interval';
COMMENT ON COLUMN trading_dw.dim_time.minute_start_end IS 'Combined minutes of the start and end of the time interval';


-- trading_dw.insurance_type definition

-- Drop table

-- DROP TABLE trading_dw.insurance_type;

CREATE TABLE trading_dw.insurance_type (
	id serial4 NOT NULL, -- Unique identifier for each insurance type
	"name" varchar(100) NOT NULL, -- Name of the insurance type
	description text NULL, -- Detailed description of the insurance type
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	created_by varchar(100) DEFAULT 'System'::character varying NULL,
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	modified_by varchar(100) DEFAULT 'System'::character varying NULL,
	is_deleted bool DEFAULT false NULL,
	CONSTRAINT dim_insurance_type_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE trading_dw.insurance_type IS 'Dimension table for insurance types';

-- Column comments

COMMENT ON COLUMN trading_dw.insurance_type.id IS 'Unique identifier for each insurance type';
COMMENT ON COLUMN trading_dw.insurance_type."name" IS 'Name of the insurance type';
COMMENT ON COLUMN trading_dw.insurance_type.description IS 'Detailed description of the insurance type';


-- trading_dw.limit_group definition

-- Drop table

-- DROP TABLE trading_dw.limit_group;

CREATE TABLE trading_dw.limit_group (
	id serial4 NOT NULL,
	"name" varchar(50) NOT NULL,
	description varchar(100) NOT NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	created_by varchar(100) DEFAULT 'System'::character varying NULL,
	modified_by varchar(100) DEFAULT 'System'::character varying NULL,
	CONSTRAINT limit_group_pkey PRIMARY KEY (id)
);


-- trading_dw.limit_type definition

-- Drop table

-- DROP TABLE trading_dw.limit_type;

CREATE TABLE trading_dw.limit_type (
	id serial4 NOT NULL,
	"name" varchar(50) NOT NULL,
	description varchar(100) NOT NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	created_by varchar(100) DEFAULT 'System'::character varying NULL,
	modified_by varchar(100) DEFAULT 'System'::character varying NULL,
	CONSTRAINT limit_type_pkey PRIMARY KEY (id)
);


-- trading_dw.load_profile definition

-- Drop table

-- DROP TABLE trading_dw.load_profile;

CREATE TABLE trading_dw.load_profile (
	id serial4 NOT NULL, -- Unique identifier for each load profile
	"name" varchar(50) NOT NULL, -- Name of the load profile
	description text NULL, -- Detailed description of the load profile
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	created_by varchar(100) DEFAULT 'System'::character varying NULL,
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	modified_by varchar(100) DEFAULT 'System'::character varying NULL,
	is_deleted bool DEFAULT false NULL,
	CONSTRAINT load_profile_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE trading_dw.load_profile IS 'Dimension table for load profiles (e.g., Base Load, Peak Load, Off-Peak, Hourly)';

-- Column comments

COMMENT ON COLUMN trading_dw.load_profile.id IS 'Unique identifier for each load profile';
COMMENT ON COLUMN trading_dw.load_profile."name" IS 'Name of the load profile';
COMMENT ON COLUMN trading_dw.load_profile.description IS 'Detailed description of the load profile';


-- trading_dw.market_type definition

-- Drop table

-- DROP TABLE trading_dw.market_type;

CREATE TABLE trading_dw.market_type (
	id serial4 NOT NULL, -- Unique identifier for each market type
	"name" varchar(50) NOT NULL, -- Name of the market type
	description text NULL, -- Detailed description of the market type
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	created_by varchar(100) DEFAULT 'System'::character varying NULL,
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	modified_by varchar(100) DEFAULT 'System'::character varying NULL,
	is_deleted bool DEFAULT false NULL,
	code varchar(10) NULL,
	CONSTRAINT market_type_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE trading_dw.market_type IS 'Dimension table for market types (e.g., Wholesale, Retail)';

-- Column comments

COMMENT ON COLUMN trading_dw.market_type.id IS 'Unique identifier for each market type';
COMMENT ON COLUMN trading_dw.market_type."name" IS 'Name of the market type';
COMMENT ON COLUMN trading_dw.market_type.description IS 'Detailed description of the market type';


-- trading_dw.partner definition

-- Drop table

-- DROP TABLE trading_dw.partner;

CREATE TABLE trading_dw.partner (
	id serial4 NOT NULL, -- Unique identifier for each partner (auto-increment)
	long_name varchar(255) NOT NULL, -- Full name of the partner
	short_name varchar(100) NULL, -- Shortened name of the partner
	street varchar(255) NULL, -- Street address of the partner
	city varchar(100) NULL, -- City where the partner is located
	zip varchar(20) NULL, -- Postal code of the partner
	post_office_name varchar(100) NULL, -- Name of the post office serving the partner
	country_name varchar(100) NULL, -- Name of the country where the partner is located
	country_code2 bpchar(2) NULL, -- ISO 3166-1 alpha-2 country code of the partner
	address varchar(255) NULL, -- Concatenated address field
	vat_status varchar(3) NULL, -- Indicator if the partner is VAT registered (Yes/No)
	vat varchar(50) NULL, -- VAT number of the partner
	vat2 varchar(50) NULL, -- Additional VAT number of the partner
	registration_number varchar(50) NULL, -- Registration number of the partner
	entity_type varchar(50) NULL, -- Type of entity (Legal entity / Private individual)
	scis varchar(50) NULL, -- Standard Classification of Institutional Sectors
	sca varchar(50) NULL, -- Standard Classification of Activities
	group_responsible int4 NULL, -- ID of the responsible group if the partner belongs to a group
	group_responsible_name varchar(100) NULL, -- Name of the responsible group if the partner belongs to a group
	supplier varchar(3) NULL, -- Indicator if the partner is a supplier (Yes/No)
	buyer varchar(3) NULL, -- Indicator if the partner is a buyer (Yes/No)
	bank varchar(3) NULL, -- Indicator if the partner is a bank (Yes/No)
	exchange varchar(3) NULL, -- Indicator if the partner is an exchange (Yes/No)
	clearing_house varchar(3) NULL, -- Indicator if the partner is a clearing house (Yes/No)
	broker varchar(3) NULL, -- Indicator if the partner is a broker (Yes/No)
	eic_code varchar(50) NULL, -- EIC code for the partner (1:N relationship)
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- Timestamp of when the partner record was created
	created_by varchar(100) DEFAULT 'System'::character varying NULL, -- User who created the partner record
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- Timestamp of when the partner record was last modified
	modified_by varchar(100) DEFAULT 'System'::character varying NULL, -- User who last modified the partner record
	is_deleted bool DEFAULT false NULL, -- Indicator if the partner record is logically deleted (true/false)
	CONSTRAINT partner_pkey PRIMARY KEY (id)
);
CREATE INDEX idx_partner_id ON trading_dw.partner USING btree (id);
COMMENT ON TABLE trading_dw.partner IS 'Dimension table for partner attributes including various roles and identifiers';

-- Column comments

COMMENT ON COLUMN trading_dw.partner.id IS 'Unique identifier for each partner (auto-increment)';
COMMENT ON COLUMN trading_dw.partner.long_name IS 'Full name of the partner';
COMMENT ON COLUMN trading_dw.partner.short_name IS 'Shortened name of the partner';
COMMENT ON COLUMN trading_dw.partner.street IS 'Street address of the partner';
COMMENT ON COLUMN trading_dw.partner.city IS 'City where the partner is located';
COMMENT ON COLUMN trading_dw.partner.zip IS 'Postal code of the partner';
COMMENT ON COLUMN trading_dw.partner.post_office_name IS 'Name of the post office serving the partner';
COMMENT ON COLUMN trading_dw.partner.country_name IS 'Name of the country where the partner is located';
COMMENT ON COLUMN trading_dw.partner.country_code2 IS 'ISO 3166-1 alpha-2 country code of the partner';
COMMENT ON COLUMN trading_dw.partner.address IS 'Concatenated address field';
COMMENT ON COLUMN trading_dw.partner.vat_status IS 'Indicator if the partner is VAT registered (Yes/No)';
COMMENT ON COLUMN trading_dw.partner.vat IS 'VAT number of the partner';
COMMENT ON COLUMN trading_dw.partner.vat2 IS 'Additional VAT number of the partner';
COMMENT ON COLUMN trading_dw.partner.registration_number IS 'Registration number of the partner';
COMMENT ON COLUMN trading_dw.partner.entity_type IS 'Type of entity (Legal entity / Private individual)';
COMMENT ON COLUMN trading_dw.partner.scis IS 'Standard Classification of Institutional Sectors';
COMMENT ON COLUMN trading_dw.partner.sca IS 'Standard Classification of Activities';
COMMENT ON COLUMN trading_dw.partner.group_responsible IS 'ID of the responsible group if the partner belongs to a group';
COMMENT ON COLUMN trading_dw.partner.group_responsible_name IS 'Name of the responsible group if the partner belongs to a group';
COMMENT ON COLUMN trading_dw.partner.supplier IS 'Indicator if the partner is a supplier (Yes/No)';
COMMENT ON COLUMN trading_dw.partner.buyer IS 'Indicator if the partner is a buyer (Yes/No)';
COMMENT ON COLUMN trading_dw.partner.bank IS 'Indicator if the partner is a bank (Yes/No)';
COMMENT ON COLUMN trading_dw.partner.exchange IS 'Indicator if the partner is an exchange (Yes/No)';
COMMENT ON COLUMN trading_dw.partner.clearing_house IS 'Indicator if the partner is a clearing house (Yes/No)';
COMMENT ON COLUMN trading_dw.partner.broker IS 'Indicator if the partner is a broker (Yes/No)';
COMMENT ON COLUMN trading_dw.partner.eic_code IS 'EIC code for the partner (1:N relationship)';
COMMENT ON COLUMN trading_dw.partner.created_at IS 'Timestamp of when the partner record was created';
COMMENT ON COLUMN trading_dw.partner.created_by IS 'User who created the partner record';
COMMENT ON COLUMN trading_dw.partner.modified_at IS 'Timestamp of when the partner record was last modified';
COMMENT ON COLUMN trading_dw.partner.modified_by IS 'User who last modified the partner record';
COMMENT ON COLUMN trading_dw.partner.is_deleted IS 'Indicator if the partner record is logically deleted (true/false)';


-- trading_dw.price_curve definition

-- Drop table

-- DROP TABLE trading_dw.price_curve;

CREATE TABLE trading_dw.price_curve (
	id serial4 NOT NULL, -- Unique identifier for each price curve (auto-increment)
	price_curve_name varchar(100) NOT NULL, -- Name of the price curve
	price_curve_type varchar(50) NULL, -- Type of the price curve
	description text NULL, -- Description of the price curve
	currency_id varchar(3) NULL, -- ISO 4217 currency code for the price curve
	last_modified timestamp NULL, -- Timestamp of the last modification on the source
	used_by_sales bool DEFAULT true NULL, -- Indicator if the price curve is used in Sales (true/false)
	used_by_trading bool DEFAULT true NULL, -- Indicator if the price curve is used in Trading (true/false)
	time_zone varchar(50) NULL, -- Time zone associated with the price curve
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- Timestamp of when the price curve record was created
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- Timestamp of when the price curve record was last modified
	created_by varchar(100) DEFAULT 'System'::character varying NULL, -- User who created the price curve record
	modified_by varchar(100) DEFAULT 'System'::character varying NULL, -- User who last modified the price curve record
	CONSTRAINT price_curve_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE trading_dw.price_curve IS 'Dimension table for price curves used in trading and sales departments';

-- Column comments

COMMENT ON COLUMN trading_dw.price_curve.id IS 'Unique identifier for each price curve (auto-increment)';
COMMENT ON COLUMN trading_dw.price_curve.price_curve_name IS 'Name of the price curve';
COMMENT ON COLUMN trading_dw.price_curve.price_curve_type IS 'Type of the price curve';
COMMENT ON COLUMN trading_dw.price_curve.description IS 'Description of the price curve';
COMMENT ON COLUMN trading_dw.price_curve.currency_id IS 'ISO 4217 currency code for the price curve';
COMMENT ON COLUMN trading_dw.price_curve.last_modified IS 'Timestamp of the last modification on the source';
COMMENT ON COLUMN trading_dw.price_curve.used_by_sales IS 'Indicator if the price curve is used in Sales (true/false)';
COMMENT ON COLUMN trading_dw.price_curve.used_by_trading IS 'Indicator if the price curve is used in Trading (true/false)';
COMMENT ON COLUMN trading_dw.price_curve.time_zone IS 'Time zone associated with the price curve';
COMMENT ON COLUMN trading_dw.price_curve.created_at IS 'Timestamp of when the price curve record was created';
COMMENT ON COLUMN trading_dw.price_curve.modified_at IS 'Timestamp of when the price curve record was last modified';
COMMENT ON COLUMN trading_dw.price_curve.created_by IS 'User who created the price curve record';
COMMENT ON COLUMN trading_dw.price_curve.modified_by IS 'User who last modified the price curve record';


-- trading_dw.product_type definition

-- Drop table

-- DROP TABLE trading_dw.product_type;

CREATE TABLE trading_dw.product_type (
	id serial4 NOT NULL, -- Unique identifier for each period type
	"name" varchar(50) NOT NULL, -- Name of the period type
	description text NULL, -- Detailed description of the period type
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	created_by varchar(100) DEFAULT 'System'::character varying NULL,
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	modified_by varchar(100) DEFAULT 'System'::character varying NULL,
	is_deleted bool DEFAULT false NULL,
	CONSTRAINT product_type_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE trading_dw.product_type IS 'Dimension table for Trading periods';

-- Column comments

COMMENT ON COLUMN trading_dw.product_type.id IS 'Unique identifier for each period type';
COMMENT ON COLUMN trading_dw.product_type."name" IS 'Name of the period type';
COMMENT ON COLUMN trading_dw.product_type.description IS 'Detailed description of the period type';


-- trading_dw.realization_book definition

-- Drop table

-- DROP TABLE trading_dw.realization_book;

CREATE TABLE trading_dw.realization_book (
	book_id int4 NOT NULL,
	identification_description_buy varchar(13) NOT NULL,
	identification_description_sell varchar(13) NOT NULL,
	trading_interval_start_id int4 NOT NULL,
	trading_interval_end_id_buy int4 NULL,
	trading_interval_end_id_sell int4 NULL,
	price_buy numeric(18, 6) NULL,
	price_sell numeric(18, 6) NULL,
	quantity_buy numeric(18, 6) NOT NULL,
	quantity_sell numeric(18, 6) NOT NULL,
	quantity_reminder_buy numeric(18, 6) NOT NULL,
	quantity_reminder_sell numeric(18, 6) NOT NULL,
	trade_date_id_buy int4 NOT NULL,
	trade_date_id_sell int4 NOT NULL,
	product_id_buy int4 NOT NULL,
	product_id_sell int4 NOT NULL,
	realization numeric(18, 6) NULL,
	buy_closed bool NOT NULL,
	sell_closed bool NOT NULL,
	created_at timestamptz DEFAULT now() NULL,
	created_by varchar(255) NULL,
	modified_at timestamptz DEFAULT now() NULL,
	modified_by varchar(255) NULL,
	CONSTRAINT realization_book_pkey PRIMARY KEY (book_id, identification_description_buy, identification_description_sell, trading_interval_start_id)
);


-- trading_dw.realization_company definition

-- Drop table

-- DROP TABLE trading_dw.realization_company;

CREATE TABLE trading_dw.realization_company (
	identification_description_buy varchar(13) NOT NULL,
	identification_description_sell varchar(13) NOT NULL,
	trading_interval_start_id int4 NOT NULL,
	trading_interval_end_id_buy int4 NULL,
	trading_interval_end_id_sell int4 NULL,
	price_buy numeric(18, 6) NULL,
	price_sell numeric(18, 6) NULL,
	quantity_buy numeric(18, 6) NOT NULL,
	quantity_sell numeric(18, 6) NOT NULL,
	quantity_reminder_buy numeric(18, 6) NOT NULL,
	quantity_reminder_sell numeric(18, 6) NOT NULL,
	trade_date_id_buy int4 NOT NULL,
	trade_date_id_sell int4 NOT NULL,
	product_id_sell int4 NOT NULL,
	product_id_buy int4 NOT NULL,
	realization numeric(18, 6) NULL,
	buy_closed bool NOT NULL,
	sell_closed bool NOT NULL,
	created_at timestamptz DEFAULT now() NULL,
	created_by varchar(255) NULL,
	modified_at timestamptz DEFAULT now() NULL,
	modified_by varchar(255) NULL,
	CONSTRAINT realization_pkey PRIMARY KEY (identification_description_buy, identification_description_sell, trading_interval_start_id)
);
CREATE INDEX idx_min_reminder_buy ON trading_dw.realization_company USING btree (identification_description_buy, identification_description_sell, trading_interval_start_id, quantity_reminder_buy, quantity_reminder_sell);
CREATE INDEX idx_realization_company_identification_start ON trading_dw.realization_company USING btree (identification_description_buy, identification_description_sell, trading_interval_start_id);
CREATE INDEX idx_realization_company_interval_ids ON trading_dw.realization_company USING btree (trading_interval_start_id, trading_interval_end_id_buy, trading_interval_end_id_sell);
CREATE INDEX idx_realization_company_product_ids ON trading_dw.realization_company USING btree (product_id_buy, product_id_sell);
CREATE INDEX idx_realization_company_trade_date_ids ON trading_dw.realization_company USING btree (trade_date_id_buy, trade_date_id_sell);
CREATE INDEX idx_sum_realization ON trading_dw.realization_company USING btree (identification_description_buy, identification_description_sell, trading_interval_start_id, realization);


-- trading_dw.realization_cross definition

-- Drop table

-- DROP TABLE trading_dw.realization_cross;

CREATE TABLE trading_dw.realization_cross (
	identification_description_buy varchar(13) NOT NULL,
	identification_description_sell varchar(13) NOT NULL,
	trading_interval_start_id int4 NOT NULL,
	trading_interval_end_id_buy int4 NULL,
	trading_interval_end_id_sell int4 NULL,
	price_buy numeric(18, 6) NULL,
	price_sell numeric(18, 6) NULL,
	quantity_buy numeric(18, 6) NOT NULL,
	quantity_sell numeric(18, 6) NOT NULL,
	quantity_reminder_buy numeric(18, 6) NOT NULL,
	quantity_reminder_sell numeric(18, 6) NOT NULL,
	trade_date_id_buy int4 NOT NULL,
	trade_date_id_sell int4 NOT NULL,
	realization numeric(18, 6) NULL,
	buy_closed bool NOT NULL,
	sell_closed bool NOT NULL,
	created_at timestamptz DEFAULT now() NULL,
	created_by varchar(255) NULL,
	modified_at timestamptz DEFAULT now() NULL,
	modified_by varchar(255) NULL,
	CONSTRAINT realization_cross_pkey PRIMARY KEY (identification_description_buy, identification_description_sell, trading_interval_start_id)
);


-- trading_dw.realization_portfolio definition

-- Drop table

-- DROP TABLE trading_dw.realization_portfolio;

CREATE TABLE trading_dw.realization_portfolio (
	portfolio_id int4 NOT NULL,
	identification_description_buy varchar(13) NOT NULL,
	identification_description_sell varchar(13) NOT NULL,
	trading_interval_start_id int4 NOT NULL,
	trading_interval_end_id_buy int4 NULL,
	trading_interval_end_id_sell int4 NULL,
	price_buy numeric(18, 6) NULL,
	price_sell numeric(18, 6) NULL,
	quantity_buy numeric(18, 6) NOT NULL,
	quantity_sell numeric(18, 6) NOT NULL,
	quantity_reminder_buy numeric(18, 6) NOT NULL,
	quantity_reminder_sell numeric(18, 6) NOT NULL,
	trade_date_id_buy int4 NOT NULL,
	trade_date_id_sell int4 NOT NULL,
	realization numeric(18, 6) NULL,
	product_id_buy int4 NOT NULL,
	product_id_sell int4 NOT NULL,
	buy_closed bool NOT NULL,
	sell_closed bool NOT NULL,
	created_at timestamptz DEFAULT now() NULL,
	created_by varchar(255) NULL,
	modified_at timestamptz DEFAULT now() NULL,
	modified_by varchar(255) NULL,
	CONSTRAINT realization_portfolio_pkey PRIMARY KEY (portfolio_id, identification_description_buy, identification_description_sell, trading_interval_start_id)
);


-- trading_dw.realization_trader definition

-- Drop table

-- DROP TABLE trading_dw.realization_trader;

CREATE TABLE trading_dw.realization_trader (
	trader_id int4 NOT NULL,
	identification_description_buy varchar(13) NOT NULL,
	identification_description_sell varchar(13) NOT NULL,
	trading_interval_start_id int4 NOT NULL,
	trading_interval_end_id_buy int4 NULL,
	trading_interval_end_id_sell int4 NULL,
	price_buy numeric(18, 6) NULL,
	price_sell numeric(18, 6) NULL,
	quantity_buy numeric(18, 6) NOT NULL,
	quantity_sell numeric(18, 6) NOT NULL,
	quantity_reminder_buy numeric(18, 6) NOT NULL,
	quantity_reminder_sell numeric(18, 6) NOT NULL,
	trade_date_id_buy int4 NOT NULL,
	trade_date_id_sell int4 NOT NULL,
	product_id_buy int4 NOT NULL,
	product_id_sell int4 NOT NULL,
	realization numeric(18, 6) NULL,
	buy_closed bool NOT NULL,
	sell_closed bool NOT NULL,
	created_at timestamptz DEFAULT now() NULL,
	created_by varchar(255) NULL,
	modified_at timestamptz DEFAULT now() NULL,
	modified_by varchar(255) NULL,
	CONSTRAINT realization_trader_pkey PRIMARY KEY (trader_id, identification_description_buy, identification_description_sell, trading_interval_start_id)
);


-- trading_dw.trader definition

-- Drop table

-- DROP TABLE trading_dw.trader;

CREATE TABLE trading_dw.trader (
	id serial4 NOT NULL, -- Unique identifier for each trader
	trader_name varchar(100) NOT NULL, -- Full name of the trader
	department varchar(50) NULL, -- Department to which the trader belongs
	business_unit varchar(50) NULL, -- Business unit to which the trader belongs
	active bool DEFAULT true NULL, -- Indicator if the trader is currently active (true/false)
	contact_info varchar(50) NULL, -- Contact information of the trader, if available
	trader_classification varchar(50) NULL, -- trader classification
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- Timestamp of when the trader record was created
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- Timestamp of when the trader record was last modified
	created_by varchar(100) DEFAULT 'System'::character varying NULL, -- User who created the trader record
	modified_by varchar(100) DEFAULT 'System'::character varying NULL, -- User who last modified the trader record
	CONSTRAINT trader_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE trading_dw.trader IS 'Dimension table for traders in the trading department';

-- Column comments

COMMENT ON COLUMN trading_dw.trader.id IS 'Unique identifier for each trader';
COMMENT ON COLUMN trading_dw.trader.trader_name IS 'Full name of the trader';
COMMENT ON COLUMN trading_dw.trader.department IS 'Department to which the trader belongs';
COMMENT ON COLUMN trading_dw.trader.business_unit IS 'Business unit to which the trader belongs';
COMMENT ON COLUMN trading_dw.trader.active IS 'Indicator if the trader is currently active (true/false)';
COMMENT ON COLUMN trading_dw.trader.contact_info IS 'Contact information of the trader, if available';
COMMENT ON COLUMN trading_dw.trader.trader_classification IS 'trader classification';
COMMENT ON COLUMN trading_dw.trader.created_at IS 'Timestamp of when the trader record was created';
COMMENT ON COLUMN trading_dw.trader.modified_at IS 'Timestamp of when the trader record was last modified';
COMMENT ON COLUMN trading_dw.trader.created_by IS 'User who created the trader record';
COMMENT ON COLUMN trading_dw.trader.modified_by IS 'User who last modified the trader record';


-- trading_dw.trading_period definition

-- Drop table

-- DROP TABLE trading_dw.trading_period;

CREATE TABLE trading_dw.trading_period (
	id serial4 NOT NULL, -- Unique identifier for each trading period type
	"name" varchar(50) NOT NULL, -- Name of the trading period type
	description varchar(255) NOT NULL, -- Description of the trading period type
	resolution_seconds int4 NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	created_by varchar(100) DEFAULT 'System'::character varying NULL,
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	modified_by varchar(100) DEFAULT 'System'::character varying NULL,
	CONSTRAINT trading_period_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE trading_dw.trading_period IS 'Dimension table for types of trading periods';

-- Column comments

COMMENT ON COLUMN trading_dw.trading_period.id IS 'Unique identifier for each trading period type';
COMMENT ON COLUMN trading_dw.trading_period."name" IS 'Name of the trading period type';
COMMENT ON COLUMN trading_dw.trading_period.description IS 'Description of the trading period type';


-- trading_dw.balancing_group definition

-- Drop table

-- DROP TABLE trading_dw.balancing_group;

CREATE TABLE trading_dw.balancing_group (
	id serial4 NOT NULL, -- Unique identifier for each balancing group (auto-increment)
	balancing_group_name varchar(100) NOT NULL, -- Name of the balancing group (NazivBilančneSkupine)
	balancing_group_eic varchar(50) NULL, -- EIC code of the balancing group (EICBilančneSkupine)
	balancing_subgroup_name varchar(255) NULL, -- Name of the balancing subgroup (NazivBilančnePodskupine)
	balancing_subgroup_eic varchar(50) NULL, -- EIC code of the balancing subgroup (EICBilančnePodskupine)
	control_area_id int4 NULL, -- ID of the control area to which the balancing group belongs
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- Timestamp of when the balancing group record was created
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- Timestamp of when the balancing group record was last modified
	created_by varchar(100) DEFAULT 'System'::character varying NULL, -- User who created the balancing group record
	modified_by varchar(100) DEFAULT 'System'::character varying NULL, -- User who last modified the balancing group record
	CONSTRAINT balancing_group_pkey PRIMARY KEY (id),
	CONSTRAINT balancing_group_control_area_id_fkey FOREIGN KEY (control_area_id) REFERENCES trading_dw.control_area(id)
);
COMMENT ON TABLE trading_dw.balancing_group IS 'Dimension table for balancing groups including subgroup hierarchy and related control areas';

-- Column comments

COMMENT ON COLUMN trading_dw.balancing_group.id IS 'Unique identifier for each balancing group (auto-increment)';
COMMENT ON COLUMN trading_dw.balancing_group.balancing_group_name IS 'Name of the balancing group (NazivBilančneSkupine)';
COMMENT ON COLUMN trading_dw.balancing_group.balancing_group_eic IS 'EIC code of the balancing group (EICBilančneSkupine)';
COMMENT ON COLUMN trading_dw.balancing_group.balancing_subgroup_name IS 'Name of the balancing subgroup (NazivBilančnePodskupine)';
COMMENT ON COLUMN trading_dw.balancing_group.balancing_subgroup_eic IS 'EIC code of the balancing subgroup (EICBilančnePodskupine)';
COMMENT ON COLUMN trading_dw.balancing_group.control_area_id IS 'ID of the control area to which the balancing group belongs';
COMMENT ON COLUMN trading_dw.balancing_group.created_at IS 'Timestamp of when the balancing group record was created';
COMMENT ON COLUMN trading_dw.balancing_group.modified_at IS 'Timestamp of when the balancing group record was last modified';
COMMENT ON COLUMN trading_dw.balancing_group.created_by IS 'User who created the balancing group record';
COMMENT ON COLUMN trading_dw.balancing_group.modified_by IS 'User who last modified the balancing group record';


-- trading_dw.code_value definition

-- Drop table

-- DROP TABLE trading_dw.code_value;

CREATE TABLE trading_dw.code_value (
	id serial4 NOT NULL,
	code_id int4 NOT NULL,
	value varchar(100) NOT NULL,
	description varchar(255) NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
	modified_at timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
	created_by varchar(100) NOT NULL,
	modified_by varchar(100) NOT NULL,
	CONSTRAINT code_value_pkey PRIMARY KEY (id),
	CONSTRAINT code_value_code_id_fkey FOREIGN KEY (code_id) REFERENCES trading_dw.code(id) ON DELETE CASCADE
);


-- trading_dw.commodity definition

-- Drop table

-- DROP TABLE trading_dw.commodity;

CREATE TABLE trading_dw.commodity (
	id serial4 NOT NULL, -- Unique identifier for each commodity (auto-increment)
	"name" varchar(100) NOT NULL, -- Name of the commodity
	"type" varchar(100) NOT NULL, -- Type or category of the commodity
	description text NULL, -- Description of the commodity
	unit varchar(50) NULL, -- Unit of measurement for the commodity
	currency_id int4 NULL, -- Foreign key referencing the currency table
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- Timestamp of when the commodity record was created
	created_by varchar(100) DEFAULT 'System'::character varying NULL, -- User who created the commodity record
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- Timestamp of when the commodity record was last modified
	modified_by varchar(100) DEFAULT 'System'::character varying NULL, -- User who last modified the commodity record
	is_deleted bool DEFAULT false NULL, -- Indicator if the commodity record is logically deleted (true/false)
	CONSTRAINT commodity_pkey PRIMARY KEY (id),
	CONSTRAINT fk_currency FOREIGN KEY (currency_id) REFERENCES trading_dw.currency(id)
);
CREATE INDEX idx_commodity_id ON trading_dw.commodity USING btree (id);
COMMENT ON TABLE trading_dw.commodity IS 'Dimension table for commodities traded in the trading system';

-- Column comments

COMMENT ON COLUMN trading_dw.commodity.id IS 'Unique identifier for each commodity (auto-increment)';
COMMENT ON COLUMN trading_dw.commodity."name" IS 'Name of the commodity';
COMMENT ON COLUMN trading_dw.commodity."type" IS 'Type or category of the commodity';
COMMENT ON COLUMN trading_dw.commodity.description IS 'Description of the commodity';
COMMENT ON COLUMN trading_dw.commodity.unit IS 'Unit of measurement for the commodity';
COMMENT ON COLUMN trading_dw.commodity.currency_id IS 'Foreign key referencing the currency table';
COMMENT ON COLUMN trading_dw.commodity.created_at IS 'Timestamp of when the commodity record was created';
COMMENT ON COLUMN trading_dw.commodity.created_by IS 'User who created the commodity record';
COMMENT ON COLUMN trading_dw.commodity.modified_at IS 'Timestamp of when the commodity record was last modified';
COMMENT ON COLUMN trading_dw.commodity.modified_by IS 'User who last modified the commodity record';
COMMENT ON COLUMN trading_dw.commodity.is_deleted IS 'Indicator if the commodity record is logically deleted (true/false)';


-- trading_dw.contract_value definition

-- Drop table

-- DROP TABLE trading_dw.contract_value;

CREATE TABLE trading_dw.contract_value (
	id serial4 NOT NULL, -- Unique identifier for each contract value record
	contract_id int4 NOT NULL, -- Foreign key referencing the associated contract
	value_date_id int4 NOT NULL, -- Foreign key referencing the date dimension for the value date
	quantity numeric(18, 3) NULL, -- Contracted quantity (e.g., MWh)
	price numeric(18, 6) NULL, -- Price per unit
	currency_id int4 NOT NULL, -- Foreign key referencing the currency dimension
	total_value numeric(18, 2) NULL, -- Total contract value (quantity * price)
	mark_to_market_value numeric(18, 2) NULL, -- Current market value of the contract
	unrealized_pnl numeric(18, 2) NULL, -- Unrealized profit/loss
	realized_pnl numeric(18, 2) NULL, -- Realized profit/loss
	exchange_rate numeric(10, 6) NULL, -- Exchange rate if applicable
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	created_by varchar(100) DEFAULT 'System'::character varying NOT NULL,
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	modified_by varchar(100) DEFAULT 'System'::character varying NOT NULL,
	is_deleted bool DEFAULT false NOT NULL,
	CONSTRAINT contract_value_pkey PRIMARY KEY (id),
	CONSTRAINT fk_contract_value_contract FOREIGN KEY (contract_id) REFERENCES trading_dw.contract(id),
	CONSTRAINT fk_contract_value_currency FOREIGN KEY (currency_id) REFERENCES trading_dw.currency(id),
	CONSTRAINT fk_contract_value_date FOREIGN KEY (value_date_id) REFERENCES trading_dw.dim_date(id)
);
COMMENT ON TABLE trading_dw.contract_value IS 'Fact table for contract values, including financial and quantitative details';

-- Column comments

COMMENT ON COLUMN trading_dw.contract_value.id IS 'Unique identifier for each contract value record';
COMMENT ON COLUMN trading_dw.contract_value.contract_id IS 'Foreign key referencing the associated contract';
COMMENT ON COLUMN trading_dw.contract_value.value_date_id IS 'Foreign key referencing the date dimension for the value date';
COMMENT ON COLUMN trading_dw.contract_value.quantity IS 'Contracted quantity (e.g., MWh)';
COMMENT ON COLUMN trading_dw.contract_value.price IS 'Price per unit';
COMMENT ON COLUMN trading_dw.contract_value.currency_id IS 'Foreign key referencing the currency dimension';
COMMENT ON COLUMN trading_dw.contract_value.total_value IS 'Total contract value (quantity * price)';
COMMENT ON COLUMN trading_dw.contract_value.mark_to_market_value IS 'Current market value of the contract';
COMMENT ON COLUMN trading_dw.contract_value.unrealized_pnl IS 'Unrealized profit/loss';
COMMENT ON COLUMN trading_dw.contract_value.realized_pnl IS 'Realized profit/loss';
COMMENT ON COLUMN trading_dw.contract_value.exchange_rate IS 'Exchange rate if applicable';


-- trading_dw.exchange_rate definition

-- Drop table

-- DROP TABLE trading_dw.exchange_rate;

CREATE TABLE trading_dw.exchange_rate (
	id serial4 NOT NULL,
	currency_id int4 NOT NULL,
	exchange_date_id int4 NOT NULL,
	rate numeric(10, 4) NOT NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	created_by varchar(100) DEFAULT 'System'::character varying NULL,
	modified_by varchar(100) DEFAULT 'System'::character varying NULL,
	CONSTRAINT exchange_rate_pkey PRIMARY KEY (id),
	CONSTRAINT exchange_rate_currency_id_fkey FOREIGN KEY (currency_id) REFERENCES trading_dw.currency(id)
);


-- trading_dw.holiday definition

-- Drop table

-- DROP TABLE trading_dw.holiday;

CREATE TABLE trading_dw.holiday (
	"date" timestamp NOT NULL,
	country_id int4 NOT NULL,
	is_work_free_day bool NOT NULL,
	is_pre_work_free_day bool NOT NULL,
	is_post_work_free_day bool NOT NULL,
	is_bridge bool NOT NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	created_by varchar(100) DEFAULT 'System'::character varying NULL,
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	modified_by varchar(100) DEFAULT 'System'::character varying NULL,
	is_deleted bool DEFAULT false NULL,
	CONSTRAINT holiday_pk PRIMARY KEY (date, country_id),
	CONSTRAINT country_id FOREIGN KEY (country_id) REFERENCES trading_dw.country(id)
);


-- trading_dw.insurance_issuer definition

-- Drop table

-- DROP TABLE trading_dw.insurance_issuer;

CREATE TABLE trading_dw.insurance_issuer (
	id serial4 NOT NULL, -- Unique identifier for each insurance issuer
	"name" varchar(255) NOT NULL, -- Name of the insurance issuer
	issuer_type varchar(50) NOT NULL, -- Type of issuer (e.g., Bank, Insurance Company, Parent Company)
	country_id int4 NULL, -- Reference to dim_country.id for the country of the issuer
	registration_number varchar(100) NULL, -- Official registration number of the issuer
	contact_person varchar(255) NULL, -- Name of the primary contact person
	contact_email varchar(255) NULL, -- Email of the primary contact person
	contact_phone varchar(50) NULL, -- Phone number of the primary contact person
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	created_by varchar(100) DEFAULT 'System'::character varying NULL,
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	modified_by varchar(100) DEFAULT 'System'::character varying NULL,
	is_deleted bool DEFAULT false NULL,
	CONSTRAINT dim_insurance_issuer_pkey PRIMARY KEY (id),
	CONSTRAINT fk_insurance_issuer_country_id FOREIGN KEY (country_id) REFERENCES trading_dw.country(id)
);
COMMENT ON TABLE trading_dw.insurance_issuer IS 'Dimension table for insurance issuers';

-- Column comments

COMMENT ON COLUMN trading_dw.insurance_issuer.id IS 'Unique identifier for each insurance issuer';
COMMENT ON COLUMN trading_dw.insurance_issuer."name" IS 'Name of the insurance issuer';
COMMENT ON COLUMN trading_dw.insurance_issuer.issuer_type IS 'Type of issuer (e.g., Bank, Insurance Company, Parent Company)';
COMMENT ON COLUMN trading_dw.insurance_issuer.country_id IS 'Reference to dim_country.id for the country of the issuer';
COMMENT ON COLUMN trading_dw.insurance_issuer.registration_number IS 'Official registration number of the issuer';
COMMENT ON COLUMN trading_dw.insurance_issuer.contact_person IS 'Name of the primary contact person';
COMMENT ON COLUMN trading_dw.insurance_issuer.contact_email IS 'Email of the primary contact person';
COMMENT ON COLUMN trading_dw.insurance_issuer.contact_phone IS 'Phone number of the primary contact person';


-- trading_dw."limit" definition

-- Drop table

-- DROP TABLE trading_dw."limit";

CREATE TABLE trading_dw."limit" (
	id serial4 NOT NULL,
	"name" varchar(50) DEFAULT 'System'::character varying NOT NULL,
	limit_group_id int4 NOT NULL,
	limit_type_id int4 NOT NULL,
	subject_id int4 NOT NULL,
	time_period varchar(50) DEFAULT 'System'::character varying NULL,
	area varchar(10) DEFAULT 'System'::character varying NULL,
	unit varchar(10) DEFAULT 'System'::character varying NULL,
	limit_value numeric(18, 2) NOT NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	created_by varchar(100) DEFAULT 'System'::character varying NULL,
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	modified_by varchar(100) DEFAULT 'System'::character varying NULL,
	time_period_year int4 NULL,
	product_type_id int4 NULL,
	CONSTRAINT limit_pkey PRIMARY KEY (id),
	CONSTRAINT limit_group_key_fkey FOREIGN KEY (limit_group_id) REFERENCES trading_dw.limit_group(id),
	CONSTRAINT limit_type_key_fkey FOREIGN KEY (limit_type_id) REFERENCES trading_dw.limit_type(id)
);


-- trading_dw.partner_insurance definition

-- Drop table

-- DROP TABLE trading_dw.partner_insurance;

CREATE TABLE trading_dw.partner_insurance (
	id serial4 NOT NULL, -- Unique identifier for each insurance record
	partner_id int4 NOT NULL, -- Reference to partner.id for the insured partner
	insurance_type_id int4 NOT NULL, -- Reference to insurance_type.id for the type of insurance
	insurance_issuer_id int4 NOT NULL, -- Reference to insurance_issuer.id for the issuer of the insurance
	amount numeric(18, 2) NOT NULL, -- Amount of the insurance
	currency_id int4 NOT NULL, -- Reference to dim_currency.id for the currency of the insurance amount
	start_date_id int4 NOT NULL, -- Reference to dim_date.id for the start date of the insurance
	end_date_id int4 NOT NULL, -- Reference to dim_date.id for the end date of the insurance
	reference_number varchar(100) NULL, -- Reference number or identifier for the insurance document
	status varchar(20) NOT NULL, -- Status of the insurance (e.g., active, expired, cancelled)
	notes text NULL, -- Additional notes or comments about the insurance
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	created_by varchar(100) DEFAULT 'System'::character varying NULL,
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	modified_by varchar(100) DEFAULT 'System'::character varying NULL,
	is_deleted bool DEFAULT false NULL,
	CONSTRAINT partner_insurance_pkey PRIMARY KEY (id),
	CONSTRAINT fk_partner_insurance_currency_id FOREIGN KEY (currency_id) REFERENCES trading_dw.currency(id),
	CONSTRAINT fk_partner_insurance_end_date_id FOREIGN KEY (end_date_id) REFERENCES trading_dw.dim_date(id),
	CONSTRAINT fk_partner_insurance_insurance_issuer_id FOREIGN KEY (insurance_issuer_id) REFERENCES trading_dw.insurance_issuer(id),
	CONSTRAINT fk_partner_insurance_insurance_type_id FOREIGN KEY (insurance_type_id) REFERENCES trading_dw.insurance_type(id),
	CONSTRAINT fk_partner_insurance_partner_id FOREIGN KEY (partner_id) REFERENCES trading_dw.partner(id),
	CONSTRAINT fk_partner_insurance_start_date_id FOREIGN KEY (start_date_id) REFERENCES trading_dw.dim_date(id)
);
COMMENT ON TABLE trading_dw.partner_insurance IS 'Table to store insurance information for partners';

-- Column comments

COMMENT ON COLUMN trading_dw.partner_insurance.id IS 'Unique identifier for each insurance record';
COMMENT ON COLUMN trading_dw.partner_insurance.partner_id IS 'Reference to partner.id for the insured partner';
COMMENT ON COLUMN trading_dw.partner_insurance.insurance_type_id IS 'Reference to insurance_type.id for the type of insurance';
COMMENT ON COLUMN trading_dw.partner_insurance.insurance_issuer_id IS 'Reference to insurance_issuer.id for the issuer of the insurance';
COMMENT ON COLUMN trading_dw.partner_insurance.amount IS 'Amount of the insurance';
COMMENT ON COLUMN trading_dw.partner_insurance.currency_id IS 'Reference to dim_currency.id for the currency of the insurance amount';
COMMENT ON COLUMN trading_dw.partner_insurance.start_date_id IS 'Reference to dim_date.id for the start date of the insurance';
COMMENT ON COLUMN trading_dw.partner_insurance.end_date_id IS 'Reference to dim_date.id for the end date of the insurance';
COMMENT ON COLUMN trading_dw.partner_insurance.reference_number IS 'Reference number or identifier for the insurance document';
COMMENT ON COLUMN trading_dw.partner_insurance.status IS 'Status of the insurance (e.g., active, expired, cancelled)';
COMMENT ON COLUMN trading_dw.partner_insurance.notes IS 'Additional notes or comments about the insurance';


-- trading_dw.portfolio definition

-- Drop table

-- DROP TABLE trading_dw.portfolio;

CREATE TABLE trading_dw.portfolio (
	id serial4 NOT NULL, -- Unique identifier for each portfolio
	portfolio_code varchar(50) NOT NULL, -- Unique code for the portfolio
	portfolio_name varchar(100) NOT NULL, -- Name of the portfolio
	portfolio_type varchar(50) NOT NULL, -- Type of portfolio (e.g., Trading, Hedging, Investment)
	risk_profile varchar(50) NULL, -- Risk profile of the portfolio (e.g., Low, Medium, High)
	parent_portfolio_id int4 NULL, -- ID of the parent portfolio, if this is a sub-portfolio
	business_unit varchar(100) NULL, -- Business unit responsible for the portfolio
	strategy varchar(100) NULL, -- Trading strategy associated with the portfolio
	currency_id int4 NULL, -- Base currency of the portfolio
	is_active bool DEFAULT true NULL, -- Indicates if the portfolio is currently active
	var_limit numeric(18, 2) NULL, -- Value at Risk (VaR) limit for the portfolio
	position_limit numeric(18, 2) NULL, -- Position limit for the portfolio
	description text NULL, -- Detailed description of the portfolio
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- Timestamp when the portfolio record was created
	created_by varchar(100) DEFAULT 'System'::character varying NULL, -- User who created the portfolio record
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- Timestamp when the portfolio record was last modified
	modified_by varchar(100) DEFAULT 'System'::character varying NULL, -- User who last modified the portfolio record
	is_deleted bool DEFAULT false NULL, -- Soft delete flag
	CONSTRAINT portfolio_pkey PRIMARY KEY (id),
	CONSTRAINT portfolio_portfolio_code_key UNIQUE (portfolio_code),
	CONSTRAINT fk_portfolio_currency FOREIGN KEY (currency_id) REFERENCES trading_dw.currency(id),
	CONSTRAINT fk_portfolio_parent FOREIGN KEY (parent_portfolio_id) REFERENCES trading_dw.portfolio(id)
);
COMMENT ON TABLE trading_dw.portfolio IS 'Dimension table for trading portfolios';

-- Column comments

COMMENT ON COLUMN trading_dw.portfolio.id IS 'Unique identifier for each portfolio';
COMMENT ON COLUMN trading_dw.portfolio.portfolio_code IS 'Unique code for the portfolio';
COMMENT ON COLUMN trading_dw.portfolio.portfolio_name IS 'Name of the portfolio';
COMMENT ON COLUMN trading_dw.portfolio.portfolio_type IS 'Type of portfolio (e.g., Trading, Hedging, Investment)';
COMMENT ON COLUMN trading_dw.portfolio.risk_profile IS 'Risk profile of the portfolio (e.g., Low, Medium, High)';
COMMENT ON COLUMN trading_dw.portfolio.parent_portfolio_id IS 'ID of the parent portfolio, if this is a sub-portfolio';
COMMENT ON COLUMN trading_dw.portfolio.business_unit IS 'Business unit responsible for the portfolio';
COMMENT ON COLUMN trading_dw.portfolio.strategy IS 'Trading strategy associated with the portfolio';
COMMENT ON COLUMN trading_dw.portfolio.currency_id IS 'Base currency of the portfolio';
COMMENT ON COLUMN trading_dw.portfolio.is_active IS 'Indicates if the portfolio is currently active';
COMMENT ON COLUMN trading_dw.portfolio.var_limit IS 'Value at Risk (VaR) limit for the portfolio';
COMMENT ON COLUMN trading_dw.portfolio.position_limit IS 'Position limit for the portfolio';
COMMENT ON COLUMN trading_dw.portfolio.description IS 'Detailed description of the portfolio';
COMMENT ON COLUMN trading_dw.portfolio.created_at IS 'Timestamp when the portfolio record was created';
COMMENT ON COLUMN trading_dw.portfolio.created_by IS 'User who created the portfolio record';
COMMENT ON COLUMN trading_dw.portfolio.modified_at IS 'Timestamp when the portfolio record was last modified';
COMMENT ON COLUMN trading_dw.portfolio.modified_by IS 'User who last modified the portfolio record';
COMMENT ON COLUMN trading_dw.portfolio.is_deleted IS 'Soft delete flag';


-- trading_dw.portfolio_book definition

-- Drop table

-- DROP TABLE trading_dw.portfolio_book;

CREATE TABLE trading_dw.portfolio_book (
	id serial4 NOT NULL,
	portfolio_id int4 NOT NULL,
	book_id int4 NOT NULL,
	allocation_percent numeric(5, 2) DEFAULT 100.00 NOT NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
	created_by varchar(100) DEFAULT 'DWH'::character varying NULL,
	modified_at timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
	modified_by varchar(100) DEFAULT 'DWH'::character varying NULL,
	is_deleted bool DEFAULT false NOT NULL,
	CONSTRAINT portfolio_book_pkey PRIMARY KEY (id),
	CONSTRAINT unique_portfolio_book UNIQUE (portfolio_id, book_id),
	CONSTRAINT fk_book FOREIGN KEY (book_id) REFERENCES trading_dw.book(id),
	CONSTRAINT fk_portfolio FOREIGN KEY (portfolio_id) REFERENCES trading_dw.portfolio(id)
);


-- trading_dw.prices definition

-- Drop table

-- DROP TABLE trading_dw.prices;

CREATE TABLE trading_dw.prices (
	id serial4 NOT NULL, -- Unique identifier for each price entry (auto-increment)
	date_id int4 NOT NULL, -- Foreign key referencing dim_date table
	time_id int4 NOT NULL, -- Foreign key referencing dim_time table
	price_curve_id int4 NOT NULL, -- Foreign key referencing price_curve table
	price numeric(18, 6) NOT NULL, -- Price value
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- Timestamp of when the price record was created
	created_by varchar(100) DEFAULT 'Dule'::character varying NULL, -- User who created the price record
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- Timestamp of when the price record was last modified
	modified_by varchar(100) DEFAULT 'Dule'::character varying NULL, -- User who last modified the price record
	is_updated bool DEFAULT false NOT NULL,
	CONSTRAINT fact_prices_date_id_time_id_curve_id_key UNIQUE (date_id, time_id, price_curve_id),
	CONSTRAINT fact_prices_pkey PRIMARY KEY (id),
	CONSTRAINT fk_fact_prices_date FOREIGN KEY (date_id) REFERENCES trading_dw.dim_date(id),
	CONSTRAINT fk_fact_prices_price_curve FOREIGN KEY (price_curve_id) REFERENCES trading_dw.price_curve(id),
	CONSTRAINT fk_fact_prices_time FOREIGN KEY (time_id) REFERENCES trading_dw.dim_time(id)
);
COMMENT ON TABLE trading_dw.prices IS 'Fact table for prices used in trading and sales departments';

-- Column comments

COMMENT ON COLUMN trading_dw.prices.id IS 'Unique identifier for each price entry (auto-increment)';
COMMENT ON COLUMN trading_dw.prices.date_id IS 'Foreign key referencing dim_date table';
COMMENT ON COLUMN trading_dw.prices.time_id IS 'Foreign key referencing dim_time table';
COMMENT ON COLUMN trading_dw.prices.price_curve_id IS 'Foreign key referencing price_curve table';
COMMENT ON COLUMN trading_dw.prices.price IS 'Price value';
COMMENT ON COLUMN trading_dw.prices.created_at IS 'Timestamp of when the price record was created';
COMMENT ON COLUMN trading_dw.prices.created_by IS 'User who created the price record';
COMMENT ON COLUMN trading_dw.prices.modified_at IS 'Timestamp of when the price record was last modified';
COMMENT ON COLUMN trading_dw.prices.modified_by IS 'User who last modified the price record';


-- trading_dw.product definition

-- Drop table

-- DROP TABLE trading_dw.product;

CREATE TABLE trading_dw.product (
	id serial4 NOT NULL, -- Unique identifier for each product
	"name" varchar(100) NOT NULL, -- Name of the product
	product_type_id int4 NOT NULL, -- Foreign key to product_type table
	load_profile_id int4 NOT NULL, -- Foreign key to load_profile table
	market_type_id int4 NOT NULL, -- Foreign key to market_type table
	commodity_id int4 NOT NULL, -- Foreign key to commodity table
	trading_period_id int4 NOT NULL, -- Foreign key to trading_period table
	por varchar(50) NULL, -- Point of Receipt
	pod varchar(50) NULL, -- Point of Delivery
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	created_by varchar(100) DEFAULT 'System'::character varying NULL,
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	modified_by varchar(100) DEFAULT 'System'::character varying NULL,
	is_deleted bool DEFAULT false NULL,
	CONSTRAINT product_pkey PRIMARY KEY (id),
	CONSTRAINT fk_commodity FOREIGN KEY (commodity_id) REFERENCES trading_dw.commodity(id),
	CONSTRAINT fk_load_profile FOREIGN KEY (load_profile_id) REFERENCES trading_dw.load_profile(id),
	CONSTRAINT fk_market_type FOREIGN KEY (market_type_id) REFERENCES trading_dw.market_type(id),
	CONSTRAINT fk_product_type FOREIGN KEY (product_type_id) REFERENCES trading_dw.product_type(id),
	CONSTRAINT fk_trading_period FOREIGN KEY (trading_period_id) REFERENCES trading_dw.trading_period(id)
);
COMMENT ON TABLE trading_dw.product IS 'Dimension table for tradable products';

-- Column comments

COMMENT ON COLUMN trading_dw.product.id IS 'Unique identifier for each product';
COMMENT ON COLUMN trading_dw.product."name" IS 'Name of the product';
COMMENT ON COLUMN trading_dw.product.product_type_id IS 'Foreign key to product_type table';
COMMENT ON COLUMN trading_dw.product.load_profile_id IS 'Foreign key to load_profile table';
COMMENT ON COLUMN trading_dw.product.market_type_id IS 'Foreign key to market_type table';
COMMENT ON COLUMN trading_dw.product.commodity_id IS 'Foreign key to commodity table';
COMMENT ON COLUMN trading_dw.product.trading_period_id IS 'Foreign key to trading_period table';
COMMENT ON COLUMN trading_dw.product.por IS 'Point of Receipt';
COMMENT ON COLUMN trading_dw.product.pod IS 'Point of Delivery';


-- trading_dw.trade definition

-- Drop table

-- DROP TABLE trading_dw.trade;

CREATE TABLE trading_dw.trade (
	identification_description varchar(13) NOT NULL,
	trade_date_id int4 NOT NULL, -- Reference to dim_date.id for trade date
	company_id int4 NOT NULL, -- Reference to partner.id for Company
	currency_id int4 NOT NULL,
	counterparty_id int4 NOT NULL, -- Reference to partner.id for Counterparty
	position_type varchar(10) NOT NULL, -- Position type (BUY/SELL)
	in_control_area_id int4 NOT NULL,
	out_control_area_id int4 NOT NULL, -- Reference to control_area.id
	trade_book_id int4 NOT NULL, -- Reference to dim_trade_book.id for trade book
	trader_id int4 NOT NULL, -- Reference to trader.id
	trade_name varchar(100) NOT NULL, -- Trade name
	trade_type varchar(50) NOT NULL, -- Trade type
	product_id int4 NOT NULL, -- Reference to product.id
	general_agreement_contract_identification varchar(20) NOT NULL, -- Reference to contract.general_agreement_contract_identification if applicable
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	created_by varchar(100) DEFAULT 'System'::character varying NULL,
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	modified_by varchar(100) DEFAULT 'System'::character varying NULL,
	is_deleted bool DEFAULT false NULL,
	reserved_flag bool DEFAULT false NOT NULL,
	transaction_time timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT trade_pkey PRIMARY KEY (identification_description),
	CONSTRAINT fk_trade_company_id FOREIGN KEY (company_id) REFERENCES trading_dw.partner(id),
	CONSTRAINT fk_trade_control_area_id FOREIGN KEY (out_control_area_id) REFERENCES trading_dw.control_area(id),
	CONSTRAINT fk_trade_counterparty_id FOREIGN KEY (counterparty_id) REFERENCES trading_dw.partner(id),
	CONSTRAINT fk_trade_currency_id FOREIGN KEY (currency_id) REFERENCES trading_dw.currency(id),
	CONSTRAINT fk_trade_product_id FOREIGN KEY (product_id) REFERENCES trading_dw.product(id),
	CONSTRAINT fk_trade_trade_book_id FOREIGN KEY (trade_book_id) REFERENCES trading_dw.book(id),
	CONSTRAINT fk_trade_trade_date_id FOREIGN KEY (trade_date_id) REFERENCES trading_dw.dim_date(id),
	CONSTRAINT fk_trade_trader_id FOREIGN KEY (trader_id) REFERENCES trading_dw.trader(id)
);
CREATE INDEX "IX_position_type" ON trading_dw.trade USING btree (position_type) WITH (deduplicate_items='true');
CREATE INDEX idx_trade_identification ON trading_dw.trade USING btree (identification_description);
CREATE INDEX trade_transaction_time_idx ON trading_dw.trade USING btree (transaction_time);
COMMENT ON TABLE trading_dw.trade IS 'Combined fact table for trade and mark-to-market positions';

-- Column comments

COMMENT ON COLUMN trading_dw.trade.trade_date_id IS 'Reference to dim_date.id for trade date';
COMMENT ON COLUMN trading_dw.trade.company_id IS 'Reference to partner.id for Company';
COMMENT ON COLUMN trading_dw.trade.counterparty_id IS 'Reference to partner.id for Counterparty';
COMMENT ON COLUMN trading_dw.trade.position_type IS 'Position type (BUY/SELL)';
COMMENT ON COLUMN trading_dw.trade.out_control_area_id IS 'Reference to control_area.id';
COMMENT ON COLUMN trading_dw.trade.trade_book_id IS 'Reference to dim_trade_book.id for trade book';
COMMENT ON COLUMN trading_dw.trade.trader_id IS 'Reference to trader.id';
COMMENT ON COLUMN trading_dw.trade.trade_name IS 'Trade name';
COMMENT ON COLUMN trading_dw.trade.trade_type IS 'Trade type';
COMMENT ON COLUMN trading_dw.trade.product_id IS 'Reference to product.id';
COMMENT ON COLUMN trading_dw.trade.general_agreement_contract_identification IS 'Reference to contract.general_agreement_contract_identification if applicable';


-- trading_dw.trading_interval definition

-- Drop table

-- DROP TABLE trading_dw.trading_interval;

CREATE TABLE trading_dw.trading_interval (
	id serial4 NOT NULL, -- Unique identifier for each trading interval
	trading_period_id int4 NOT NULL, -- Foreign key to trading_period for the type of trading period
	start_date_id int4 NOT NULL, -- Foreign key to dim_date for the start date of the trading interval
	end_date_id int4 NOT NULL, -- Foreign key to dim_date for the end date of the trading interval
	start_time_id int4 NOT NULL, -- Foreign key to dim_time for the start time of the trading interval
	end_time_id int4 NOT NULL, -- Foreign key to dim_time for the end time of the trading interval
	description text NULL, -- Additional description for this specific trading interval, if needed
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	created_by varchar(100) DEFAULT 'System'::character varying NULL,
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	modified_by varchar(100) DEFAULT 'System'::character varying NULL,
	is_deleted bool DEFAULT false NULL,
	CONSTRAINT trading_interval_pkey PRIMARY KEY (id),
	CONSTRAINT fk_end_date FOREIGN KEY (end_date_id) REFERENCES trading_dw.dim_date(id),
	CONSTRAINT fk_end_time FOREIGN KEY (end_time_id) REFERENCES trading_dw.dim_time(id),
	CONSTRAINT fk_start_date FOREIGN KEY (start_date_id) REFERENCES trading_dw.dim_date(id),
	CONSTRAINT fk_start_time FOREIGN KEY (start_time_id) REFERENCES trading_dw.dim_time(id),
	CONSTRAINT fk_trading_period FOREIGN KEY (trading_period_id) REFERENCES trading_dw.trading_period(id)
);
COMMENT ON TABLE trading_dw.trading_interval IS 'Fact table for specific trading intervals';

-- Column comments

COMMENT ON COLUMN trading_dw.trading_interval.id IS 'Unique identifier for each trading interval';
COMMENT ON COLUMN trading_dw.trading_interval.trading_period_id IS 'Foreign key to trading_period for the type of trading period';
COMMENT ON COLUMN trading_dw.trading_interval.start_date_id IS 'Foreign key to dim_date for the start date of the trading interval';
COMMENT ON COLUMN trading_dw.trading_interval.end_date_id IS 'Foreign key to dim_date for the end date of the trading interval';
COMMENT ON COLUMN trading_dw.trading_interval.start_time_id IS 'Foreign key to dim_time for the start time of the trading interval';
COMMENT ON COLUMN trading_dw.trading_interval.end_time_id IS 'Foreign key to dim_time for the end time of the trading interval';
COMMENT ON COLUMN trading_dw.trading_interval.description IS 'Additional description for this specific trading interval, if needed';


-- trading_dw.var_scenarios definition

-- Drop table

-- DROP TABLE trading_dw.var_scenarios;

CREATE TABLE trading_dw.var_scenarios (
	id serial4 NOT NULL, -- Unique identifier for each VaR scenario result
	date_id int4 NOT NULL, -- Foreign key to date table, represents the date of the VaR calculation
	scenario_name varchar(100) NOT NULL, -- Name of the VaR scenario
	scenario_type varchar(50) NOT NULL, -- Type of scenario (e.g., Historical, Monte Carlo, Parametric)
	confidence_level numeric(5, 2) NOT NULL, -- Confidence level used in VaR calculation (e.g., 95, 99)
	time_horizon int4 NOT NULL, -- Time horizon for VaR calculation in days
	var_value numeric(18, 6) NOT NULL, -- Calculated Value at Risk
	expected_shortfall numeric(18, 6) NULL, -- Expected Shortfall (Conditional VaR) value
	stress_test_factor numeric(5, 2) NULL, -- Stress test factor applied, if any
	portfolio_id int4 NULL, -- Foreign key to portfolio table, represents the portfolio for which VaR is calculated
	commodity_id int4 NULL, -- Foreign key to commodity table, represents the commodity if VaR is calculated for a specific commodity
	currency_id_scenario int4 NULL, -- Foreign key to currency table, represents the currency of the VaR value
	calculation_method varchar(50) NULL, -- Method used for VaR calculation (e.g., Variance-Covariance, Historical Simulation)
	is_regulatory bool DEFAULT false NULL, -- Indicates if the VaR calculation is for regulatory reporting purposes
	notes text NULL, -- Additional notes or comments about the VaR scenario
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- Timestamp when the record was created
	created_by varchar(100) DEFAULT 'System'::character varying NULL, -- User who created the record
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- Timestamp when the record was last modified
	modified_by varchar(100) DEFAULT 'System'::character varying NULL, -- User who last modified the record
	is_deleted bool DEFAULT false NULL, -- Soft delete flag
	CONSTRAINT var_scenarios_pkey PRIMARY KEY (id),
	CONSTRAINT fk_var_scenarios_commodity FOREIGN KEY (commodity_id) REFERENCES trading_dw.commodity(id),
	CONSTRAINT fk_var_scenarios_currency FOREIGN KEY (currency_id_scenario) REFERENCES trading_dw.currency(id),
	CONSTRAINT fk_var_scenarios_date FOREIGN KEY (date_id) REFERENCES trading_dw.dim_date(id),
	CONSTRAINT fk_var_scenarios_portfolio FOREIGN KEY (portfolio_id) REFERENCES trading_dw.portfolio(id)
);
COMMENT ON TABLE trading_dw.var_scenarios IS 'Fact table for Value at Risk (VaR) scenario analysis results';

-- Column comments

COMMENT ON COLUMN trading_dw.var_scenarios.id IS 'Unique identifier for each VaR scenario result';
COMMENT ON COLUMN trading_dw.var_scenarios.date_id IS 'Foreign key to date table, represents the date of the VaR calculation';
COMMENT ON COLUMN trading_dw.var_scenarios.scenario_name IS 'Name of the VaR scenario';
COMMENT ON COLUMN trading_dw.var_scenarios.scenario_type IS 'Type of scenario (e.g., Historical, Monte Carlo, Parametric)';
COMMENT ON COLUMN trading_dw.var_scenarios.confidence_level IS 'Confidence level used in VaR calculation (e.g., 95, 99)';
COMMENT ON COLUMN trading_dw.var_scenarios.time_horizon IS 'Time horizon for VaR calculation in days';
COMMENT ON COLUMN trading_dw.var_scenarios.var_value IS 'Calculated Value at Risk';
COMMENT ON COLUMN trading_dw.var_scenarios.expected_shortfall IS 'Expected Shortfall (Conditional VaR) value';
COMMENT ON COLUMN trading_dw.var_scenarios.stress_test_factor IS 'Stress test factor applied, if any';
COMMENT ON COLUMN trading_dw.var_scenarios.portfolio_id IS 'Foreign key to portfolio table, represents the portfolio for which VaR is calculated';
COMMENT ON COLUMN trading_dw.var_scenarios.commodity_id IS 'Foreign key to commodity table, represents the commodity if VaR is calculated for a specific commodity';
COMMENT ON COLUMN trading_dw.var_scenarios.currency_id_scenario IS 'Foreign key to currency table, represents the currency of the VaR value';
COMMENT ON COLUMN trading_dw.var_scenarios.calculation_method IS 'Method used for VaR calculation (e.g., Variance-Covariance, Historical Simulation)';
COMMENT ON COLUMN trading_dw.var_scenarios.is_regulatory IS 'Indicates if the VaR calculation is for regulatory reporting purposes';
COMMENT ON COLUMN trading_dw.var_scenarios.notes IS 'Additional notes or comments about the VaR scenario';
COMMENT ON COLUMN trading_dw.var_scenarios.created_at IS 'Timestamp when the record was created';
COMMENT ON COLUMN trading_dw.var_scenarios.created_by IS 'User who created the record';
COMMENT ON COLUMN trading_dw.var_scenarios.modified_at IS 'Timestamp when the record was last modified';
COMMENT ON COLUMN trading_dw.var_scenarios.modified_by IS 'User who last modified the record';
COMMENT ON COLUMN trading_dw.var_scenarios.is_deleted IS 'Soft delete flag';


-- trading_dw.calculation_history definition

-- Drop table

-- DROP TABLE trading_dw.calculation_history;

CREATE TABLE trading_dw.calculation_history (
	id serial4 NOT NULL,
	calculation_date_id int4 NOT NULL,
	calculation_type_id int4 NOT NULL,
	calculation_code_id int4 NOT NULL,
	object_id int4 NOT NULL,
	value numeric(18, 6) NOT NULL,
	created_at timestamptz DEFAULT now() NULL,
	modified_at timestamptz DEFAULT now() NULL,
	created_by varchar(50) DEFAULT 'DWH'::character varying NOT NULL,
	modified_by varchar(50) DEFAULT 'DWH'::character varying NOT NULL,
	CONSTRAINT calculation_history_pkey PRIMARY KEY (id),
	CONSTRAINT calculation_history_calculation_code_id_fkey FOREIGN KEY (calculation_code_id) REFERENCES trading_dw.code_value(id),
	CONSTRAINT calculation_history_calculation_date_id_fkey FOREIGN KEY (calculation_date_id) REFERENCES trading_dw.dim_date(id),
	CONSTRAINT calculation_history_calculation_type_id_fkey FOREIGN KEY (calculation_type_id) REFERENCES trading_dw.code_value(id)
);


-- trading_dw.mtm_book definition

-- Drop table

-- DROP TABLE trading_dw.mtm_book;

CREATE TABLE trading_dw.mtm_book (
	id serial4 NOT NULL,
	calculation_date_id int4 NOT NULL,
	book_id int4 NOT NULL,
	identification_description varchar(13) NOT NULL,
	delivery_day_id int4 NOT NULL,
	trading_interval_start_id int4 NOT NULL,
	trading_interval_end_id int4 NULL,
	position_type varchar(10) NOT NULL,
	price numeric(18, 6) NULL,
	quantity numeric(18, 6) NOT NULL,
	reminder_of_quantity numeric(18, 6) NOT NULL,
	trade_date_id int4 NOT NULL,
	product_id int4 NOT NULL,
	market_price_id int4 NOT NULL,
	mtm numeric(18, 6) NULL,
	mtm_previous_year numeric(18, 6) DEFAULT 0 NOT NULL,
	mtm_previous_day numeric(18, 6) DEFAULT 0 NOT NULL,
	trade_closed bool NOT NULL,
	created_at timestamptz DEFAULT now() NULL,
	created_by varchar(255) NULL,
	modified_at timestamptz DEFAULT now() NULL,
	modified_by varchar(255) NULL,
	CONSTRAINT mtm_book_pkey PRIMARY KEY (id),
	CONSTRAINT fk_mtm_book_book_id FOREIGN KEY (book_id) REFERENCES trading_dw.book(id),
	CONSTRAINT fk_mtm_book_delivery_day_id FOREIGN KEY (delivery_day_id) REFERENCES trading_dw.dim_date(id),
	CONSTRAINT fk_mtm_book_market_price_id FOREIGN KEY (market_price_id) REFERENCES trading_dw.prices(id),
	CONSTRAINT fk_mtm_book_product_id FOREIGN KEY (product_id) REFERENCES trading_dw.product(id),
	CONSTRAINT fk_mtm_book_trading_interval_end_id FOREIGN KEY (trading_interval_end_id) REFERENCES trading_dw.dim_time(id),
	CONSTRAINT fk_mtm_book_trading_interval_start_id FOREIGN KEY (trading_interval_start_id) REFERENCES trading_dw.dim_time(id),
	CONSTRAINT fk_mtm_calculation_date_id FOREIGN KEY (calculation_date_id) REFERENCES trading_dw.dim_date(id)
);


-- trading_dw.mtm_company definition

-- Drop table

-- DROP TABLE trading_dw.mtm_company;

CREATE TABLE trading_dw.mtm_company (
	id serial4 NOT NULL,
	calculation_date_id int4 NOT NULL,
	identification_description varchar(13) NOT NULL,
	delivery_day_id int4 NOT NULL,
	trading_interval_start_id int4 NOT NULL,
	trading_interval_end_id int4 NULL,
	position_type varchar(10) NOT NULL,
	price numeric(18, 6) NULL,
	quantity numeric(18, 6) NOT NULL,
	reminder_of_quantity numeric(18, 6) NOT NULL,
	trade_date_id int4 NOT NULL,
	product_id int4 NOT NULL,
	market_price_id int4 NOT NULL,
	mtm numeric(18, 6) NULL,
	mtm_previous_year numeric(18, 6) DEFAULT 0 NOT NULL,
	mtm_previous_day numeric(18, 6) DEFAULT 0 NOT NULL,
	trade_closed bool NOT NULL,
	created_at timestamptz DEFAULT now() NULL,
	created_by varchar(255) NULL,
	modified_at timestamptz DEFAULT now() NULL,
	modified_by varchar(255) NULL,
	CONSTRAINT mtm_company_pkey PRIMARY KEY (id),
	CONSTRAINT fk_mtm_calculation_date_id FOREIGN KEY (calculation_date_id) REFERENCES trading_dw.dim_date(id),
	CONSTRAINT fk_mtm_company_delivery_day_id FOREIGN KEY (delivery_day_id) REFERENCES trading_dw.dim_date(id),
	CONSTRAINT fk_mtm_company_market_price_id FOREIGN KEY (market_price_id) REFERENCES trading_dw.prices(id),
	CONSTRAINT fk_mtm_company_product_id FOREIGN KEY (product_id) REFERENCES trading_dw.product(id),
	CONSTRAINT fk_mtm_company_trading_interval_end_id FOREIGN KEY (trading_interval_end_id) REFERENCES trading_dw.dim_time(id),
	CONSTRAINT fk_mtm_company_trading_interval_start_id FOREIGN KEY (trading_interval_start_id) REFERENCES trading_dw.dim_time(id)
);


-- trading_dw.mtm_portfolio definition

-- Drop table

-- DROP TABLE trading_dw.mtm_portfolio;

CREATE TABLE trading_dw.mtm_portfolio (
	id serial4 NOT NULL,
	calculation_date_id int4 NOT NULL,
	portfolio_id int4 NOT NULL,
	identification_description varchar(13) NOT NULL,
	delivery_day_id int4 NOT NULL,
	trading_interval_start_id int4 NOT NULL,
	trading_interval_end_id int4 NULL,
	position_type varchar(10) NOT NULL,
	price numeric(18, 6) NULL,
	quantity numeric(18, 6) NOT NULL,
	reminder_of_quantity numeric(18, 6) NOT NULL,
	trade_date_id int4 NOT NULL,
	product_id int4 NOT NULL,
	market_price_id int4 NOT NULL,
	mtm numeric(18, 6) NULL,
	mtm_previous_year numeric(18, 6) DEFAULT 0 NOT NULL,
	mtm_previous_day numeric(18, 6) DEFAULT 0 NOT NULL,
	trade_closed bool NOT NULL,
	created_at timestamptz DEFAULT now() NULL,
	created_by varchar(255) NULL,
	modified_at timestamptz DEFAULT now() NULL,
	modified_by varchar(255) NULL,
	CONSTRAINT mtm_portfolio_pkey PRIMARY KEY (id),
	CONSTRAINT fk_mtm_portfoio_trading_interval_end_id FOREIGN KEY (trading_interval_end_id) REFERENCES trading_dw.dim_time(id),
	CONSTRAINT fk_mtm_portfolio_calculation_date_id FOREIGN KEY (calculation_date_id) REFERENCES trading_dw.dim_date(id),
	CONSTRAINT fk_mtm_portfolio_delivery_day_id FOREIGN KEY (delivery_day_id) REFERENCES trading_dw.dim_date(id),
	CONSTRAINT fk_mtm_portfolio_id FOREIGN KEY (portfolio_id) REFERENCES trading_dw.portfolio(id),
	CONSTRAINT fk_mtm_portfolio_market_price_id FOREIGN KEY (market_price_id) REFERENCES trading_dw.prices(id),
	CONSTRAINT fk_mtm_portfolio_product_id FOREIGN KEY (product_id) REFERENCES trading_dw.product(id),
	CONSTRAINT fk_mtm_portfolio_trading_interval_start_id FOREIGN KEY (trading_interval_start_id) REFERENCES trading_dw.dim_time(id)
);


-- trading_dw.mtm_trader definition

-- Drop table

-- DROP TABLE trading_dw.mtm_trader;

CREATE TABLE trading_dw.mtm_trader (
	id serial4 NOT NULL,
	calculation_date_id int4 NOT NULL,
	trader_id int4 NOT NULL,
	identification_description varchar(13) NOT NULL,
	delivery_day_id int4 NOT NULL,
	trading_interval_start_id int4 NOT NULL,
	trading_interval_end_id int4 NULL,
	position_type varchar(10) NOT NULL,
	price numeric(18, 6) NULL,
	quantity numeric(18, 6) NOT NULL,
	reminder_of_quantity numeric(18, 6) NOT NULL,
	trade_date_id int4 NOT NULL,
	product_id int4 NOT NULL,
	market_price_id int4 NOT NULL,
	mtm numeric(18, 6) NULL,
	mtm_previous_year numeric(18, 6) DEFAULT 0 NOT NULL,
	mtm_previous_day numeric(18, 6) DEFAULT 0 NOT NULL,
	trade_closed bool NOT NULL,
	created_at timestamptz DEFAULT now() NULL,
	created_by varchar(255) NULL,
	modified_at timestamptz DEFAULT now() NULL,
	modified_by varchar(255) NULL,
	CONSTRAINT mtm_trader_pkey PRIMARY KEY (id),
	CONSTRAINT fk_mtm_calculation_date_id FOREIGN KEY (calculation_date_id) REFERENCES trading_dw.dim_date(id),
	CONSTRAINT fk_mtm_trader_delivery_day_id FOREIGN KEY (delivery_day_id) REFERENCES trading_dw.dim_date(id),
	CONSTRAINT fk_mtm_trader_market_price_id FOREIGN KEY (market_price_id) REFERENCES trading_dw.prices(id),
	CONSTRAINT fk_mtm_trader_product_id FOREIGN KEY (product_id) REFERENCES trading_dw.product(id),
	CONSTRAINT fk_mtm_trader_trader_id FOREIGN KEY (trader_id) REFERENCES trading_dw.trader(id),
	CONSTRAINT fk_mtm_trader_trading_interval_end_id FOREIGN KEY (trading_interval_end_id) REFERENCES trading_dw.dim_time(id),
	CONSTRAINT fk_mtm_trader_trading_interval_start_id FOREIGN KEY (trading_interval_start_id) REFERENCES trading_dw.dim_time(id)
);


-- trading_dw.trade_prices definition

-- Drop table

-- DROP TABLE trading_dw.trade_prices;

CREATE TABLE trading_dw.trade_prices (
	id serial4 NOT NULL,
	identification_description varchar(13) NOT NULL,
	delivery_day_id int4 NOT NULL,
	quantity numeric(18, 6) NOT NULL,
	price numeric(18, 6) NULL,
	value numeric(18, 6) NOT NULL,
	netting varchar(3) NOT NULL,
	trading_interval_id int4 NULL,
	reserved_flag bool DEFAULT false NOT NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	created_by varchar(100) DEFAULT 'System'::character varying NULL,
	modified_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	modified_by varchar(100) DEFAULT 'System'::character varying NULL,
	is_deleted bool DEFAULT false NULL,
	CONSTRAINT trade_prices_pkey PRIMARY KEY (id),
	CONSTRAINT fk_trade_prices_delivery_day_id FOREIGN KEY (delivery_day_id) REFERENCES trading_dw.dim_date(id),
	CONSTRAINT fk_trade_prices_trading_interval_id FOREIGN KEY (trading_interval_id) REFERENCES trading_dw.trading_interval(id),
	CONSTRAINT trade_prices_trade_fk FOREIGN KEY (identification_description) REFERENCES trading_dw.trade(identification_description) ON DELETE CASCADE ON UPDATE CASCADE
);
CREATE INDEX "IX_TRADE_PRICES_IDENTIFICATION_DESCRIPTION" ON trading_dw.trade_prices USING btree (identification_description) WITH (deduplicate_items='true');


-- trading_dw.v_basic_trade source

CREATE OR REPLACE VIEW trading_dw.v_basic_trade
AS SELECT t.identification_description AS trade,
    (t.transaction_time AT TIME ZONE 'Europe/Ljubljana'::text) AS transaction_time,
    t_trade_date.date AS trade_date,
    t.position_type,
    min(dt.date) AS delivery_day,
    min(ti_start_time.date_hour_minute_start) AS delivery_start,
    p.name AS product_name,
    p.id AS product_id,
    sum(
        CASE
            WHEN t.position_type::text = 'Sell'::text THEN tp.quantity * '-1'::integer::numeric
            ELSE tp.quantity
        END) AS quantity,
    sum(
        CASE
            WHEN t.position_type::text = 'Sell'::text THEN tp.value * '-1'::integer::numeric
            ELSE tp.value
        END) AS value,
        CASE
            WHEN t.position_type::text = 'Sell'::text THEN cin.zone_name
            ELSE cout.zone_name
        END AS market_area,
        CASE
            WHEN t.position_type::text = 'Buy'::text THEN company.short_name
            ELSE counterparty.short_name
        END AS company_short_name,
        CASE
            WHEN t.position_type::text = 'Buy'::text THEN counterparty.short_name
            ELSE company.short_name
        END AS counterparty_short_name,
    b_1.book_name AS trade_book,
    tr.trader_name,
    pt.name AS trade_type,
    ''::text AS netting,
    t.general_agreement_contract_identification,
    c.currency_code AS currency
   FROM trading_dw.trade_prices tp
     JOIN trading_dw.trade t ON tp.identification_description::text = t.identification_description::text
     JOIN trading_dw.trader tr ON t.trader_id = tr.id
     JOIN trading_dw.dim_date dt ON tp.delivery_day_id = dt.id
     JOIN trading_dw.book b_1 ON t.trade_book_id = b_1.id
     JOIN trading_dw.product p ON t.product_id = p.id
     JOIN trading_dw.product_type pt ON p.product_type_id = pt.id
     JOIN trading_dw.partner company ON t.company_id = company.id
     JOIN trading_dw.partner counterparty ON t.counterparty_id = counterparty.id
     JOIN trading_dw.trading_interval ti ON ti.id = tp.trading_interval_id
     JOIN trading_dw.v_dim_date_met t_trade_date ON t_trade_date.id = t.trade_date_id
     JOIN trading_dw.v_dim_time_met ti_start_time ON ti_start_time.id = ti.start_time_id
     JOIN trading_dw.currency c ON c.id = t.currency_id
     JOIN trading_dw.control_area cin ON cin.id = t.in_control_area_id
     JOIN trading_dw.control_area cout ON cout.id = t.out_control_area_id
  GROUP BY t_trade_date.date, company.short_name, counterparty.short_name, t.position_type, cin.zone_name, cout.zone_name, b_1.book_name, tr.trader_name, p.name, p.id, t.identification_description, pt.name, t.general_agreement_contract_identification, c.currency_code;


-- trading_dw.v_book_realization_sum source

CREATE OR REPLACE VIEW trading_dw.v_book_realization_sum
AS WITH min_reminder_buy AS (
         SELECT a_1.identification_description_buy,
            a_1.identification_description_sell,
            a_1.trading_interval_start_id,
            min(a_1.quantity_reminder_buy) AS quantity_reminder_buy,
            min(a_1.quantity_reminder_sell) AS quantity_reminder_sell
           FROM trading_dw.realization_book a_1
          GROUP BY a_1.identification_description_buy, a_1.identification_description_sell, a_1.trading_interval_start_id
        ), sum_realization AS (
         SELECT a_1.identification_description_buy,
            a_1.identification_description_sell,
            a_1.trading_interval_start_id,
            sum(a_1.realization) AS realization
           FROM trading_dw.realization_book a_1
          GROUP BY a_1.identification_description_buy, a_1.identification_description_sell, a_1.trading_interval_start_id
        )
 SELECT bk.book_name,
    a.identification_description_buy,
    a.identification_description_sell,
    b.date AS trade_date_buy,
    c.date AS trade_date_sell,
    min(d.date_hour_minute_start) AS trading_interval_start,
    max(e.date_hour_minute_start) AS trading_interval_end_buy,
    max(f.date_hour_minute_start) AS trading_interval_end_sell,
    a.price_buy,
    a.price_sell,
    sum(a.quantity_buy) AS quantity_buy,
    sum(a.quantity_buy) AS quantity_sell,
    sum(mb.quantity_reminder_buy) AS quantity_reminder_buy,
    sum(mb.quantity_reminder_sell) AS quantity_reminder_sell,
    sum(r.realization) AS realization,
    a.trade_date_id_buy,
    a.trade_date_id_sell,
    g.name AS product_name_sell,
    h.name AS product_name_buy,
    a.buy_closed,
    a.sell_closed,
    min(a.trading_interval_start_id) AS trading_interval_start_id,
    max(a.trading_interval_end_id_buy) AS trading_interval_start_id_buy,
    max(a.trading_interval_end_id_sell) AS trading_interval_end_id_sell,
    a.product_id_buy,
    a.product_id_sell,
    a.book_id
   FROM trading_dw.realization_book a
     JOIN trading_dw.v_dim_date_met b ON a.trade_date_id_buy = b.id
     JOIN trading_dw.v_dim_date_met c ON a.trade_date_id_sell = c.id
     JOIN trading_dw.v_dim_time_met d ON a.trading_interval_start_id = d.id
     JOIN trading_dw.v_dim_time_met e ON a.trading_interval_end_id_buy = e.id
     JOIN trading_dw.v_dim_time_met f ON a.trading_interval_end_id_sell = f.id
     JOIN trading_dw.product g ON a.product_id_sell = g.id
     JOIN trading_dw.product h ON a.product_id_buy = h.id
     JOIN trading_dw.book bk ON a.book_id = bk.id
     JOIN min_reminder_buy mb ON a.identification_description_buy::text = mb.identification_description_buy::text AND a.identification_description_sell::text = mb.identification_description_sell::text AND a.trading_interval_start_id = mb.trading_interval_start_id
     JOIN sum_realization r ON a.identification_description_buy::text = r.identification_description_buy::text AND a.identification_description_sell::text = r.identification_description_sell::text AND a.trading_interval_start_id = r.trading_interval_start_id
  GROUP BY bk.book_name, a.identification_description_buy, a.identification_description_sell, b.date, c.date, a.price_buy, a.price_sell, a.trade_date_id_buy, a.trade_date_id_sell, g.name, h.name, a.buy_closed, a.sell_closed, a.product_id_buy, a.product_id_sell, a.book_id
  ORDER BY a.identification_description_buy, a.identification_description_sell, b.date, bk.book_name;


-- trading_dw.v_book_realization_sum_risk source

CREATE OR REPLACE VIEW trading_dw.v_book_realization_sum_risk
AS WITH min_reminder_buy AS (
         SELECT a_1.identification_description_buy,
            a_1.identification_description_sell,
            a_1.trading_interval_start_id,
            min(a_1.quantity_reminder_buy) AS quantity_reminder_buy,
            min(a_1.quantity_reminder_sell) AS quantity_reminder_sell
           FROM trading_dw.realization_book a_1
          GROUP BY a_1.identification_description_buy, a_1.identification_description_sell, a_1.trading_interval_start_id
        ), sum_realization AS (
         SELECT a_1.identification_description_buy,
            a_1.identification_description_sell,
            a_1.trading_interval_start_id,
            sum(a_1.realization) AS realization
           FROM trading_dw.realization_book a_1
          GROUP BY a_1.identification_description_buy, a_1.identification_description_sell, a_1.trading_interval_start_id
        )
 SELECT a.identification_description_buy,
    a.identification_description_sell,
    cm.name AS commodity,
    trader.trader_name AS trader_name_buy,
    trader_sell.trader_name AS trader_name_sell,
    book.book_name AS book_name_buy,
    book_sell.book_name AS book_name_sell,
    company.short_name AS company_short_name_buy,
    counterparty.short_name AS counterparty_short_name_buy,
    company_sell.short_name AS company_short_name_sell,
    counterparty_sell.short_name AS counterparty_short_name_sell,
    cout.zone_name AS market_area_buy,
    cin.zone_name AS market_area_sell,
    b.date AS trade_date_buy,
    c.date AS trade_date_sell,
    min(d.date_hour_minute_start) AS trading_interval_start,
    max(e.date_hour_minute_start) AS trading_interval_end_buy,
    max(f.date_hour_minute_start) AS trading_interval_end_sell,
    a.price_buy,
    a.price_sell,
    sum(a.quantity_buy) AS quantity_buy,
    sum(a.quantity_buy) AS quantity_sell,
    sum(mb.quantity_reminder_buy) AS quantity_reminder_buy,
    sum(mb.quantity_reminder_sell) AS quantity_reminder_sell,
    sum(r.realization) AS realization,
    a.trade_date_id_buy,
    a.trade_date_id_sell,
    g.name AS product_name_sell,
    h.name AS product_name_buy,
    a.buy_closed,
    a.sell_closed,
    min(a.trading_interval_start_id) AS trading_interval_start_id,
    max(a.trading_interval_end_id_buy) AS trading_interval_start_id_buy,
    max(a.trading_interval_end_id_sell) AS trading_interval_end_id_sell,
    a.product_id_buy,
    a.product_id_sell
   FROM trading_dw.realization_book a
     JOIN trading_dw.v_dim_date_met b ON a.trade_date_id_buy = b.id
     JOIN trading_dw.v_dim_date_met c ON a.trade_date_id_sell = c.id
     JOIN trading_dw.v_dim_time_met d ON a.trading_interval_start_id = d.id
     JOIN trading_dw.v_dim_time_met e ON a.trading_interval_end_id_buy = e.id
     JOIN trading_dw.v_dim_time_met f ON a.trading_interval_end_id_sell = f.id
     JOIN trading_dw.product g ON a.product_id_sell = g.id
     JOIN trading_dw.product h ON a.product_id_buy = h.id
     JOIN trading_dw.trade t_buy ON t_buy.identification_description::text = a.identification_description_buy::text
     JOIN trading_dw.trade t_sell ON t_sell.identification_description::text = a.identification_description_sell::text
     JOIN trading_dw.trader trader ON t_buy.trader_id = trader.id
     JOIN trading_dw.trader trader_sell ON t_sell.trader_id = trader_sell.id
     JOIN trading_dw.book book ON t_buy.trade_book_id = book.id
     JOIN trading_dw.book book_sell ON t_sell.trade_book_id = book_sell.id
     JOIN trading_dw.partner company ON t_buy.company_id = company.id
     JOIN trading_dw.partner company_sell ON t_sell.company_id = company_sell.id
     JOIN trading_dw.partner counterparty ON t_buy.counterparty_id = counterparty.id
     JOIN trading_dw.partner counterparty_sell ON t_sell.counterparty_id = counterparty_sell.id
     JOIN trading_dw.control_area cin ON cin.id = t_buy.in_control_area_id
     JOIN trading_dw.control_area cout ON cout.id = t_sell.out_control_area_id
     JOIN trading_dw.commodity cm ON h.commodity_id = cm.id
     JOIN min_reminder_buy mb ON a.identification_description_buy::text = mb.identification_description_buy::text AND a.identification_description_sell::text = mb.identification_description_sell::text AND a.trading_interval_start_id = mb.trading_interval_start_id
     JOIN sum_realization r ON a.identification_description_buy::text = r.identification_description_buy::text AND a.identification_description_sell::text = r.identification_description_sell::text AND a.trading_interval_start_id = r.trading_interval_start_id
  GROUP BY a.identification_description_buy, a.identification_description_sell, b.date, c.date, a.price_buy, a.price_sell, a.trade_date_id_buy, a.trade_date_id_sell, g.name, h.name, a.buy_closed, a.sell_closed, a.product_id_buy, a.product_id_sell, cm.name, trader.trader_name, book.book_name, company.short_name, counterparty.short_name, cout.zone_name, trader_sell.trader_name, book_sell.book_name, company_sell.short_name, counterparty_sell.short_name, cin.zone_name
  ORDER BY a.identification_description_buy, a.identification_description_sell, c.date;


-- trading_dw.v_calculation_history source

CREATE OR REPLACE VIEW trading_dw.v_calculation_history
AS SELECT vddm.date AS calculation_date,
    cv.value AS calculation_type,
    cv_1.value AS calculation_code,
    p.short_name AS calc_object,
    ch.value AS calculated_value
   FROM trading_dw.calculation_history ch
     JOIN trading_dw.v_dim_date_met vddm ON vddm.id = ch.calculation_date_id
     JOIN trading_dw.code_value cv ON cv.id = ch.calculation_type_id
     JOIN trading_dw.code_value cv_1 ON cv_1.id = ch.calculation_code_id
     JOIN trading_dw.partner p ON ch.object_id = p.id
  WHERE ch.calculation_code_id = 4
UNION ALL
 SELECT vddm.date AS calculation_date,
    cv.value AS calculation_type,
    cv_1.value AS calculation_code,
    p.trader_name AS calc_object,
    ch.value AS calculated_value
   FROM trading_dw.calculation_history ch
     JOIN trading_dw.v_dim_date_met vddm ON vddm.id = ch.calculation_date_id
     JOIN trading_dw.code_value cv ON cv.id = ch.calculation_type_id
     JOIN trading_dw.code_value cv_1 ON cv_1.id = ch.calculation_code_id
     JOIN trading_dw.trader p ON ch.object_id = p.id
  WHERE ch.calculation_code_id = 5
UNION ALL
 SELECT vddm.date AS calculation_date,
    cv.value AS calculation_type,
    cv_1.value AS calculation_code,
    p.book_name AS calc_object,
    ch.value AS calculated_value
   FROM trading_dw.calculation_history ch
     JOIN trading_dw.v_dim_date_met vddm ON vddm.id = ch.calculation_date_id
     JOIN trading_dw.code_value cv ON cv.id = ch.calculation_type_id
     JOIN trading_dw.code_value cv_1 ON cv_1.id = ch.calculation_code_id
     JOIN trading_dw.book p ON ch.object_id = p.id
  WHERE ch.calculation_code_id = 6
UNION ALL
 SELECT vddm.date AS calculation_date,
    cv.value AS calculation_type,
    cv_1.value AS calculation_code,
    p.portfolio_name AS calc_object,
    ch.value AS calculated_value
   FROM trading_dw.calculation_history ch
     JOIN trading_dw.v_dim_date_met vddm ON vddm.id = ch.calculation_date_id
     JOIN trading_dw.code_value cv ON cv.id = ch.calculation_type_id
     JOIN trading_dw.code_value cv_1 ON cv_1.id = ch.calculation_code_id
     JOIN trading_dw.portfolio p ON ch.object_id = p.id
  WHERE ch.calculation_code_id = 7
  ORDER BY 1, 2, 3, 4;


-- trading_dw.v_check_sum_trades source

CREATE OR REPLACE VIEW trading_dw.v_check_sum_trades
AS SELECT a.product_name,
    a.position_type,
    a.delivery_start,
    sum(a.quantity) AS sum_quantity,
    count(*) AS num_of_trades
   FROM trading_dw.v_basic_trade a
  GROUP BY a.product_name, a.position_type, a.delivery_start
  ORDER BY a.product_name, a.delivery_start, a.position_type;


-- trading_dw.v_company_realization_sum source

CREATE OR REPLACE VIEW trading_dw.v_company_realization_sum
AS WITH min_reminder_buy AS (
         SELECT a_1.identification_description_buy,
            a_1.identification_description_sell,
            a_1.trading_interval_start_id,
            min(a_1.quantity_reminder_buy) AS quantity_reminder_buy,
            min(a_1.quantity_reminder_sell) AS quantity_reminder_sell
           FROM trading_dw.realization_company a_1
          GROUP BY a_1.identification_description_buy, a_1.identification_description_sell, a_1.trading_interval_start_id
        ), sum_realization AS (
         SELECT a_1.identification_description_buy,
            a_1.identification_description_sell,
            a_1.trading_interval_start_id,
            sum(a_1.realization) AS realization
           FROM trading_dw.realization_company a_1
          GROUP BY a_1.identification_description_buy, a_1.identification_description_sell, a_1.trading_interval_start_id
        )
 SELECT a.identification_description_buy,
    a.identification_description_sell,
    b.date AS trade_date_buy,
    c.date AS trade_date_sell,
    min(d.date_hour_minute_start) AS trading_interval_start,
    max(e.date_hour_minute_start) AS trading_interval_end_buy,
    max(f.date_hour_minute_start) AS trading_interval_end_sell,
    a.price_buy,
    a.price_sell,
    sum(a.quantity_buy) AS quantity_buy,
    sum(a.quantity_buy) AS quantity_sell,
    sum(mb.quantity_reminder_buy) AS quantity_reminder_buy,
    sum(mb.quantity_reminder_sell) AS quantity_reminder_sell,
    sum(r.realization) AS realization,
    a.trade_date_id_buy,
    a.trade_date_id_sell,
    g.name AS product_name_sell,
    h.name AS product_name_buy,
    a.buy_closed,
    a.sell_closed,
    min(a.trading_interval_start_id) AS trading_interval_start_id,
    max(a.trading_interval_end_id_buy) AS trading_interval_start_id_buy,
    max(a.trading_interval_end_id_sell) AS trading_interval_end_id_sell,
    a.product_id_buy,
    a.product_id_sell
   FROM trading_dw.realization_company a
     JOIN trading_dw.v_dim_date_met b ON a.trade_date_id_buy = b.id
     JOIN trading_dw.v_dim_date_met c ON a.trade_date_id_sell = c.id
     JOIN trading_dw.v_dim_time_met d ON a.trading_interval_start_id = d.id
     JOIN trading_dw.v_dim_time_met e ON a.trading_interval_end_id_buy = e.id
     JOIN trading_dw.v_dim_time_met f ON a.trading_interval_end_id_sell = f.id
     JOIN trading_dw.product g ON a.product_id_sell = g.id
     JOIN trading_dw.product h ON a.product_id_buy = h.id
     JOIN min_reminder_buy mb ON a.identification_description_buy::text = mb.identification_description_buy::text AND a.identification_description_sell::text = mb.identification_description_sell::text AND a.trading_interval_start_id = mb.trading_interval_start_id
     JOIN sum_realization r ON a.identification_description_buy::text = r.identification_description_buy::text AND a.identification_description_sell::text = r.identification_description_sell::text AND a.trading_interval_start_id = r.trading_interval_start_id
  GROUP BY a.identification_description_buy, a.identification_description_sell, b.date, c.date, a.price_buy, a.price_sell, a.trade_date_id_buy, a.trade_date_id_sell, g.name, h.name, a.buy_closed, a.sell_closed, a.product_id_buy, a.product_id_sell
  ORDER BY a.identification_description_buy, a.identification_description_sell, c.date;


-- trading_dw.v_company_realization_sum_risk source

CREATE OR REPLACE VIEW trading_dw.v_company_realization_sum_risk
AS WITH min_reminder_buy AS (
         SELECT a_1.identification_description_buy,
            a_1.identification_description_sell,
            a_1.trading_interval_start_id,
            min(a_1.quantity_reminder_buy) AS quantity_reminder_buy,
            min(a_1.quantity_reminder_sell) AS quantity_reminder_sell
           FROM trading_dw.realization_company a_1
          GROUP BY a_1.identification_description_buy, a_1.identification_description_sell, a_1.trading_interval_start_id
        ), sum_realization AS (
         SELECT a_1.identification_description_buy,
            a_1.identification_description_sell,
            a_1.trading_interval_start_id,
            sum(a_1.realization) AS realization
           FROM trading_dw.realization_company a_1
          GROUP BY a_1.identification_description_buy, a_1.identification_description_sell, a_1.trading_interval_start_id
        )
 SELECT a.identification_description_buy,
    a.identification_description_sell,
    cm.name AS commodity,
    trader.trader_name AS trader_name_buy,
    trader_sell.trader_name AS trader_name_sell,
    book.book_name AS book_name_buy,
    book_sell.book_name AS book_name_sell,
    company.short_name AS company_short_name_buy,
    counterparty.short_name AS counterparty_short_name_buy,
    company_sell.short_name AS company_short_name_sell,
    counterparty_sell.short_name AS counterparty_short_name_sell,
    cout.zone_name AS market_area_buy,
    cin.zone_name AS market_area_sell,
    b.date AS trade_date_buy,
    c.date AS trade_date_sell,
    min(d.date_hour_minute_start) AS trading_interval_start,
    max(e.date_hour_minute_start) AS trading_interval_end_buy,
    max(f.date_hour_minute_start) AS trading_interval_end_sell,
    a.price_buy,
    a.price_sell,
    sum(a.quantity_buy) AS quantity_buy,
    sum(a.quantity_buy) AS quantity_sell,
    sum(mb.quantity_reminder_buy) AS quantity_reminder_buy,
    sum(mb.quantity_reminder_sell) AS quantity_reminder_sell,
    sum(r.realization) AS realization,
    a.trade_date_id_buy,
    a.trade_date_id_sell,
    g.name AS product_name_sell,
    h.name AS product_name_buy,
    a.buy_closed,
    a.sell_closed,
    min(a.trading_interval_start_id) AS trading_interval_start_id,
    max(a.trading_interval_end_id_buy) AS trading_interval_start_id_buy,
    max(a.trading_interval_end_id_sell) AS trading_interval_end_id_sell,
    a.product_id_buy,
    a.product_id_sell
   FROM trading_dw.realization_company a
     JOIN trading_dw.v_dim_date_met b ON a.trade_date_id_buy = b.id
     JOIN trading_dw.v_dim_date_met c ON a.trade_date_id_sell = c.id
     JOIN trading_dw.v_dim_time_met d ON a.trading_interval_start_id = d.id
     JOIN trading_dw.v_dim_time_met e ON a.trading_interval_end_id_buy = e.id
     JOIN trading_dw.v_dim_time_met f ON a.trading_interval_end_id_sell = f.id
     JOIN trading_dw.product g ON a.product_id_sell = g.id
     JOIN trading_dw.product h ON a.product_id_buy = h.id
     JOIN trading_dw.trade t_buy ON t_buy.identification_description::text = a.identification_description_buy::text
     JOIN trading_dw.trade t_sell ON t_sell.identification_description::text = a.identification_description_sell::text
     JOIN trading_dw.trader trader ON t_buy.trader_id = trader.id
     JOIN trading_dw.trader trader_sell ON t_sell.trader_id = trader_sell.id
     JOIN trading_dw.book book ON t_buy.trade_book_id = book.id
     JOIN trading_dw.book book_sell ON t_sell.trade_book_id = book_sell.id
     JOIN trading_dw.partner company ON t_buy.company_id = company.id
     JOIN trading_dw.partner company_sell ON t_sell.company_id = company_sell.id
     JOIN trading_dw.partner counterparty ON t_buy.counterparty_id = counterparty.id
     JOIN trading_dw.partner counterparty_sell ON t_sell.counterparty_id = counterparty_sell.id
     JOIN trading_dw.control_area cin ON cin.id = t_buy.in_control_area_id
     JOIN trading_dw.control_area cout ON cout.id = t_sell.out_control_area_id
     JOIN trading_dw.commodity cm ON h.commodity_id = cm.id
     JOIN min_reminder_buy mb ON a.identification_description_buy::text = mb.identification_description_buy::text AND a.identification_description_sell::text = mb.identification_description_sell::text AND a.trading_interval_start_id = mb.trading_interval_start_id
     JOIN sum_realization r ON a.identification_description_buy::text = r.identification_description_buy::text AND a.identification_description_sell::text = r.identification_description_sell::text AND a.trading_interval_start_id = r.trading_interval_start_id
  GROUP BY a.identification_description_buy, a.identification_description_sell, b.date, c.date, a.price_buy, a.price_sell, a.trade_date_id_buy, a.trade_date_id_sell, g.name, h.name, a.buy_closed, a.sell_closed, a.product_id_buy, a.product_id_sell, cm.name, trader.trader_name, book.book_name, company.short_name, counterparty.short_name, cout.zone_name, trader_sell.trader_name, book_sell.book_name, company_sell.short_name, counterparty_sell.short_name, cin.zone_name
  ORDER BY a.identification_description_buy, a.identification_description_sell, c.date;


-- trading_dw.v_dim_date_met source

CREATE OR REPLACE VIEW trading_dw.v_dim_date_met
AS WITH daylight_savings AS (
         SELECT t.winter_to_summer_switch,
            t.summer_to_winter_switch
           FROM ( VALUES ('2020-03-29'::date,'2020-10-25'::date), ('2021-03-28'::date,'2021-10-31'::date), ('2022-03-27'::date,'2022-10-30'::date), ('2023-03-26'::date,'2023-10-29'::date), ('2024-03-31'::date,'2024-10-27'::date), ('2025-03-30'::date,'2025-10-26'::date), ('2026-03-29'::date,'2026-10-25'::date), ('2027-03-28'::date,'2027-10-31'::date), ('2028-03-26'::date,'2028-10-29'::date), ('2029-03-25'::date,'2029-10-28'::date), ('2030-03-31'::date,'2030-10-27'::date), ('2031-03-30'::date,'2031-10-26'::date), ('2032-03-28'::date,'2032-10-31'::date), ('2033-03-27'::date,'2033-10-30'::date), ('2034-03-26'::date,'2034-10-29'::date), ('2035-03-25'::date,'2035-10-28'::date), ('2036-03-30'::date,'2036-10-26'::date), ('2037-03-29'::date,'2037-10-25'::date), ('2038-03-28'::date,'2038-10-31'::date), ('2039-03-27'::date,'2039-10-30'::date), ('2040-03-31'::date,'2040-10-28'::date)) t(winter_to_summer_switch, summer_to_winter_switch)
        )
 SELECT dim_date.id,
    (dim_date.date AT TIME ZONE 'CET'::text)::date AS date,
    date_part('year'::text, (dim_date.date AT TIME ZONE 'CET'::text))::integer AS year,
        CASE
            WHEN date_part('month'::text, (dim_date.date AT TIME ZONE 'CET'::text)) <= 6::double precision THEN 'H1'::text
            ELSE 'H2'::text
        END AS half_year,
        CASE
            WHEN date_part('month'::text, (dim_date.date AT TIME ZONE 'CET'::text)) <= 6::double precision THEN 1
            ELSE 2
        END AS half_year_number,
    'Q'::text || date_part('quarter'::text, (dim_date.date AT TIME ZONE 'CET'::text))::integer AS quarter,
    date_part('quarter'::text, (dim_date.date AT TIME ZONE 'CET'::text))::integer AS quarter_number,
    'M'::text || lpad(date_part('month'::text, (dim_date.date AT TIME ZONE 'CET'::text))::text, 2, '0'::text) AS month,
    date_part('month'::text, (dim_date.date AT TIME ZONE 'CET'::text))::integer AS month_number,
    to_char((dim_date.date AT TIME ZONE 'CET'::text), 'Month'::text) AS month_name,
    'W'::text || lpad(date_part('week'::text, (dim_date.date AT TIME ZONE 'CET'::text))::text, 2, '0'::text) AS week_iso,
    'EW'::text || lpad(date_part('week'::text, (dim_date.date AT TIME ZONE 'CET'::text))::text, 2, '0'::text) AS week_eex,
    date_part('day'::text, (dim_date.date AT TIME ZONE 'CET'::text))::integer AS day_of_month,
    to_char((dim_date.date AT TIME ZONE 'CET'::text), 'Day'::text) AS day_of_week,
    date_part('isodow'::text, (dim_date.date AT TIME ZONE 'CET'::text)) = ANY (ARRAY[6::double precision, 7::double precision]) AS is_weekend,
        CASE
            WHEN ((dim_date.date AT TIME ZONE 'CET'::text)::date IN ( SELECT daylight_savings.winter_to_summer_switch
               FROM daylight_savings)) THEN 'WinterToSummerSwitch'::text
            WHEN ((dim_date.date AT TIME ZONE 'CET'::text)::date IN ( SELECT daylight_savings.summer_to_winter_switch
               FROM daylight_savings)) THEN 'SummerToWinterSwitch'::text
            WHEN (EXISTS ( SELECT 1
               FROM daylight_savings
              WHERE (dim_date.date AT TIME ZONE 'CET'::text)::date >= daylight_savings.winter_to_summer_switch AND (dim_date.date AT TIME ZONE 'CET'::text)::date <= daylight_savings.summer_to_winter_switch)) THEN 'SummerTime'::text
            ELSE 'WinterTime'::text
        END AS summer_time,
    dim_date.created_at,
    dim_date.created_by,
    dim_date.modified_at,
    dim_date.modified_by,
    dim_date.is_deleted
   FROM trading_dw.dim_date;


-- trading_dw.v_dim_time_met source

CREATE OR REPLACE VIEW trading_dw.v_dim_time_met
AS WITH dst_transitions AS (
         VALUES ('2020-03-29'::date,'2020-10-25'::date), ('2021-03-28'::date,'2021-10-31'::date), ('2022-03-27'::date,'2022-10-30'::date), ('2023-03-26'::date,'2023-10-29'::date), ('2024-03-31'::date,'2024-10-27'::date), ('2025-03-30'::date,'2025-10-26'::date), ('2026-03-29'::date,'2026-10-25'::date), ('2027-03-28'::date,'2027-10-31'::date), ('2028-03-26'::date,'2028-10-29'::date), ('2029-03-25'::date,'2029-10-28'::date), ('2030-03-31'::date,'2030-10-27'::date), ('2031-03-30'::date,'2031-10-26'::date), ('2032-03-28'::date,'2032-10-31'::date), ('2033-03-27'::date,'2033-10-30'::date), ('2034-03-26'::date,'2034-10-29'::date), ('2035-03-25'::date,'2035-10-28'::date), ('2036-03-30'::date,'2036-10-26'::date), ('2037-03-29'::date,'2037-10-25'::date), ('2038-03-28'::date,'2038-10-31'::date), ('2039-03-27'::date,'2039-10-30'::date), ('2040-03-31'::date,'2040-10-28'::date)
        )
 SELECT dim_time.id,
    (dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text)::date AS date,
        CASE
            WHEN EXTRACT(hour FROM (dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text)) = 0::numeric THEN 24::numeric
            ELSE EXTRACT(hour FROM (dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text))
        END::smallint AS hour_interval,
    to_char((dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text), 'DD.MM.YYYY HH24'::text) AS date_hour_start,
    to_char((dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text), 'DD.MM.YYYY HH24'::text) AS date_hour_end,
    (((to_char((dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text), 'DD.MM.YYYY HH24'::text) || ' '::text) || to_char((dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text), 'HH24:MI'::text)) || '-'::text) || to_char((dim_time.date_hour_minute_end AT TIME ZONE 'Europe/Ljubljana'::text), 'HH24:MI'::text) AS date_hour_minute_start_end,
    (dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text)::time without time zone AS hour_minute_start,
    (dim_time.date_hour_minute_end AT TIME ZONE 'Europe/Ljubljana'::text)::time without time zone AS hour_minute_end,
    (to_char((dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text), 'HH24:MI'::text) || '-'::text) || to_char((dim_time.date_hour_minute_end AT TIME ZONE 'Europe/Ljubljana'::text), 'HH24:MI'::text) AS hour_minute_start_end,
    EXTRACT(minute FROM (dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text))::smallint AS minute_start,
    EXTRACT(minute FROM (dim_time.date_hour_minute_end AT TIME ZONE 'Europe/Ljubljana'::text))::smallint AS minute_end,
    (to_char((dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text), 'MI'::text) || '-'::text) || to_char((dim_time.date_hour_minute_end AT TIME ZONE 'Europe/Ljubljana'::text), 'MI'::text) AS minute_start_end,
    EXTRACT(year FROM (dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text))::integer AS year,
        CASE
            WHEN EXTRACT(month FROM (dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text)) <= 6::numeric THEN 'H1'::text
            ELSE 'H2'::text
        END AS half_year,
        CASE
            WHEN EXTRACT(month FROM (dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text)) <= 6::numeric THEN 1
            ELSE 2
        END AS half_year_number,
        CASE
            WHEN EXTRACT(month FROM (dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text)) = ANY (ARRAY[1::numeric, 2::numeric, 3::numeric]) THEN 'Q1'::text
            WHEN EXTRACT(month FROM (dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text)) = ANY (ARRAY[4::numeric, 5::numeric, 6::numeric]) THEN 'Q2'::text
            WHEN EXTRACT(month FROM (dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text)) = ANY (ARRAY[7::numeric, 8::numeric, 9::numeric]) THEN 'Q3'::text
            ELSE 'Q4'::text
        END AS quarter,
        CASE
            WHEN EXTRACT(month FROM (dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text)) = ANY (ARRAY[1::numeric, 2::numeric, 3::numeric]) THEN 1
            WHEN EXTRACT(month FROM (dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text)) = ANY (ARRAY[4::numeric, 5::numeric, 6::numeric]) THEN 2
            WHEN EXTRACT(month FROM (dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text)) = ANY (ARRAY[7::numeric, 8::numeric, 9::numeric]) THEN 3
            ELSE 4
        END AS quarter_number,
    'M'::text || to_char((dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text), 'MM'::text) AS month,
    EXTRACT(month FROM (dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text))::smallint AS month_number,
    to_char((dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text), 'Month'::text) AS month_name,
    'W'::text || to_char(to_number(to_char((dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text), 'IW'::text), '999'::text), 'FM999'::text) AS week_iso,
    'EW'::text || to_char(to_number(to_char((dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text), 'IW'::text), '999'::text), 'FM999'::text) AS week_eex,
    EXTRACT(day FROM (dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text))::smallint AS day_of_month,
    EXTRACT(isodow FROM (dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text))::character varying AS day_of_week,
    EXTRACT(isodow FROM (dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text)) = ANY (ARRAY[6::numeric, 7::numeric]) AS is_weekend,
    dim_time.created_at,
    dim_time.created_by,
    dim_time.modified_at,
    dim_time.modified_by,
    dim_time.is_deleted,
    (dim_time.date_hour_minute_start AT TIME ZONE 'Europe/Ljubljana'::text) AS date_hour_minute_start,
    (dim_time.date_hour_minute_end AT TIME ZONE 'Europe/Ljubljana'::text) AS date_hour_minute_end
   FROM trading_dw.dim_time
  WHERE dim_time.date_hour_minute_start >= '2020-01-01 00:00:00'::timestamp without time zone;


-- trading_dw.v_limit_groups source

CREATE OR REPLACE VIEW trading_dw.v_limit_groups
AS WITH dummy_list AS (
         SELECT c.country_code2 AS area,
            d.year
           FROM trading_dw.country c
             CROSS JOIN ( SELECT DISTINCT date_part('year'::text, dd.date) AS year
                   FROM trading_dw.trade t
                     JOIN trading_dw.v_dim_date_met dd ON t.trade_date_id = dd.id) d
        )
 SELECT DISTINCT t.trader_name AS subject_name,
    l.name,
    lg.name AS "group",
    lt.name AS type,
        CASE
            WHEN l.time_period IS NULL THEN dl.year
            ELSE l.time_period::integer::double precision
        END AS time_period,
        CASE
            WHEN l.area IS NULL THEN dl.area::character varying
            ELSE l.area
        END AS area,
    l.unit,
    l.limit_value,
    l.subject_id,
    l.product_type_id
   FROM trading_dw."limit" l
     JOIN trading_dw.limit_group lg ON lg.id = l.limit_group_id
     JOIN trading_dw.limit_type lt ON lt.id = l.limit_type_id
     JOIN trading_dw.trader t ON l.subject_id = t.id AND lg.name::text = 'trader'::text
     CROSS JOIN dummy_list dl
UNION
 SELECT DISTINCT p.portfolio_name AS subject_name,
    l.name,
    lg.name AS "group",
    lt.name AS type,
        CASE
            WHEN l.time_period IS NULL THEN dl.year
            ELSE l.time_period::integer::double precision
        END AS time_period,
        CASE
            WHEN l.area IS NULL THEN dl.area::character varying
            ELSE l.area
        END AS area,
    l.unit,
    l.limit_value,
    l.subject_id,
    l.product_type_id
   FROM trading_dw."limit" l
     JOIN trading_dw.limit_group lg ON lg.id = l.limit_group_id
     JOIN trading_dw.limit_type lt ON lt.id = l.limit_type_id
     JOIN trading_dw.portfolio p ON l.subject_id = p.id AND lg.name::text = 'portfolio'::text
     CROSS JOIN dummy_list dl
UNION
 SELECT DISTINCT b.book_name AS subject_name,
    l.name,
    lg.name AS "group",
    lt.name AS type,
        CASE
            WHEN l.time_period IS NULL THEN dl.year
            ELSE l.time_period::integer::double precision
        END AS time_period,
        CASE
            WHEN l.area IS NULL THEN dl.area::character varying
            ELSE l.area
        END AS area,
    l.unit,
    l.limit_value,
    l.subject_id,
    l.product_type_id
   FROM trading_dw."limit" l
     JOIN trading_dw.limit_group lg ON lg.id = l.limit_group_id
     JOIN trading_dw.limit_type lt ON lt.id = l.limit_type_id
     JOIN trading_dw.book b ON l.subject_id = b.id AND lg.name::text = 'tradebook'::text
     CROSS JOIN dummy_list dl
UNION
 SELECT DISTINCT p.short_name AS subject_name,
    l.name,
    lg.name AS "group",
    lt.name AS type,
        CASE
            WHEN l.time_period IS NULL THEN dl.year
            ELSE l.time_period::integer::double precision
        END AS time_period,
        CASE
            WHEN l.area IS NULL THEN dl.area::character varying
            ELSE l.area
        END AS area,
    l.unit,
    l.limit_value,
    l.subject_id,
    l.product_type_id
   FROM trading_dw."limit" l
     JOIN trading_dw.limit_group lg ON lg.id = l.limit_group_id
     JOIN trading_dw.limit_type lt ON lt.id = l.limit_type_id
     JOIN trading_dw.partner p ON l.subject_id = p.id AND lg.name::text = 'counterparty'::text
     CROSS JOIN dummy_list dl
UNION
 SELECT DISTINCT p.short_name AS subject_name,
    l.name,
    lg.name AS "group",
    lt.name AS type,
        CASE
            WHEN l.time_period IS NULL THEN dl.year
            ELSE l.time_period::integer::double precision
        END AS time_period,
        CASE
            WHEN l.area IS NULL THEN dl.area::character varying
            ELSE l.area
        END AS area,
    l.unit,
    l.limit_value,
    l.subject_id,
    l.product_type_id
   FROM trading_dw."limit" l
     JOIN trading_dw.limit_group lg ON lg.id = l.limit_group_id
     JOIN trading_dw.limit_type lt ON lt.id = l.limit_type_id
     JOIN trading_dw.partner p ON l.subject_id = p.id AND lg.name::text = 'company'::text
     CROSS JOIN dummy_list dl;


-- trading_dw.v_mtm_book source

CREATE OR REPLACE VIEW trading_dw.v_mtm_book
AS SELECT dd.date AS calculation_date,
    bk.book_name,
    mc.identification_description AS trade,
    p.name AS product,
        CASE
            WHEN mc.position_type::text = 'Buy'::text THEN company.short_name
            ELSE counterparty.short_name
        END AS company_short_name,
        CASE
            WHEN mc.position_type::text = 'Buy'::text THEN counterparty.short_name
            ELSE company.short_name
        END AS counterparty_short_name,
        CASE
            WHEN trade.position_type::text = 'Sell'::text THEN cin.zone_name
            ELSE cout.zone_name
        END AS market_area,
    dd2.date AS trade_date,
    dd3.date AS delivery_day,
    t.date_hour_minute_start AS trading_interval_start,
    t1.date_hour_minute_start AS trading_interval_end,
    mc.position_type,
    mc.price,
        CASE
            WHEN mc.position_type::text = 'Sell'::text THEN mc.quantity * '-1'::integer::numeric
            ELSE mc.quantity
        END AS quantity,
        CASE
            WHEN mc.position_type::text = 'Sell'::text THEN mc.reminder_of_quantity * '-1'::integer::numeric
            ELSE mc.reminder_of_quantity
        END AS reminder_of_quantity,
    mc.price *
        CASE
            WHEN mc.position_type::text = 'Sell'::text THEN mc.quantity * '-1'::integer::numeric
            ELSE mc.quantity
        END AS value,
    pr.price *
        CASE
            WHEN mc.position_type::text = 'Sell'::text THEN mc.reminder_of_quantity * '-1'::integer::numeric
            ELSE mc.reminder_of_quantity
        END AS market_value,
    pr.price AS market_price,
    mc.mtm,
    mc.mtm_previous_year,
    mc.mtm_previous_day,
    mc.trade_closed,
    date_part('year'::text, t.date_hour_minute_start) AS year,
    to_char(t.date_hour_minute_start::timestamp with time zone, 'YYYY-MM'::text) AS month,
    mc.trade_date_id,
    mc.product_id,
    mc.market_price_id,
    mc.calculation_date_id,
    mc.book_id,
    mc.delivery_day_id,
    mc.trading_interval_start_id AS interval_start_time_id,
    mc.trading_interval_end_id AS interval_end_time_id
   FROM trading_dw.mtm_book mc
     LEFT JOIN trading_dw.product p ON mc.product_id = p.id
     LEFT JOIN trading_dw.book bk ON mc.book_id = bk.id
     LEFT JOIN trading_dw.v_dim_date_met dd ON mc.calculation_date_id = dd.id
     LEFT JOIN trading_dw.v_dim_date_met dd2 ON mc.trade_date_id = dd2.id
     LEFT JOIN trading_dw.v_dim_time_met t ON mc.trading_interval_start_id = t.id
     LEFT JOIN trading_dw.v_dim_time_met t1 ON mc.trading_interval_end_id = t1.id
     LEFT JOIN trading_dw.prices pr ON mc.market_price_id = pr.id
     LEFT JOIN trading_dw.v_dim_date_met dd3 ON mc.delivery_day_id = dd3.id
     JOIN trading_dw.trade trade ON mc.identification_description::text = trade.identification_description::text
     JOIN trading_dw.partner company ON trade.company_id = company.id
     JOIN trading_dw.partner counterparty ON trade.counterparty_id = counterparty.id
     JOIN trading_dw.control_area cin ON cin.id = trade.in_control_area_id
     JOIN trading_dw.control_area cout ON cout.id = trade.out_control_area_id;


-- trading_dw.v_mtm_book_sum source

CREATE OR REPLACE VIEW trading_dw.v_mtm_book_sum
AS SELECT dd.date AS calculation_date,
    mc.identification_description AS trade,
    trader.trader_name,
    book.book_name,
    cm.name AS commodity,
    p.name AS product,
        CASE
            WHEN mc.position_type::text = 'Buy'::text THEN company.short_name
            ELSE counterparty.short_name
        END AS company_short_name,
        CASE
            WHEN mc.position_type::text = 'Buy'::text THEN counterparty.short_name
            ELSE company.short_name
        END AS counterparty_short_name,
        CASE
            WHEN trade.position_type::text = 'Sell'::text THEN cin.zone_name
            ELSE cout.zone_name
        END AS market_area,
    dd2.date AS trade_date,
    min(dd3.date) AS delivery_day,
    min(t.date_hour_minute_start) AS trading_interval_start,
    max(t1.date_hour_minute_start) AS trading_interval_end,
    mc.position_type,
    mc.price,
    sum(
        CASE
            WHEN mc.position_type::text = 'Sell'::text THEN mc.quantity * '-1'::integer::numeric
            ELSE mc.quantity
        END) AS quantity,
    sum(
        CASE
            WHEN mc.position_type::text = 'Sell'::text THEN mc.reminder_of_quantity * '-1'::integer::numeric
            ELSE mc.reminder_of_quantity
        END) AS reminder_of_quantity,
    mc.price * sum(
        CASE
            WHEN mc.position_type::text = 'Sell'::text THEN mc.quantity * '-1'::integer::numeric
            ELSE mc.quantity
        END) AS value,
    round(avg(pr.price), 2) AS market_price,
    avg(pr.price) * sum(
        CASE
            WHEN mc.position_type::text = 'Sell'::text THEN mc.reminder_of_quantity * '-1'::integer::numeric
            ELSE mc.reminder_of_quantity
        END) AS market_value,
    sum(mc.mtm) AS mtm,
    sum(mc.mtm_previous_year) AS mtm_previous_year,
    sum(mc.mtm_previous_day) AS mtm_previous_day,
    mc.trade_closed,
    date_part('year'::text, min(t.date_hour_minute_start)) AS year,
    to_char(min(t.date_hour_minute_start)::timestamp with time zone, 'YYYY-MM'::text) AS month,
    min(mc.trade_date_id) AS trade_date_id,
    mc.product_id,
    mc.calculation_date_id,
    min(mc.delivery_day_id) AS delivery_day_id,
    min(mc.trading_interval_start_id) AS interval_start_time_id,
    max(mc.trading_interval_end_id) AS interval_end_time_id
   FROM trading_dw.mtm_book mc
     LEFT JOIN trading_dw.product p ON mc.product_id = p.id
     LEFT JOIN trading_dw.v_dim_date_met dd ON mc.calculation_date_id = dd.id
     LEFT JOIN trading_dw.v_dim_date_met dd2 ON mc.trade_date_id = dd2.id
     LEFT JOIN trading_dw.v_dim_time_met t ON mc.trading_interval_start_id = t.id
     LEFT JOIN trading_dw.v_dim_time_met t1 ON mc.trading_interval_end_id = t1.id
     LEFT JOIN trading_dw.prices pr ON mc.market_price_id = pr.id
     LEFT JOIN trading_dw.v_dim_date_met dd3 ON mc.delivery_day_id = dd3.id
     JOIN trading_dw.trade trade ON mc.identification_description::text = trade.identification_description::text
     JOIN trading_dw.trader trader ON trade.trader_id = trader.id
     JOIN trading_dw.book book ON mc.book_id = book.id
     JOIN trading_dw.partner company ON trade.company_id = company.id
     JOIN trading_dw.partner counterparty ON trade.counterparty_id = counterparty.id
     JOIN trading_dw.control_area cin ON cin.id = trade.in_control_area_id
     JOIN trading_dw.control_area cout ON cout.id = trade.out_control_area_id
     JOIN trading_dw.commodity cm ON p.commodity_id = cm.id
  GROUP BY dd.date, mc.identification_description, trader.trader_name, book.book_name, cm.name, p.name, mc.position_type, mc.price, company.short_name, counterparty.short_name, trade.position_type, cin.zone_name, cout.zone_name, dd2.date, mc.trade_closed, mc.product_id, mc.calculation_date_id
  ORDER BY mc.identification_description, (min(t.date_hour_minute_start));


-- trading_dw.v_mtm_company source

CREATE OR REPLACE VIEW trading_dw.v_mtm_company
AS SELECT dd.date AS calculation_date,
    mc.identification_description AS trade,
    p.name AS product,
        CASE
            WHEN mc.position_type::text = 'Buy'::text THEN company.short_name
            ELSE counterparty.short_name
        END AS company_short_name,
        CASE
            WHEN mc.position_type::text = 'Buy'::text THEN counterparty.short_name
            ELSE company.short_name
        END AS counterparty_short_name,
        CASE
            WHEN trade.position_type::text = 'Sell'::text THEN cin.zone_name
            ELSE cout.zone_name
        END AS market_area,
    dd2.date AS trade_date,
    dd3.date AS delivery_day,
    t.date_hour_minute_start AS trading_interval_start,
    t1.date_hour_minute_start AS trading_interval_end,
    mc.position_type,
    mc.price,
        CASE
            WHEN mc.position_type::text = 'Sell'::text THEN mc.quantity * '-1'::integer::numeric
            ELSE mc.quantity
        END AS quantity,
        CASE
            WHEN mc.position_type::text = 'Sell'::text THEN mc.reminder_of_quantity * '-1'::integer::numeric
            ELSE mc.reminder_of_quantity
        END AS reminder_of_quantity,
    mc.price *
        CASE
            WHEN mc.position_type::text = 'Sell'::text THEN mc.quantity * '-1'::integer::numeric
            ELSE mc.quantity
        END AS value,
    pr.price AS market_price,
    pr.price *
        CASE
            WHEN mc.position_type::text = 'Sell'::text THEN mc.reminder_of_quantity * '-1'::integer::numeric
            ELSE mc.reminder_of_quantity
        END AS market_value,
    mc.mtm,
    mc.mtm_previous_year,
    mc.mtm_previous_day,
    mc.trade_closed,
    date_part('year'::text, t.date_hour_minute_start) AS year,
    to_char(t.date_hour_minute_start::timestamp with time zone, 'YYYY-MM'::text) AS month,
    mc.trade_date_id,
    mc.product_id,
    mc.market_price_id,
    mc.calculation_date_id,
    mc.delivery_day_id,
    mc.trading_interval_start_id AS interval_start_time_id,
    mc.trading_interval_end_id AS interval_end_time_id
   FROM trading_dw.mtm_company mc
     LEFT JOIN trading_dw.product p ON mc.product_id = p.id
     LEFT JOIN trading_dw.v_dim_date_met dd ON mc.calculation_date_id = dd.id
     LEFT JOIN trading_dw.v_dim_date_met dd2 ON mc.trade_date_id = dd2.id
     LEFT JOIN trading_dw.v_dim_time_met t ON mc.trading_interval_start_id = t.id
     LEFT JOIN trading_dw.v_dim_time_met t1 ON mc.trading_interval_end_id = t1.id
     LEFT JOIN trading_dw.prices pr ON mc.market_price_id = pr.id
     LEFT JOIN trading_dw.v_dim_date_met dd3 ON mc.delivery_day_id = dd3.id
     JOIN trading_dw.trade trade ON mc.identification_description::text = trade.identification_description::text
     JOIN trading_dw.partner company ON trade.company_id = company.id
     JOIN trading_dw.partner counterparty ON trade.counterparty_id = counterparty.id
     JOIN trading_dw.control_area cin ON cin.id = trade.in_control_area_id
     JOIN trading_dw.control_area cout ON cout.id = trade.out_control_area_id;


-- trading_dw.v_mtm_company_sum source

CREATE OR REPLACE VIEW trading_dw.v_mtm_company_sum
AS SELECT dd.date AS calculation_date,
    mc.identification_description AS trade,
    trader.trader_name,
    book.book_name,
    cm.name AS commodity,
    p.name AS product,
        CASE
            WHEN mc.position_type::text = 'Buy'::text THEN company.short_name
            ELSE counterparty.short_name
        END AS company_short_name,
        CASE
            WHEN mc.position_type::text = 'Buy'::text THEN counterparty.short_name
            ELSE company.short_name
        END AS counterparty_short_name,
        CASE
            WHEN trade.position_type::text = 'Sell'::text THEN cin.zone_name
            ELSE cout.zone_name
        END AS market_area,
    dd2.date AS trade_date,
    min(dd3.date) AS delivery_day,
    min(t.date_hour_minute_start) AS trading_interval_start,
    max(t1.date_hour_minute_start) AS trading_interval_end,
    mc.position_type,
    mc.price,
    sum(
        CASE
            WHEN mc.position_type::text = 'Sell'::text THEN mc.quantity * '-1'::integer::numeric
            ELSE mc.quantity
        END) AS quantity,
    sum(
        CASE
            WHEN mc.position_type::text = 'Sell'::text THEN mc.reminder_of_quantity * '-1'::integer::numeric
            ELSE mc.reminder_of_quantity
        END) AS reminder_of_quantity,
    mc.price * sum(
        CASE
            WHEN mc.position_type::text = 'Sell'::text THEN mc.quantity * '-1'::integer::numeric
            ELSE mc.quantity
        END) AS value,
    round(avg(pr.price), 2) AS market_price,
    avg(pr.price) * sum(
        CASE
            WHEN mc.position_type::text = 'Sell'::text THEN mc.reminder_of_quantity * '-1'::integer::numeric
            ELSE mc.reminder_of_quantity
        END) AS market_value,
    sum(mc.mtm) AS mtm,
    sum(mc.mtm_previous_year) AS mtm_previous_year,
    sum(mc.mtm_previous_day) AS mtm_previous_day,
    mc.trade_closed,
    date_part('year'::text, min(t.date_hour_minute_start)) AS year,
    to_char(min(t.date_hour_minute_start)::timestamp with time zone, 'YYYY-MM'::text) AS month,
    min(mc.trade_date_id) AS trade_date_id,
    mc.product_id,
    mc.calculation_date_id,
    min(mc.delivery_day_id) AS delivery_day_id,
    min(mc.trading_interval_start_id) AS interval_start_time_id,
    max(mc.trading_interval_end_id) AS interval_end_time_id
   FROM trading_dw.mtm_company mc
     LEFT JOIN trading_dw.product p ON mc.product_id = p.id
     LEFT JOIN trading_dw.v_dim_date_met dd ON mc.calculation_date_id = dd.id
     LEFT JOIN trading_dw.v_dim_date_met dd2 ON mc.trade_date_id = dd2.id
     LEFT JOIN trading_dw.v_dim_time_met t ON mc.trading_interval_start_id = t.id
     LEFT JOIN trading_dw.v_dim_time_met t1 ON mc.trading_interval_end_id = t1.id
     LEFT JOIN trading_dw.prices pr ON mc.market_price_id = pr.id
     LEFT JOIN trading_dw.v_dim_date_met dd3 ON mc.delivery_day_id = dd3.id
     JOIN trading_dw.trade trade ON mc.identification_description::text = trade.identification_description::text
     JOIN trading_dw.trader trader ON trade.trader_id = trader.id
     JOIN trading_dw.book book ON trade.trade_book_id = book.id
     JOIN trading_dw.partner company ON trade.company_id = company.id
     JOIN trading_dw.partner counterparty ON trade.counterparty_id = counterparty.id
     JOIN trading_dw.control_area cin ON cin.id = trade.in_control_area_id
     JOIN trading_dw.control_area cout ON cout.id = trade.out_control_area_id
     JOIN trading_dw.commodity cm ON p.commodity_id = cm.id
  GROUP BY dd.date, mc.identification_description, trader.trader_name, book.book_name, cm.name, p.name, mc.position_type, mc.price, company.short_name, counterparty.short_name, trade.position_type, cin.zone_name, cout.zone_name, dd2.date, mc.trade_closed, mc.product_id, mc.calculation_date_id
  ORDER BY mc.identification_description, (min(t.date_hour_minute_start));


-- trading_dw.v_mtm_trader source

CREATE OR REPLACE VIEW trading_dw.v_mtm_trader
AS SELECT dd.date AS calculation_date,
    tr.trader_name,
    mc.identification_description AS trade,
    p.name AS product,
        CASE
            WHEN mc.position_type::text = 'Buy'::text THEN company.short_name
            ELSE counterparty.short_name
        END AS company_short_name,
        CASE
            WHEN mc.position_type::text = 'Buy'::text THEN counterparty.short_name
            ELSE company.short_name
        END AS counterparty_short_name,
        CASE
            WHEN trade.position_type::text = 'Sell'::text THEN cin.zone_name
            ELSE cout.zone_name
        END AS market_area,
    dd2.date AS trade_date,
    dd3.date AS delivery_day,
    t.date_hour_minute_start AS trading_interval_start,
    t1.date_hour_minute_start AS trading_interval_end,
    mc.position_type,
    mc.price,
        CASE
            WHEN mc.position_type::text = 'Sell'::text THEN mc.quantity * '-1'::integer::numeric
            ELSE mc.quantity
        END AS quantity,
        CASE
            WHEN mc.position_type::text = 'Sell'::text THEN mc.reminder_of_quantity * '-1'::integer::numeric
            ELSE mc.reminder_of_quantity
        END AS reminder_of_quantity,
    mc.price *
        CASE
            WHEN mc.position_type::text = 'Sell'::text THEN mc.quantity * '-1'::integer::numeric
            ELSE mc.quantity
        END AS value,
    pr.price AS market_price,
    pr.price *
        CASE
            WHEN mc.position_type::text = 'Sell'::text THEN mc.reminder_of_quantity * '-1'::integer::numeric
            ELSE mc.reminder_of_quantity
        END AS market_value,
    mc.mtm,
    mc.mtm_previous_year,
    mc.mtm_previous_day,
    mc.trade_closed,
    date_part('year'::text, t.date_hour_minute_start) AS year,
    to_char(t.date_hour_minute_start::timestamp with time zone, 'YYYY-MM'::text) AS month,
    mc.trade_date_id,
    mc.product_id,
    mc.market_price_id,
    mc.calculation_date_id,
    mc.trader_id,
    mc.delivery_day_id,
    mc.trading_interval_start_id AS interval_start_time_id,
    mc.trading_interval_end_id AS interval_end_time_id
   FROM trading_dw.mtm_trader mc
     LEFT JOIN trading_dw.product p ON mc.product_id = p.id
     LEFT JOIN trading_dw.trader tr ON mc.trader_id = tr.id
     LEFT JOIN trading_dw.v_dim_date_met dd ON mc.calculation_date_id = dd.id
     LEFT JOIN trading_dw.v_dim_date_met dd2 ON mc.trade_date_id = dd2.id
     LEFT JOIN trading_dw.v_dim_time_met t ON mc.trading_interval_start_id = t.id
     LEFT JOIN trading_dw.v_dim_time_met t1 ON mc.trading_interval_end_id = t1.id
     LEFT JOIN trading_dw.prices pr ON mc.market_price_id = pr.id
     LEFT JOIN trading_dw.v_dim_date_met dd3 ON mc.delivery_day_id = dd3.id
     JOIN trading_dw.trade trade ON mc.identification_description::text = trade.identification_description::text
     JOIN trading_dw.partner company ON trade.company_id = company.id
     JOIN trading_dw.partner counterparty ON trade.counterparty_id = counterparty.id
     JOIN trading_dw.control_area cin ON cin.id = trade.in_control_area_id
     JOIN trading_dw.control_area cout ON cout.id = trade.out_control_area_id;


-- trading_dw.v_mtm_trader_sum source

CREATE OR REPLACE VIEW trading_dw.v_mtm_trader_sum
AS SELECT dd.date AS calculation_date,
    mc.identification_description AS trade,
    trader.trader_name,
    book.book_name,
    cm.name AS commodity,
    p.name AS product,
        CASE
            WHEN mc.position_type::text = 'Buy'::text THEN company.short_name
            ELSE counterparty.short_name
        END AS company_short_name,
        CASE
            WHEN mc.position_type::text = 'Buy'::text THEN counterparty.short_name
            ELSE company.short_name
        END AS counterparty_short_name,
        CASE
            WHEN trade.position_type::text = 'Sell'::text THEN cin.zone_name
            ELSE cout.zone_name
        END AS market_area,
    dd2.date AS trade_date,
    min(dd3.date) AS delivery_day,
    min(t.date_hour_minute_start) AS trading_interval_start,
    max(t1.date_hour_minute_start) AS trading_interval_end,
    mc.position_type,
    mc.price,
    sum(
        CASE
            WHEN mc.position_type::text = 'Sell'::text THEN mc.quantity * '-1'::integer::numeric
            ELSE mc.quantity
        END) AS quantity,
    sum(
        CASE
            WHEN mc.position_type::text = 'Sell'::text THEN mc.reminder_of_quantity * '-1'::integer::numeric
            ELSE mc.reminder_of_quantity
        END) AS reminder_of_quantity,
    mc.price * sum(
        CASE
            WHEN mc.position_type::text = 'Sell'::text THEN mc.quantity * '-1'::integer::numeric
            ELSE mc.quantity
        END) AS value,
    round(avg(pr.price), 2) AS market_price,
    avg(pr.price) * sum(
        CASE
            WHEN mc.position_type::text = 'Sell'::text THEN mc.reminder_of_quantity * '-1'::integer::numeric
            ELSE mc.reminder_of_quantity
        END) AS market_value,
    sum(mc.mtm) AS mtm,
    sum(mc.mtm_previous_year) AS mtm_previous_year,
    sum(mc.mtm_previous_day) AS mtm_previous_day,
    mc.trade_closed,
    date_part('year'::text, min(t.date_hour_minute_start)) AS year,
    to_char(min(t.date_hour_minute_start)::timestamp with time zone, 'YYYY-MM'::text) AS month,
    min(mc.trade_date_id) AS trade_date_id,
    mc.product_id,
    mc.calculation_date_id,
    min(mc.delivery_day_id) AS delivery_day_id,
    min(mc.trading_interval_start_id) AS interval_start_time_id,
    max(mc.trading_interval_end_id) AS interval_end_time_id
   FROM trading_dw.mtm_trader mc
     LEFT JOIN trading_dw.product p ON mc.product_id = p.id
     LEFT JOIN trading_dw.v_dim_date_met dd ON mc.calculation_date_id = dd.id
     LEFT JOIN trading_dw.v_dim_date_met dd2 ON mc.trade_date_id = dd2.id
     LEFT JOIN trading_dw.v_dim_time_met t ON mc.trading_interval_start_id = t.id
     LEFT JOIN trading_dw.v_dim_time_met t1 ON mc.trading_interval_end_id = t1.id
     LEFT JOIN trading_dw.prices pr ON mc.market_price_id = pr.id
     LEFT JOIN trading_dw.v_dim_date_met dd3 ON mc.delivery_day_id = dd3.id
     JOIN trading_dw.trade trade ON mc.identification_description::text = trade.identification_description::text
     JOIN trading_dw.trader trader ON mc.trader_id = trader.id
     JOIN trading_dw.book book ON trade.trade_book_id = book.id
     JOIN trading_dw.partner company ON trade.company_id = company.id
     JOIN trading_dw.partner counterparty ON trade.counterparty_id = counterparty.id
     JOIN trading_dw.control_area cin ON cin.id = trade.in_control_area_id
     JOIN trading_dw.control_area cout ON cout.id = trade.out_control_area_id
     JOIN trading_dw.commodity cm ON p.commodity_id = cm.id
  GROUP BY dd.date, mc.identification_description, trader.trader_name, book.book_name, cm.name, p.name, mc.position_type, mc.price, company.short_name, counterparty.short_name, trade.position_type, cin.zone_name, cout.zone_name, dd2.date, mc.trade_closed, mc.product_id, mc.calculation_date_id
  ORDER BY mc.identification_description, (min(t.date_hour_minute_start));


-- trading_dw.v_pfc source

CREATE OR REPLACE VIEW trading_dw.v_pfc
AS SELECT b.date,
    c.date_hour_minute_start AS "time",
    d.price_curve_name AS price_curve,
    a.price
   FROM trading_dw.prices a
     JOIN trading_dw.v_dim_time_met c ON c.id = a.time_id
     JOIN trading_dw.v_dim_date_met b ON b.date = c.date
     JOIN trading_dw.price_curve d ON d.id = a.price_curve_id;


-- trading_dw.v_pfc_de source

CREATE OR REPLACE VIEW trading_dw.v_pfc_de
AS WITH max_tick AS (
         SELECT max(tsv.date_time_tick) AS date_time_tick
           FROM timeseries.time_series_value tsv
          WHERE tsv.time_series_id = (( SELECT b_1.id
                   FROM timeseries.level_relation_view b_1
                  WHERE b_1.category = 'Power|Price|Synthetic|Day Ahead'::text AND b_1.geography_from = 'DE|Country'::text))
        ), data_pfc AS (
         SELECT a_1.date_time_tick,
            a_1.date_time_value,
            a_1.numeric_value
           FROM timeseries.time_series_value a_1
          WHERE a_1.time_series_id = (( SELECT b_1.id
                   FROM timeseries.level_relation_view b_1
                  WHERE b_1.category = 'Power|Price|Synthetic|Day Ahead'::text AND b_1.geography_from = 'DE|Country'::text))
        )
 SELECT (a.date_time_tick AT TIME ZONE 'Europe/Ljubljana'::text) AS date_time_tick,
    (a.date_time_value AT TIME ZONE 'Europe/Ljubljana'::text) AS date_time_value,
    a.numeric_value
   FROM data_pfc a,
    max_tick b
  WHERE a.date_time_tick = b.date_time_tick;


-- trading_dw.v_pfc_fr source

CREATE OR REPLACE VIEW trading_dw.v_pfc_fr
AS WITH max_tick AS (
         SELECT max(tsv.date_time_tick) AS date_time_tick
           FROM timeseries.time_series_value tsv
          WHERE tsv.time_series_id = (( SELECT b_1.id
                   FROM timeseries.level_relation_view b_1
                  WHERE b_1.category = 'Power|Price|Synthetic|Day Ahead'::text AND b_1.geography_from = 'FR|Country'::text))
        ), data_pfc AS (
         SELECT a_1.date_time_tick,
            a_1.date_time_value,
            a_1.numeric_value
           FROM timeseries.time_series_value a_1
          WHERE a_1.time_series_id = (( SELECT b_1.id
                   FROM timeseries.level_relation_view b_1
                  WHERE b_1.category = 'Power|Price|Synthetic|Day Ahead'::text AND b_1.geography_from = 'FR|Country'::text))
        )
 SELECT (a.date_time_tick AT TIME ZONE 'Europe/Ljubljana'::text) AS date_time_tick,
    (a.date_time_value AT TIME ZONE 'Europe/Ljubljana'::text) AS date_time_value,
    a.numeric_value
   FROM data_pfc a,
    max_tick b
  WHERE a.date_time_tick = b.date_time_tick;


-- trading_dw.v_pfc_hu source

CREATE OR REPLACE VIEW trading_dw.v_pfc_hu
AS WITH max_tick AS (
         SELECT max(tsv.date_time_tick) AS date_time_tick
           FROM timeseries.time_series_value tsv
          WHERE tsv.time_series_id = (( SELECT b_1.id
                   FROM timeseries.level_relation_view b_1
                  WHERE b_1.category = 'Power|Price|Synthetic|Day Ahead'::text AND b_1.geography_from = 'HU|Country'::text))
        ), data_pfc AS (
         SELECT a_1.date_time_tick,
            a_1.date_time_value,
            a_1.numeric_value
           FROM timeseries.time_series_value a_1
          WHERE a_1.time_series_id = (( SELECT b_1.id
                   FROM timeseries.level_relation_view b_1
                  WHERE b_1.category = 'Power|Price|Synthetic|Day Ahead'::text AND b_1.geography_from = 'HU|Country'::text))
        )
 SELECT (a.date_time_tick AT TIME ZONE 'Europe/Ljubljana'::text) AS date_time_tick,
    (a.date_time_value AT TIME ZONE 'Europe/Ljubljana'::text) AS date_time_value,
    a.numeric_value
   FROM data_pfc a,
    max_tick b
  WHERE a.date_time_tick = b.date_time_tick;


-- trading_dw.v_pfc_it source

CREATE OR REPLACE VIEW trading_dw.v_pfc_it
AS WITH max_tick AS (
         SELECT max(tsv.date_time_tick) AS date_time_tick
           FROM timeseries.time_series_value tsv
          WHERE tsv.time_series_id = (( SELECT b_1.id
                   FROM timeseries.level_relation_view b_1
                  WHERE b_1.category = 'Power|Price|Synthetic|Day Ahead'::text AND b_1.geography_from = 'IT|Country'::text))
        ), data_pfc AS (
         SELECT a_1.date_time_tick,
            a_1.date_time_value,
            a_1.numeric_value
           FROM timeseries.time_series_value a_1
          WHERE a_1.time_series_id = (( SELECT b_1.id
                   FROM timeseries.level_relation_view b_1
                  WHERE b_1.category = 'Power|Price|Synthetic|Day Ahead'::text AND b_1.geography_from = 'IT|Country'::text))
        )
 SELECT (a.date_time_tick AT TIME ZONE 'Europe/Ljubljana'::text) AS date_time_tick,
    (a.date_time_value AT TIME ZONE 'Europe/Ljubljana'::text) AS date_time_value,
    a.numeric_value
   FROM data_pfc a,
    max_tick b
  WHERE a.date_time_tick = b.date_time_tick;


-- trading_dw.v_pfc_prices_daily_comparison source

CREATE OR REPLACE VIEW trading_dw.v_pfc_prices_daily_comparison
AS SELECT current_day.current_day_price,
    previous_day.previous_day_price,
    100::numeric - previous_day.previous_day_price * 100::numeric / current_day.current_day_price AS price_percentage_difference,
    current_day.price_curve_name,
    current_day.price_curve_id,
    current_day.date_hour_minute_start,
    current_day.hour_minute_start
   FROM ( SELECT DISTINCT pc.price_curve_name,
            p.price AS previous_day_price,
            p.price_curve_id,
            dt.date_hour_minute_start,
            dt.hour_minute_start
           FROM trading_dw.prices p
             JOIN trading_dw.price_curve pc ON pc.id = p.price_curve_id
             JOIN trading_dw.dim_time dt ON p.time_id = dt.id
          WHERE dt.date_hour_minute_start >= (date_trunc('day'::text, CURRENT_TIMESTAMP) - '1 day'::interval day) AND dt.date_hour_minute_start < date_trunc('day'::text, CURRENT_TIMESTAMP)
          ORDER BY dt.date_hour_minute_start) previous_day
     JOIN ( SELECT DISTINCT pc.price_curve_name,
            p.price AS current_day_price,
            p.price_curve_id,
            dt.date_hour_minute_start,
            dt.hour_minute_start
           FROM trading_dw.prices p
             JOIN trading_dw.price_curve pc ON pc.id = p.price_curve_id
             JOIN trading_dw.dim_time dt ON p.time_id = dt.id
          WHERE dt.date_hour_minute_start >= date_trunc('day'::text, CURRENT_TIMESTAMP) AND dt.date_hour_minute_start <= date_trunc('hour'::text, CURRENT_TIMESTAMP)
          ORDER BY dt.date_hour_minute_start) current_day ON previous_day.price_curve_id = current_day.price_curve_id AND previous_day.hour_minute_start = current_day.hour_minute_start;


-- trading_dw.v_pnl_book_sum source

CREATE OR REPLACE VIEW trading_dw.v_pnl_book_sum
AS WITH mtm AS (
         SELECT a_1.trade,
            a_1.commodity,
            a_1.trade_type,
            a_1.trade_date,
            a_1.trade_book,
            a_1.trader_name,
            a_1.company_short_name,
            a_1.counterparty_short_name,
            a_1.market_area,
            a_1.position_type,
            a_1.delivery_day,
            a_1.delivery_start,
            a_1.price,
            a_1.quantity,
            COALESCE(mtm.reminder_of_quantity, 0::numeric) AS reminder_of_quantity,
            a_1.value,
            COALESCE(mtm.market_price, 0::numeric) AS market_price,
            COALESCE(mtm.market_price * mtm.reminder_of_quantity, 0::numeric) AS market_value,
            COALESCE(mtm.mtm, 0::numeric) AS mtm,
            ''::text AS netting,
            COALESCE(mtm.mtm_previous_day, 0::numeric) AS mtm_eopd,
            COALESCE(mtm.mtm_previous_year, 0::numeric) AS mtm_eopy,
            date_part('year'::text, a_1.trade_date) AS year,
            to_char(a_1.trade_date::timestamp with time zone, 'YYYY-MM'::text) AS month
           FROM trading_dw.v_trade_for_pnl a_1
             LEFT JOIN trading_dw.v_mtm_book_sum mtm ON mtm.trade::text = a_1.trade::text AND mtm.calculation_date =
                CASE
                    WHEN EXTRACT(dow FROM (now() AT TIME ZONE 'UTC'::text)) = 1::numeric THEN ( SELECT x.date
                       FROM trading_dw.dim_date x
                      WHERE x.date = (date_trunc('day'::text, now() - '3 days'::interval) AT TIME ZONE 'UTC'::text))
                    ELSE ( SELECT x.date
                       FROM trading_dw.dim_date x
                      WHERE x.date = (date_trunc('day'::text, now() - '1 day'::interval) AT TIME ZONE 'UTC'::text))
                END
        ), realization AS (
         SELECT a_1.identification_description_sell,
            a_1.trade_date_sell,
            a_1.trading_interval_start,
            a_1.trading_interval_end_sell,
            a_1.quantity_reminder_sell,
            a_1.trade_date_id_sell,
            a_1.product_name_sell,
            a_1.realization,
            a_1.trading_interval_start_id,
            a_1.trading_interval_end_id_sell,
            a_1.product_id_sell
           FROM trading_dw.v_book_realization_sum a_1
        )
 SELECT a.trade,
    a.commodity,
    a.trade_type,
    a.trade_book,
    a.trader_name,
    a.company_short_name,
    a.counterparty_short_name,
    a.market_area,
    a.trade_date,
    a.position_type,
    a.delivery_day,
    a.delivery_start,
    a.price::numeric AS price,
    a.quantity,
    COALESCE(a.reminder_of_quantity, COALESCE(b.quantity_reminder_sell, a.quantity)) AS reminder_of_quantity,
    a.value,
    a.market_price,
    a.market_value,
    a.mtm,
    COALESCE(b.realization, 0::numeric) AS realization,
    a.mtm + COALESCE(b.realization, 0::numeric) AS pnl,
    a.netting,
    a.mtm_eopd,
    a.mtm_eopy,
    a.year,
    a.month
   FROM mtm a
     LEFT JOIN realization b ON b.identification_description_sell::text = a.trade::text AND b.trading_interval_start = a.delivery_start
  ORDER BY a.trade, a.delivery_start;


-- trading_dw.v_pnl_company_sum source

CREATE OR REPLACE VIEW trading_dw.v_pnl_company_sum
AS WITH mtm AS (
         SELECT a_1.trade,
            a_1.commodity,
            a_1.trade_type,
            a_1.trade_date,
            a_1.trade_book,
            a_1.trader_name,
            a_1.company_short_name,
            a_1.counterparty_short_name,
            a_1.market_area,
            a_1.position_type,
            a_1.delivery_day,
            a_1.delivery_start,
            a_1.price,
            a_1.quantity,
            COALESCE(mtm.reminder_of_quantity, 0::numeric) AS reminder_of_quantity,
            a_1.value,
            COALESCE(mtm.market_price, 0::numeric) AS market_price,
            COALESCE(mtm.market_price * mtm.reminder_of_quantity, 0::numeric) AS market_value,
            COALESCE(mtm.mtm, 0::numeric) AS mtm,
            ''::text AS netting,
            COALESCE(mtm.mtm_previous_day, 0::numeric) AS mtm_eopd,
            COALESCE(mtm.mtm_previous_year, 0::numeric) AS mtm_eopy,
            date_part('year'::text, a_1.trade_date) AS year,
            to_char(a_1.trade_date::timestamp with time zone, 'YYYY-MM'::text) AS month
           FROM trading_dw.v_trade_for_pnl a_1
             LEFT JOIN trading_dw.v_mtm_company_sum mtm ON mtm.trade::text = a_1.trade::text AND mtm.calculation_date =
                CASE
                    WHEN EXTRACT(dow FROM (now() AT TIME ZONE 'UTC'::text)) = 1::numeric THEN ( SELECT x.date
                       FROM trading_dw.dim_date x
                      WHERE x.date = (date_trunc('day'::text, now() - '3 days'::interval) AT TIME ZONE 'UTC'::text))
                    ELSE ( SELECT x.date
                       FROM trading_dw.dim_date x
                      WHERE x.date = (date_trunc('day'::text, now() - '1 day'::interval) AT TIME ZONE 'UTC'::text))
                END
        ), realization AS (
         SELECT a_1.identification_description_sell,
            a_1.trade_date_sell,
            a_1.trading_interval_start,
            a_1.trading_interval_end_sell,
            a_1.quantity_reminder_sell,
            a_1.trade_date_id_sell,
            a_1.product_name_sell,
            a_1.realization,
            a_1.trading_interval_start_id,
            a_1.trading_interval_end_id_sell,
            a_1.product_id_sell
           FROM trading_dw.v_company_realization_sum a_1
        )
 SELECT a.trade,
    a.commodity,
    a.trade_type,
    a.trade_book,
    a.trader_name,
    a.company_short_name,
    a.counterparty_short_name,
    a.market_area,
    a.trade_date,
    a.position_type,
    a.delivery_day,
    a.delivery_start,
    a.price::numeric AS price,
    a.quantity,
    COALESCE(a.reminder_of_quantity, COALESCE(b.quantity_reminder_sell, a.quantity)) AS reminder_of_quantity,
    a.value,
    a.market_price,
    a.market_value,
    a.mtm,
    COALESCE(b.realization, 0::numeric) AS realization,
    a.mtm + COALESCE(b.realization, 0::numeric) AS pnl,
    a.netting,
    a.mtm_eopd,
    a.mtm_eopy,
    a.year,
    a.month
   FROM mtm a
     LEFT JOIN realization b ON b.identification_description_sell::text = a.trade::text AND b.trading_interval_start = a.delivery_start
  ORDER BY a.trade, a.delivery_start;


-- trading_dw.v_pnl_for_book source

CREATE OR REPLACE VIEW trading_dw.v_pnl_for_book
AS WITH mtm AS (
         SELECT bk.book_name,
            bk.id AS book_id,
            t_trade_date.date AS trade_date,
                CASE
                    WHEN t.position_type::text = 'Buy'::text THEN company.short_name
                    ELSE counterparty.short_name
                END AS company_short_name,
                CASE
                    WHEN t.position_type::text = 'Buy'::text THEN counterparty.short_name
                    ELSE company.short_name
                END AS counterparty_short_name,
            t.position_type,
            dt.date AS delivery_day,
            ti_start_time.date_hour_minute_start AS delivery_start,
            tp.price,
            tp.quantity,
            mtm.reminder_of_quantity,
            tp.value,
            COALESCE(mtm.market_price, 0::numeric) AS market_price,
            COALESCE(mtm.market_price * mtm.reminder_of_quantity, 0::numeric) AS market_value,
            COALESCE(mtm.mtm, 0::numeric) AS mtm,
                CASE
                    WHEN t.position_type::text = 'Sell'::text THEN cin.zone_name
                    ELSE cout.zone_name
                END AS market_area,
            b_1.book_name AS trade_book,
            t.identification_description AS trade,
            pt.name AS trade_type,
            ''::text AS netting,
            COALESCE(mtm.mtm_previous_day, 0::numeric) AS mtm_eopd,
            COALESCE(mtm.mtm_previous_year, 0::numeric) AS mtm_eopy,
            date_part('year'::text, dt.date) AS year,
            to_char(dt.date::timestamp with time zone, 'YYYY-MM'::text) AS month,
            ti.start_time_id,
            ti_start_time.date_hour_minute_start
           FROM trading_dw.trade_prices tp
             JOIN trading_dw.trade t ON tp.identification_description::text = t.identification_description::text
             JOIN trading_dw.book bk ON t.trade_book_id = bk.id
             JOIN trading_dw.v_dim_date_met dt ON tp.delivery_day_id = dt.id
             JOIN trading_dw.book b_1 ON t.trade_book_id = b_1.id
             JOIN trading_dw.product p ON t.product_id = p.id
             JOIN trading_dw.product_type pt ON p.product_type_id = pt.id
             JOIN trading_dw.partner company ON t.company_id = company.id
             JOIN trading_dw.partner counterparty ON t.counterparty_id = counterparty.id
             JOIN trading_dw.trading_interval ti ON ti.id = tp.trading_interval_id
             JOIN trading_dw.v_dim_date_met t_trade_date ON t_trade_date.id = t.trade_date_id
             JOIN trading_dw.v_dim_time_met ti_start_time ON ti_start_time.id = ti.start_time_id
             JOIN trading_dw.control_area cin ON cin.id = t.in_control_area_id
             JOIN trading_dw.control_area cout ON cout.id = t.out_control_area_id
             LEFT JOIN trading_dw.v_mtm_book mtm ON mtm.trade::text = t.identification_description::text AND mtm.interval_start_time_id = ti_start_time.id AND mtm.calculation_date = CURRENT_DATE AND mtm.book_id = bk.id
        ), realization AS (
         SELECT a_1.book_id,
            a_1.identification_description_sell,
            a_1.trade_date_sell,
            a_1.trading_interval_start,
            a_1.trading_interval_end_sell,
            min(a_1.quantity_reminder_sell) AS quantity_reminder_sell,
            a_1.trade_date_id_sell,
            sum(a_1.realization) AS realization,
            a_1.trading_interval_start_id,
            a_1.trading_interval_end_id_sell,
            a_1.product_id_sell
           FROM trading_dw.v_realization_by_book a_1
          GROUP BY a_1.book_id, a_1.identification_description_sell, a_1.trade_date_sell, a_1.trading_interval_start, a_1.trading_interval_end_sell, a_1.quantity_sell, a_1.trade_date_id_sell, a_1.trading_interval_start_id, a_1.trading_interval_end_id_sell, a_1.product_id_sell
        )
 SELECT a.book_name,
    a.trade_date,
    a.company_short_name,
    a.counterparty_short_name,
    a.position_type,
    a.delivery_day,
    a.delivery_start,
    a.price::numeric AS price,
    a.quantity::numeric AS quantity,
    COALESCE(a.reminder_of_quantity, COALESCE(b.quantity_reminder_sell, a.quantity)) AS reminder_of_quantity,
    a.value::numeric AS value,
    a.market_price,
    a.market_value,
    a.mtm,
    COALESCE(b.realization, 0::numeric) AS realization,
    a.mtm + COALESCE(b.realization, 0::numeric) AS pnl,
    a.market_area,
    a.trade_book,
    a.trade,
    a.trade_type,
    a.netting,
    a.mtm_eopd,
    a.mtm_eopy,
    a.year,
    a.month
   FROM mtm a
     LEFT JOIN realization b ON b.identification_description_sell::text = a.trade::text AND b.trading_interval_start_id = a.start_time_id AND a.book_id = b.book_id
  ORDER BY a.trade, a.delivery_start;


-- trading_dw.v_pnl_for_company source

CREATE OR REPLACE VIEW trading_dw.v_pnl_for_company
AS WITH mtm AS (
         SELECT t_trade_date.date AS trade_date,
                CASE
                    WHEN t.position_type::text = 'Buy'::text THEN company.short_name
                    ELSE counterparty.short_name
                END AS company_short_name,
                CASE
                    WHEN t.position_type::text = 'Buy'::text THEN counterparty.short_name
                    ELSE company.short_name
                END AS counterparty_short_name,
            t.position_type,
            dt.date AS delivery_day,
            ti_start_time.date_hour_minute_start AS delivery_start,
            tp.price,
            tp.quantity,
            mtm.reminder_of_quantity,
            tp.value,
            COALESCE(mtm.market_price, 0::numeric) AS market_price,
            COALESCE(mtm.market_price * mtm.reminder_of_quantity, 0::numeric) AS market_value,
            COALESCE(mtm.mtm, 0::numeric) AS mtm,
                CASE
                    WHEN t.position_type::text = 'Sell'::text THEN cin.zone_name
                    ELSE cout.zone_name
                END AS market_area,
            b_1.book_name AS trade_book,
            tr.trader_name,
            t.identification_description AS trade,
            pt.name AS trade_type,
            ''::text AS netting,
            COALESCE(mtm.mtm_previous_day, 0::numeric) AS mtm_eopd,
            COALESCE(mtm.mtm_previous_year, 0::numeric) AS mtm_eopy,
            date_part('year'::text, dt.date) AS year,
            to_char(dt.date::timestamp with time zone, 'YYYY-MM'::text) AS month,
            ti.start_time_id,
            ti_start_time.date_hour_minute_start
           FROM trading_dw.trade_prices tp
             JOIN trading_dw.trade t ON tp.identification_description::text = t.identification_description::text
             JOIN trading_dw.trader tr ON t.trader_id = tr.id
             JOIN trading_dw.v_dim_date_met dt ON tp.delivery_day_id = dt.id
             JOIN trading_dw.book b_1 ON t.trade_book_id = b_1.id
             JOIN trading_dw.product p ON t.product_id = p.id
             JOIN trading_dw.product_type pt ON p.product_type_id = pt.id
             JOIN trading_dw.partner company ON t.company_id = company.id
             JOIN trading_dw.partner counterparty ON t.counterparty_id = counterparty.id
             JOIN trading_dw.trading_interval ti ON ti.id = tp.trading_interval_id
             JOIN trading_dw.v_dim_date_met t_trade_date ON t_trade_date.id = t.trade_date_id
             JOIN trading_dw.v_dim_time_met ti_start_time ON ti_start_time.id = ti.start_time_id
             JOIN trading_dw.control_area cin ON cin.id = t.in_control_area_id
             JOIN trading_dw.control_area cout ON cout.id = t.out_control_area_id
             LEFT JOIN trading_dw.v_mtm_company mtm ON mtm.trade::text = t.identification_description::text AND mtm.interval_start_time_id = ti_start_time.id AND mtm.calculation_date = CURRENT_DATE
        ), realization AS (
         SELECT a_1.identification_description_sell,
            a_1.trade_date_sell,
            a_1.trading_interval_start,
            a_1.trading_interval_end_sell,
            min(a_1.quantity_reminder_sell) AS quantity_reminder_sell,
            a_1.trade_date_id_sell,
            a_1.product_name_sell,
            sum(a_1.realization) AS realization,
            a_1.trading_interval_start_id,
            a_1.trading_interval_end_id_sell,
            a_1.product_id_sell
           FROM trading_dw.v_realization_by_company a_1
          GROUP BY a_1.identification_description_sell, a_1.trade_date_sell, a_1.trading_interval_start, a_1.trading_interval_end_sell, a_1.quantity_sell, a_1.trade_date_id_sell, a_1.product_name_sell, a_1.trading_interval_start_id, a_1.trading_interval_end_id_sell, a_1.product_id_sell
        )
 SELECT a.trade_date,
    a.company_short_name,
    a.counterparty_short_name,
    a.position_type,
    a.delivery_day,
    a.delivery_start,
    a.price::numeric AS price,
    a.quantity::numeric AS quantity,
    COALESCE(a.reminder_of_quantity, COALESCE(b.quantity_reminder_sell, a.quantity)) AS reminder_of_quantity,
    a.value::numeric AS value,
    a.market_price,
    a.market_value,
    a.mtm,
    COALESCE(b.realization, 0::numeric) AS realization,
    a.mtm + COALESCE(b.realization, 0::numeric) AS pnl,
    a.market_area,
    a.trade_book,
    a.trader_name,
    a.trade,
    a.trade_type,
    a.netting,
    a.mtm_eopd,
    a.mtm_eopy,
    a.year,
    a.month
   FROM mtm a
     LEFT JOIN realization b ON b.identification_description_sell::text = a.trade::text AND b.trading_interval_start_id = a.start_time_id
  ORDER BY a.trade, a.delivery_start;


-- trading_dw.v_pnl_for_trader source

CREATE OR REPLACE VIEW trading_dw.v_pnl_for_trader
AS WITH mtm AS (
         SELECT tr.trader_name,
            tr.id AS trader_id,
            t_trade_date.date AS trade_date,
                CASE
                    WHEN t.position_type::text = 'Buy'::text THEN company.short_name
                    ELSE counterparty.short_name
                END AS company_short_name,
                CASE
                    WHEN t.position_type::text = 'Buy'::text THEN counterparty.short_name
                    ELSE company.short_name
                END AS counterparty_short_name,
            t.position_type,
            dt.date AS delivery_day,
            ti_start_time.date_hour_minute_start AS delivery_start,
            tp.price,
            tp.quantity,
            mtm.reminder_of_quantity,
            tp.value,
            COALESCE(mtm.market_price, 0::numeric) AS market_price,
            COALESCE(mtm.market_price * mtm.reminder_of_quantity, 0::numeric) AS market_value,
            COALESCE(mtm.mtm, 0::numeric) AS mtm,
                CASE
                    WHEN t.position_type::text = 'Sell'::text THEN cin.zone_name
                    ELSE cout.zone_name
                END AS market_area,
            b_1.book_name AS trade_book,
            t.identification_description AS trade,
            pt.name AS trade_type,
            ''::text AS netting,
            COALESCE(mtm.mtm_previous_day, 0::numeric) AS mtm_eopd,
            COALESCE(mtm.mtm_previous_year, 0::numeric) AS mtm_eopy,
            date_part('year'::text, dt.date) AS year,
            to_char(dt.date::timestamp with time zone, 'YYYY-MM'::text) AS month,
            ti.start_time_id,
            ti_start_time.date_hour_minute_start
           FROM trading_dw.trade_prices tp
             JOIN trading_dw.trade t ON tp.identification_description::text = t.identification_description::text
             JOIN trading_dw.trader tr ON t.trader_id = tr.id
             JOIN trading_dw.v_dim_date_met dt ON tp.delivery_day_id = dt.id
             JOIN trading_dw.book b_1 ON t.trade_book_id = b_1.id
             JOIN trading_dw.product p ON t.product_id = p.id
             JOIN trading_dw.product_type pt ON p.product_type_id = pt.id
             JOIN trading_dw.partner company ON t.company_id = company.id
             JOIN trading_dw.partner counterparty ON t.counterparty_id = counterparty.id
             JOIN trading_dw.trading_interval ti ON ti.id = tp.trading_interval_id
             JOIN trading_dw.v_dim_date_met t_trade_date ON t_trade_date.id = t.trade_date_id
             JOIN trading_dw.v_dim_time_met ti_start_time ON ti_start_time.id = ti.start_time_id
             JOIN trading_dw.control_area cin ON cin.id = t.in_control_area_id
             JOIN trading_dw.control_area cout ON cout.id = t.out_control_area_id
             LEFT JOIN trading_dw.v_mtm_trader mtm ON mtm.trade::text = t.identification_description::text AND mtm.interval_start_time_id = ti_start_time.id AND mtm.calculation_date = CURRENT_DATE AND mtm.trader_id = tr.id
        ), realization AS (
         SELECT a_1.trader_id,
            a_1.identification_description_sell,
            a_1.trade_date_sell,
            a_1.trading_interval_start,
            a_1.trading_interval_end_sell,
            min(a_1.quantity_reminder_sell) AS quantity_reminder_sell,
            a_1.trade_date_id_sell,
            sum(a_1.realization) AS realization,
            a_1.trading_interval_start_id,
            a_1.trading_interval_end_id_sell,
            a_1.product_id_sell
           FROM trading_dw.v_realization_by_trader a_1
          GROUP BY a_1.trader_id, a_1.identification_description_sell, a_1.trade_date_sell, a_1.trading_interval_start, a_1.trading_interval_end_sell, a_1.quantity_sell, a_1.trade_date_id_sell, a_1.trading_interval_start_id, a_1.trading_interval_end_id_sell, a_1.product_id_sell
        )
 SELECT a.trader_name,
    a.trade_date,
    a.company_short_name,
    a.counterparty_short_name,
    a.position_type,
    a.delivery_day,
    a.delivery_start,
    a.price::numeric AS price,
    a.quantity::numeric AS quantity,
    COALESCE(a.reminder_of_quantity, COALESCE(b.quantity_reminder_sell, a.quantity)) AS reminder_of_quantity,
    a.value::numeric AS value,
    a.market_price,
    a.market_value,
    a.mtm,
    COALESCE(b.realization, 0::numeric) AS realization,
    a.mtm + COALESCE(b.realization, 0::numeric) AS pnl,
    a.market_area,
    a.trade_book,
    a.trade,
    a.trade_type,
    a.netting,
    a.mtm_eopd,
    a.mtm_eopy,
    a.year,
    a.month
   FROM mtm a
     LEFT JOIN realization b ON b.identification_description_sell::text = a.trade::text AND b.trading_interval_start_id = a.start_time_id AND a.trader_id = b.trader_id
  ORDER BY a.trade, a.delivery_start;


-- trading_dw.v_pnl_trader_sum source

CREATE OR REPLACE VIEW trading_dw.v_pnl_trader_sum
AS WITH mtm AS (
         SELECT a_1.trade,
            a_1.commodity,
            a_1.trade_type,
            a_1.trade_date,
            a_1.trade_book,
            a_1.trader_name,
            a_1.company_short_name,
            a_1.counterparty_short_name,
            a_1.market_area,
            a_1.position_type,
            a_1.delivery_day,
            a_1.delivery_start,
            a_1.price,
            a_1.quantity,
            COALESCE(mtm.reminder_of_quantity, 0::numeric) AS reminder_of_quantity,
            a_1.value,
            COALESCE(mtm.market_price, 0::numeric) AS market_price,
            COALESCE(mtm.market_price * mtm.reminder_of_quantity, 0::numeric) AS market_value,
            COALESCE(mtm.mtm, 0::numeric) AS mtm,
            ''::text AS netting,
            COALESCE(mtm.mtm_previous_day, 0::numeric) AS mtm_eopd,
            COALESCE(mtm.mtm_previous_year, 0::numeric) AS mtm_eopy,
            date_part('year'::text, a_1.trade_date) AS year,
            to_char(a_1.trade_date::timestamp with time zone, 'YYYY-MM'::text) AS month
           FROM trading_dw.v_trade_for_pnl a_1
             LEFT JOIN trading_dw.v_mtm_trader_sum mtm ON mtm.trade::text = a_1.trade::text AND mtm.calculation_date =
                CASE
                    WHEN EXTRACT(dow FROM (now() AT TIME ZONE 'UTC'::text)) = 1::numeric THEN ( SELECT x.date
                       FROM trading_dw.dim_date x
                      WHERE x.date = (date_trunc('day'::text, now() - '3 days'::interval) AT TIME ZONE 'UTC'::text))
                    ELSE ( SELECT x.date
                       FROM trading_dw.dim_date x
                      WHERE x.date = (date_trunc('day'::text, now() - '1 day'::interval) AT TIME ZONE 'UTC'::text))
                END
        ), realization AS (
         SELECT a_1.identification_description_sell,
            a_1.trade_date_sell,
            a_1.trading_interval_start,
            a_1.trading_interval_end_sell,
            a_1.quantity_reminder_sell,
            a_1.trade_date_id_sell,
            a_1.product_name_sell,
            a_1.realization,
            a_1.trading_interval_start_id,
            a_1.trading_interval_end_id_sell,
            a_1.product_id_sell
           FROM trading_dw.v_trader_realization_sum a_1
        )
 SELECT a.trade,
    a.commodity,
    a.trade_type,
    a.trade_book,
    a.trader_name,
    a.company_short_name,
    a.counterparty_short_name,
    a.market_area,
    a.trade_date,
    a.position_type,
    a.delivery_day,
    a.delivery_start,
    a.price::numeric AS price,
    a.quantity,
    COALESCE(a.reminder_of_quantity, COALESCE(b.quantity_reminder_sell, a.quantity)) AS reminder_of_quantity,
    a.value,
    a.market_price,
    a.market_value,
    a.mtm,
    COALESCE(b.realization, 0::numeric) AS realization,
    a.mtm + COALESCE(b.realization, 0::numeric) AS pnl,
    a.netting,
    a.mtm_eopd,
    a.mtm_eopy,
    a.year,
    a.month
   FROM mtm a
     LEFT JOIN realization b ON b.identification_description_sell::text = a.trade::text AND b.trading_interval_start = a.delivery_start
  ORDER BY a.trade, a.delivery_start;


-- trading_dw.v_realization_by_book source

CREATE OR REPLACE VIEW trading_dw.v_realization_by_book
AS SELECT g.book_name,
    a.identification_description_buy,
    a.identification_description_sell,
    b.date AS trade_date_buy,
    c.date AS trade_date_sell,
    d.date_hour_minute_start AS trading_interval_start,
    e.date_hour_minute_start AS trading_interval_end_buy,
    f.date_hour_minute_start AS trading_interval_end_sell,
    a.price_buy,
    a.price_sell,
    a.quantity_buy,
    a.quantity_sell,
    a.quantity_reminder_buy,
    a.quantity_reminder_sell,
    a.realization,
    p.name,
    a.trade_date_id_buy,
    a.trade_date_id_sell,
    a.buy_closed,
    a.sell_closed,
    a.trading_interval_start_id,
    a.trading_interval_end_id_buy,
    a.trading_interval_end_id_sell,
    a.book_id,
    a.product_id_buy,
    a.product_id_sell
   FROM trading_dw.realization_book a,
    trading_dw.v_dim_date_met b,
    trading_dw.v_dim_date_met c,
    trading_dw.v_dim_time_met d,
    trading_dw.v_dim_time_met e,
    trading_dw.v_dim_time_met f,
    trading_dw.book g,
    trading_dw.product p
  WHERE a.trading_interval_start_id = d.id AND a.trading_interval_end_id_buy = e.id AND a.trading_interval_end_id_sell = f.id AND a.trade_date_id_buy = b.id AND a.trade_date_id_sell = c.id AND a.book_id = g.id AND a.product_id_buy = p.id
  ORDER BY a.book_id, a.identification_description_buy, a.identification_description_sell, d.date_hour_minute_start;


-- trading_dw.v_realization_by_company source

CREATE OR REPLACE VIEW trading_dw.v_realization_by_company
AS SELECT a.identification_description_buy,
    a.identification_description_sell,
    b.date AS trade_date_buy,
    c.date AS trade_date_sell,
    d.date_hour_minute_start AS trading_interval_start,
    e.date_hour_minute_start AS trading_interval_end_buy,
    f.date_hour_minute_start AS trading_interval_end_sell,
    a.price_buy,
    a.price_sell,
    a.quantity_buy,
    a.quantity_sell,
    a.quantity_reminder_buy,
    a.quantity_reminder_sell,
    a.trade_date_id_buy,
    a.trade_date_id_sell,
    g.name AS product_name_sell,
    h.name AS product_name_buy,
    a.realization,
    a.buy_closed,
    a.sell_closed,
    a.trading_interval_start_id,
    a.trading_interval_end_id_buy,
    a.trading_interval_end_id_sell,
    a.product_id_buy,
    a.product_id_sell
   FROM trading_dw.realization_company a,
    trading_dw.v_dim_date_met b,
    trading_dw.v_dim_date_met c,
    trading_dw.v_dim_time_met d,
    trading_dw.v_dim_time_met e,
    trading_dw.v_dim_time_met f,
    trading_dw.product g,
    trading_dw.product h
  WHERE a.trading_interval_start_id = d.id AND a.trading_interval_end_id_buy = e.id AND a.trading_interval_end_id_sell = f.id AND a.trade_date_id_buy = b.id AND a.trade_date_id_sell = c.id AND g.id = a.product_id_sell AND h.id = a.product_id_buy
  ORDER BY a.identification_description_buy, a.identification_description_sell, d.date_hour_minute_start;


-- trading_dw.v_realization_by_cross source

CREATE OR REPLACE VIEW trading_dw.v_realization_by_cross
AS SELECT a.identification_description_buy,
    a.identification_description_sell,
    b.date AS trade_date_buy,
    c.date AS trade_date_sell,
    d.date_hour_minute_start AS trading_interval_start,
    e.date_hour_minute_start AS trading_interval_end_buy,
    f.date_hour_minute_start AS trading_interval_end_sell,
    a.price_buy,
    a.price_sell,
    a.quantity_buy,
    a.quantity_sell,
    a.quantity_reminder_buy,
    a.quantity_reminder_sell,
    a.trade_date_id_buy,
    a.trade_date_id_sell,
    a.realization,
    a.buy_closed,
    a.sell_closed,
    a.trading_interval_start_id,
    a.trading_interval_end_id_buy,
    a.trading_interval_end_id_sell
   FROM trading_dw.realization_company a,
    trading_dw.v_dim_date_met b,
    trading_dw.v_dim_date_met c,
    trading_dw.v_dim_time_met d,
    trading_dw.v_dim_time_met e,
    trading_dw.v_dim_time_met f
  WHERE a.trading_interval_start_id = d.id AND a.trading_interval_end_id_buy = e.id AND a.trading_interval_end_id_sell = f.id AND a.trade_date_id_buy = b.id AND a.trade_date_id_sell = c.id
  ORDER BY a.identification_description_buy, a.identification_description_sell, d.date_hour_minute_start;


-- trading_dw.v_realization_by_portfolio source

CREATE OR REPLACE VIEW trading_dw.v_realization_by_portfolio
AS SELECT g.portfolio_name,
    a.identification_description_buy,
    a.identification_description_sell,
    b.date AS trade_date_buy,
    c.date AS trade_date_sell,
    d.date_hour_minute_start AS trading_interval_start,
    e.date_hour_minute_start AS trading_interval_end_buy,
    f.date_hour_minute_start AS trading_interval_end_sell,
    a.price_buy,
    a.price_sell,
    a.quantity_buy,
    a.quantity_sell,
    a.quantity_reminder_buy,
    a.quantity_reminder_sell,
    a.realization,
    h.name AS product_name,
    a.trade_date_id_buy,
    a.trade_date_id_sell,
    a.buy_closed,
    a.sell_closed,
    a.trading_interval_start_id,
    a.trading_interval_end_id_buy,
    a.trading_interval_end_id_sell,
    a.portfolio_id,
    h.id AS product_id
   FROM trading_dw.realization_portfolio a,
    trading_dw.v_dim_date_met b,
    trading_dw.v_dim_date_met c,
    trading_dw.v_dim_time_met d,
    trading_dw.v_dim_time_met e,
    trading_dw.v_dim_time_met f,
    trading_dw.portfolio g,
    trading_dw.product h
  WHERE a.trading_interval_start_id = d.id AND a.trading_interval_end_id_buy = e.id AND a.trading_interval_end_id_sell = f.id AND a.trade_date_id_buy = b.id AND a.trade_date_id_sell = c.id AND a.portfolio_id = g.id AND a.product_id_buy = h.id
  ORDER BY a.portfolio_id, a.identification_description_buy, a.identification_description_sell, d.date_hour_minute_start;


-- trading_dw.v_realization_by_trader source

CREATE OR REPLACE VIEW trading_dw.v_realization_by_trader
AS SELECT g.trader_name,
    a.identification_description_buy,
    a.identification_description_sell,
    b.date AS trade_date_buy,
    c.date AS trade_date_sell,
    d.date_hour_minute_start AS trading_interval_start,
    e.date_hour_minute_start AS trading_interval_end_buy,
    f.date_hour_minute_start AS trading_interval_end_sell,
    a.price_buy,
    a.price_sell,
    a.quantity_buy,
    a.quantity_sell,
    a.quantity_reminder_buy,
    a.quantity_reminder_sell,
    a.realization,
    p.name,
    a.trade_date_id_buy,
    a.trade_date_id_sell,
    a.buy_closed,
    a.sell_closed,
    a.trading_interval_start_id,
    a.trading_interval_end_id_buy,
    a.trading_interval_end_id_sell,
    a.trader_id,
    a.product_id_buy,
    a.product_id_sell
   FROM trading_dw.realization_trader a,
    trading_dw.v_dim_date_met b,
    trading_dw.v_dim_date_met c,
    trading_dw.v_dim_time_met d,
    trading_dw.v_dim_time_met e,
    trading_dw.v_dim_time_met f,
    trading_dw.trader g,
    trading_dw.product p
  WHERE a.trading_interval_start_id = d.id AND a.trading_interval_end_id_buy = e.id AND a.trading_interval_end_id_sell = f.id AND a.trade_date_id_buy = b.id AND a.trade_date_id_sell = c.id AND a.trader_id = g.id AND a.product_id_buy = p.id
  ORDER BY a.trader_id, a.identification_description_buy, a.identification_description_sell, d.date_hour_minute_start;


-- trading_dw.v_settlement_price source

CREATE OR REPLACE VIEW trading_dw.v_settlement_price
AS SELECT pc.price_curve_name,
    p.id,
    p.price,
    dt.date_hour_minute_start
   FROM trading_dw.prices p
     JOIN trading_dw.v_dim_time_met dt ON p.time_id = dt.id
     JOIN trading_dw.price_curve pc ON p.price_curve_id = pc.id
  WHERE pc.price_curve_name::text ~~ 'EEX%'::text
  ORDER BY pc.price_curve_name, dt.date_hour_minute_start;


-- trading_dw.v_trade source

CREATE OR REPLACE VIEW trading_dw.v_trade
AS SELECT t_trade_date.date AS trade_date,
    t.transaction_time,
        CASE
            WHEN t.position_type::text = 'Buy'::text THEN company.short_name
            ELSE counterparty.short_name
        END AS company_short_name,
        CASE
            WHEN t.position_type::text = 'Buy'::text THEN counterparty.short_name
            ELSE company.short_name
        END AS counterparty_short_name,
    t.position_type,
    dt.date AS delivery_day,
    ti_start_time.date_hour_minute_start AS delivery_start,
    tp.price,
        CASE
            WHEN t.position_type::text = 'Sell'::text THEN tp.quantity * '-1'::integer::numeric
            ELSE tp.quantity
        END AS quantity,
        CASE
            WHEN t.position_type::text = 'Sell'::text THEN tp.value * '-1'::integer::numeric
            ELSE tp.value
        END AS value,
        CASE
            WHEN t.position_type::text = 'Sell'::text THEN cin.zone_name
            ELSE cout.zone_name
        END AS market_area,
    b_1.book_name AS trade_book,
    tr.trader_name,
    t.identification_description AS trade,
    pt.name AS trade_type,
    ''::text AS netting,
    t.general_agreement_contract_identification,
    c.currency_code AS currency,
    date_part('year'::text, dt.date) AS year,
    to_char(dt.date::timestamp with time zone, 'YYYY-MM'::text) AS month,
    ti.start_time_id,
    ti_start_time.date_hour_minute_start
   FROM trading_dw.trade_prices tp
     JOIN trading_dw.trade t ON tp.identification_description::text = t.identification_description::text
     JOIN trading_dw.trader tr ON t.trader_id = tr.id
     JOIN trading_dw.dim_date dt ON tp.delivery_day_id = dt.id
     JOIN trading_dw.book b_1 ON t.trade_book_id = b_1.id
     JOIN trading_dw.product p ON t.product_id = p.id
     JOIN trading_dw.product_type pt ON p.product_type_id = pt.id
     JOIN trading_dw.partner company ON t.company_id = company.id
     JOIN trading_dw.partner counterparty ON t.counterparty_id = counterparty.id
     JOIN trading_dw.trading_interval ti ON ti.id = tp.trading_interval_id
     JOIN trading_dw.v_dim_date_met t_trade_date ON t_trade_date.id = t.trade_date_id
     JOIN trading_dw.v_dim_time_met ti_start_time ON ti_start_time.id = ti.start_time_id
     JOIN trading_dw.currency c ON c.id = t.currency_id
     JOIN trading_dw.control_area cin ON cin.id = t.in_control_area_id
     JOIN trading_dw.control_area cout ON cout.id = t.out_control_area_id;


-- trading_dw.v_trade_for_pnl source

CREATE OR REPLACE VIEW trading_dw.v_trade_for_pnl
AS SELECT t.identification_description AS trade,
    (t.transaction_time AT TIME ZONE 'Europe/Ljubljana'::text) AS transaction_time,
    t_trade_date.date AS trade_date,
    t.position_type,
    min(dt.date) AS delivery_day,
    min(ti_start_time.date_hour_minute_start) AS delivery_start,
    p.name AS product_name,
    com.name AS commodity,
    p.id AS product_id,
    tp.price,
    sum(
        CASE
            WHEN t.position_type::text = 'Sell'::text THEN tp.quantity * '-1'::integer::numeric
            ELSE tp.quantity
        END) AS quantity,
    sum(
        CASE
            WHEN t.position_type::text = 'Sell'::text THEN tp.value * '-1'::integer::numeric
            ELSE tp.value
        END) AS value,
        CASE
            WHEN t.position_type::text = 'Sell'::text THEN cin.zone_name
            ELSE cout.zone_name
        END AS market_area,
        CASE
            WHEN t.position_type::text = 'Buy'::text THEN company.short_name
            ELSE counterparty.short_name
        END AS company_short_name,
        CASE
            WHEN t.position_type::text = 'Buy'::text THEN counterparty.short_name
            ELSE company.short_name
        END AS counterparty_short_name,
    b_1.book_name AS trade_book,
    tr.trader_name,
    pt.name AS trade_type,
    ''::text AS netting,
    t.general_agreement_contract_identification,
    c.currency_code AS currency
   FROM trading_dw.trade_prices tp
     JOIN trading_dw.trade t ON tp.identification_description::text = t.identification_description::text
     JOIN trading_dw.trader tr ON t.trader_id = tr.id
     JOIN trading_dw.dim_date dt ON tp.delivery_day_id = dt.id
     JOIN trading_dw.book b_1 ON t.trade_book_id = b_1.id
     JOIN trading_dw.product p ON t.product_id = p.id
     JOIN trading_dw.product_type pt ON p.product_type_id = pt.id
     JOIN trading_dw.partner company ON t.company_id = company.id
     JOIN trading_dw.partner counterparty ON t.counterparty_id = counterparty.id
     JOIN trading_dw.trading_interval ti ON ti.id = tp.trading_interval_id
     JOIN trading_dw.v_dim_date_met t_trade_date ON t_trade_date.id = t.trade_date_id
     JOIN trading_dw.v_dim_time_met ti_start_time ON ti_start_time.id = ti.start_time_id
     JOIN trading_dw.currency c ON c.id = t.currency_id
     JOIN trading_dw.control_area cin ON cin.id = t.in_control_area_id
     JOIN trading_dw.control_area cout ON cout.id = t.out_control_area_id
     JOIN trading_dw.commodity com ON com.id = p.commodity_id
  GROUP BY t_trade_date.date, company.short_name, counterparty.short_name, t.position_type, cin.zone_name, cout.zone_name, b_1.book_name, tr.trader_name, p.name, tp.price, com.name, p.id, t.identification_description, pt.name, t.general_agreement_contract_identification, c.currency_code;


-- trading_dw.v_trader_realization_sum source

CREATE OR REPLACE VIEW trading_dw.v_trader_realization_sum
AS WITH min_reminder_buy AS (
         SELECT a_1.identification_description_buy,
            a_1.identification_description_sell,
            a_1.trading_interval_start_id,
            min(a_1.quantity_reminder_buy) AS quantity_reminder_buy,
            min(a_1.quantity_reminder_sell) AS quantity_reminder_sell
           FROM trading_dw.realization_trader a_1
          GROUP BY a_1.identification_description_buy, a_1.identification_description_sell, a_1.trading_interval_start_id
        ), sum_realization AS (
         SELECT a_1.identification_description_buy,
            a_1.identification_description_sell,
            a_1.trading_interval_start_id,
            sum(a_1.realization) AS realization
           FROM trading_dw.realization_trader a_1
          GROUP BY a_1.identification_description_buy, a_1.identification_description_sell, a_1.trading_interval_start_id
        )
 SELECT tr.trader_name,
    a.identification_description_buy,
    a.identification_description_sell,
    b.date AS trade_date_buy,
    c.date AS trade_date_sell,
    min(d.date_hour_minute_start) AS trading_interval_start,
    max(e.date_hour_minute_start) AS trading_interval_end_buy,
    max(f.date_hour_minute_start) AS trading_interval_end_sell,
    a.price_buy,
    a.price_sell,
    sum(a.quantity_buy) AS quantity_buy,
    sum(a.quantity_buy) AS quantity_sell,
    sum(mb.quantity_reminder_buy) AS quantity_reminder_buy,
    sum(mb.quantity_reminder_sell) AS quantity_reminder_sell,
    sum(r.realization) AS realization,
    a.trade_date_id_buy,
    a.trade_date_id_sell,
    g.name AS product_name_sell,
    h.name AS product_name_buy,
    a.buy_closed,
    a.sell_closed,
    min(a.trading_interval_start_id) AS trading_interval_start_id,
    max(a.trading_interval_end_id_buy) AS trading_interval_start_id_buy,
    max(a.trading_interval_end_id_sell) AS trading_interval_end_id_sell,
    a.product_id_buy,
    a.product_id_sell,
    a.trader_id
   FROM trading_dw.realization_trader a
     JOIN trading_dw.v_dim_date_met b ON a.trade_date_id_buy = b.id
     JOIN trading_dw.v_dim_date_met c ON a.trade_date_id_sell = c.id
     JOIN trading_dw.v_dim_time_met d ON a.trading_interval_start_id = d.id
     JOIN trading_dw.v_dim_time_met e ON a.trading_interval_end_id_buy = e.id
     JOIN trading_dw.v_dim_time_met f ON a.trading_interval_end_id_sell = f.id
     JOIN trading_dw.product g ON a.product_id_sell = g.id
     JOIN trading_dw.product h ON a.product_id_buy = h.id
     JOIN trading_dw.trader tr ON a.trader_id = tr.id
     JOIN min_reminder_buy mb ON a.identification_description_buy::text = mb.identification_description_buy::text AND a.identification_description_sell::text = mb.identification_description_sell::text AND a.trading_interval_start_id = mb.trading_interval_start_id
     JOIN sum_realization r ON a.identification_description_buy::text = r.identification_description_buy::text AND a.identification_description_sell::text = r.identification_description_sell::text AND a.trading_interval_start_id = r.trading_interval_start_id
  GROUP BY tr.trader_name, a.identification_description_buy, a.identification_description_sell, b.date, c.date, a.price_buy, a.price_sell, a.trade_date_id_buy, a.trade_date_id_sell, g.name, h.name, a.buy_closed, a.sell_closed, a.product_id_buy, a.product_id_sell, a.trader_id
  ORDER BY a.identification_description_buy, a.identification_description_sell, b.date, tr.trader_name;


-- trading_dw.v_trader_realization_sum_risk source

CREATE OR REPLACE VIEW trading_dw.v_trader_realization_sum_risk
AS WITH min_reminder_buy AS (
         SELECT a_1.identification_description_buy,
            a_1.identification_description_sell,
            a_1.trading_interval_start_id,
            min(a_1.quantity_reminder_buy) AS quantity_reminder_buy,
            min(a_1.quantity_reminder_sell) AS quantity_reminder_sell
           FROM trading_dw.realization_trader a_1
          GROUP BY a_1.identification_description_buy, a_1.identification_description_sell, a_1.trading_interval_start_id
        ), sum_realization AS (
         SELECT a_1.identification_description_buy,
            a_1.identification_description_sell,
            a_1.trading_interval_start_id,
            sum(a_1.realization) AS realization
           FROM trading_dw.realization_trader a_1
          GROUP BY a_1.identification_description_buy, a_1.identification_description_sell, a_1.trading_interval_start_id
        )
 SELECT a.identification_description_buy,
    a.identification_description_sell,
    cm.name AS commodity,
    trader.trader_name AS trader_name_buy,
    trader_sell.trader_name AS trader_name_sell,
    book.book_name AS book_name_buy,
    book_sell.book_name AS book_name_sell,
    company.short_name AS company_short_name_buy,
    counterparty.short_name AS counterparty_short_name_buy,
    company_sell.short_name AS company_short_name_sell,
    counterparty_sell.short_name AS counterparty_short_name_sell,
    cout.zone_name AS market_area_buy,
    cin.zone_name AS market_area_sell,
    b.date AS trade_date_buy,
    c.date AS trade_date_sell,
    min(d.date_hour_minute_start) AS trading_interval_start,
    max(e.date_hour_minute_start) AS trading_interval_end_buy,
    max(f.date_hour_minute_start) AS trading_interval_end_sell,
    a.price_buy,
    a.price_sell,
    sum(a.quantity_buy) AS quantity_buy,
    sum(a.quantity_buy) AS quantity_sell,
    sum(mb.quantity_reminder_buy) AS quantity_reminder_buy,
    sum(mb.quantity_reminder_sell) AS quantity_reminder_sell,
    sum(r.realization) AS realization,
    a.trade_date_id_buy,
    a.trade_date_id_sell,
    g.name AS product_name_sell,
    h.name AS product_name_buy,
    a.buy_closed,
    a.sell_closed,
    min(a.trading_interval_start_id) AS trading_interval_start_id,
    max(a.trading_interval_end_id_buy) AS trading_interval_start_id_buy,
    max(a.trading_interval_end_id_sell) AS trading_interval_end_id_sell,
    a.product_id_buy,
    a.product_id_sell
   FROM trading_dw.realization_trader a
     JOIN trading_dw.v_dim_date_met b ON a.trade_date_id_buy = b.id
     JOIN trading_dw.v_dim_date_met c ON a.trade_date_id_sell = c.id
     JOIN trading_dw.v_dim_time_met d ON a.trading_interval_start_id = d.id
     JOIN trading_dw.v_dim_time_met e ON a.trading_interval_end_id_buy = e.id
     JOIN trading_dw.v_dim_time_met f ON a.trading_interval_end_id_sell = f.id
     JOIN trading_dw.product g ON a.product_id_sell = g.id
     JOIN trading_dw.product h ON a.product_id_buy = h.id
     JOIN trading_dw.trade t_buy ON t_buy.identification_description::text = a.identification_description_buy::text
     JOIN trading_dw.trade t_sell ON t_sell.identification_description::text = a.identification_description_sell::text
     JOIN trading_dw.trader trader ON t_buy.trader_id = trader.id
     JOIN trading_dw.trader trader_sell ON t_sell.trader_id = trader_sell.id
     JOIN trading_dw.book book ON t_buy.trade_book_id = book.id
     JOIN trading_dw.book book_sell ON t_sell.trade_book_id = book_sell.id
     JOIN trading_dw.partner company ON t_buy.company_id = company.id
     JOIN trading_dw.partner company_sell ON t_sell.company_id = company_sell.id
     JOIN trading_dw.partner counterparty ON t_buy.counterparty_id = counterparty.id
     JOIN trading_dw.partner counterparty_sell ON t_sell.counterparty_id = counterparty_sell.id
     JOIN trading_dw.control_area cin ON cin.id = t_buy.in_control_area_id
     JOIN trading_dw.control_area cout ON cout.id = t_sell.out_control_area_id
     JOIN trading_dw.commodity cm ON h.commodity_id = cm.id
     JOIN min_reminder_buy mb ON a.identification_description_buy::text = mb.identification_description_buy::text AND a.identification_description_sell::text = mb.identification_description_sell::text AND a.trading_interval_start_id = mb.trading_interval_start_id
     JOIN sum_realization r ON a.identification_description_buy::text = r.identification_description_buy::text AND a.identification_description_sell::text = r.identification_description_sell::text AND a.trading_interval_start_id = r.trading_interval_start_id
  GROUP BY a.identification_description_buy, a.identification_description_sell, b.date, c.date, a.price_buy, a.price_sell, a.trade_date_id_buy, a.trade_date_id_sell, g.name, h.name, a.buy_closed, a.sell_closed, a.product_id_buy, a.product_id_sell, cm.name, trader.trader_name, book.book_name, company.short_name, counterparty.short_name, cout.zone_name, trader_sell.trader_name, book_sell.book_name, company_sell.short_name, counterparty_sell.short_name, cin.zone_name
  ORDER BY a.identification_description_buy, a.identification_description_sell, c.date;