from pyexcel_io import save_data
from pyexcel_io.constants import DB_SQL
from pyexcel_io.database.common import SQLTableImporter, SQLTableImportAdapter
from sqlalchemy import MetaData, create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from pyexcel_io._compact import OrderedDict
from pyexcel_xlsx import get_data
from Config.ConfigReader import ConfigReader
import os

import logging

logger = logging.getLogger(__name__)


class SetupOrmUtilityV2:

    def __init__(self, config_name):
        self.config_db = ConfigReader.get_config_section(config_name, "db")
        self.config_db_orm = ConfigReader.get_config_section(config_name, "db_orm")

        self.engine = create_engine("postgresql+psycopg://{user}:{password}@{host}:{port}/{dbname}"
                                    .format(user=self.config_db['user'],
                                            password=self.config_db['password'],
                                            host=self.config_db['host'],
                                            port=self.config_db['port'],
                                            dbname=self.config_db['dbname']),
                                    connect_args={'connect_timeout': self.config_db['connection_timeout']})

    def import_data(self, init_fun, table_name, type_name, data=None):

        if not self.data:
            return

        session = self._get_session()

        importer = SQLTableImporter(session)

        if data is None:
            data = {table_name: self.data[table_name]}

        adapter = SQLTableImportAdapter(type_name)
        adapter.column_names = data[table_name][0]
        adapter.row_initializer = init_fun
        importer.append(adapter)

        to_store = OrderedDict()
        to_store.update({adapter.get_name(): data[table_name][1:]})

        save_data(importer, to_store, file_type=DB_SQL)

        session.close()
        self.codes_session.close()

    def _get_session(self):
        Base = declarative_base(metadata=MetaData(schema=self.config_db_orm['schema']))

        engine = create_engine("postgresql+psycopg2://{user}:{password}@{host}:{port}/{dbname}"
                               .format(user=self.config_db['user'],
                                       password=self.config_db['password'],
                                       host=self.config_db['host'],
                                       port=self.config_db['port'],
                                       dbname=self.config_db['dbname']),
                               connect_args={'connect_timeout': self.config_db['connection_timeout']})

        Base.metadata.create_all(engine)

        Session = sessionmaker(bind=engine)

        return Session()

    def key_exists(self, list, key):
        return any(row and row[0] == key for row in list)
