from Dimensions.Currency.currency_model import CurrencyModel
from pyexcel_io.database.querysets import QuerysetsReader
from libs.setup_orm_utility import SetupOrmUtility

import logging

logger = logging.getLogger(__name__)

class CurrencyImporter():

    def __init__(self, config_name):
        self.setup_orm_utility = SetupOrmUtility(config_name)
        query_sets = self.setup_orm_utility.codes_session.query(CurrencyModel).all()


        reader = QuerysetsReader(query_sets, ["currency_numeric_code"])
        self.existing_currencies = list(reader.to_array())

    def import_data(self):
        self.rows_inserted = 0

        logger.info('Import started')

        self.setup_orm_utility.import_data(self.currency_init_func, 'currency',CurrencyModel)

        logger.info('Import ended. Inserted %s rows', self.rows_inserted)

    def currency_init_func(self,row):

        if self.setup_orm_utility.key_exists (self.existing_currencies, row['currency_numeric_code']):
            return None

        self.rows_inserted += 1

        return CurrencyModel(
                        row['currency_code'],
                        row['currency_name'],
                        row['currency_numeric_code'])
