from sqlalchemy import (
    Column,
    Integer,
    String,
    Numeric,
    Boolean,
    ForeignKey,
    DateTime,
    func
)
from sqlalchemy.orm import relationship
from setup_global import SetupGlobal
from libs.mixin import AuditMixin
from Dimensions.DimDate.dim_date_model import DimDateModel as DimDate
from Dimensions.DimTime.dim_time_model import DimTimeModel as DimTime
from Dimensions.Product.product_model import ProductModel as Product
from Dimensions.Book.book_model import BookModel as Book
from Dimensions.Prices.prices_model import PricesModel as Prices


class MtmBookModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'mtm_book'

    id = Column(Integer, primary_key=True, autoincrement=True)
    calculation_date_id = Column(Integer, ForeignKey('trading_dw.dim_date.id'), nullable=False)
    book_id = Column(Integer, ForeignKey('trading_dw.book.id'), nullable=False)
    identification_description = Column(String(13), nullable=False)
    delivery_day_id = Column(Integer, ForeignKey('trading_dw.dim_date.id'), nullable=False)
    trading_interval_start_id = Column(Integer, ForeignKey('trading_dw.dim_time.id'), nullable=False)
    trading_interval_end_id = Column(Integer, ForeignKey('trading_dw.dim_time.id'), nullable=True)
    position_type = Column(String(10), nullable=False)
    price = Column(Numeric(18, 6), nullable=True)
    quantity = Column(Numeric(18, 6), nullable=False)
    reminder_of_quantity = Column(Numeric(18, 6), nullable=False)
    trade_date_id = Column(Integer, nullable=False)
    product_id = Column(Integer, ForeignKey('trading_dw.product.id'), nullable=False)
    market_price_id = Column(Integer, ForeignKey('trading_dw.prices.id'), nullable=False)
    mtm = Column(Numeric(18, 6), nullable=True)
    mtm_previous_year = Column(Numeric(18, 6), nullable=False, default=0)
    mtm_previous_day = Column(Numeric(18, 6), nullable=False, default=0)
    trade_closed = Column(Boolean, nullable=False)
    created_at = Column(DateTime(timezone=True), default=func.now(), nullable=True)
    modified_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now(), nullable=True)

    delivery_date = relationship(DimDate,
                            foreign_keys=[delivery_day_id],
                            backref='mtm_book_delivery_dates')

    calculation_date = relationship(DimDate,
                                foreign_keys=[calculation_date_id],
                                backref='mtm_book_calculation_dates')

    trading_interval_start = relationship(DimTime,
                                        foreign_keys=[trading_interval_start_id],
                                        backref='mtm_book_interval_starts')

    trading_interval_end = relationship(DimTime,
                                    foreign_keys=[trading_interval_end_id],
                                    backref='mtm_book_interval_ends')

    product = relationship(Product,
                        foreign_keys=[product_id],
                        backref='mtm_book_products')

    book = relationship(Book,
                        foreign_keys=[book_id],
                        backref='mtm_books')

    market_price = relationship(Prices,
                            foreign_keys=[market_price_id],
                            backref='mtm_book_market_prices')

    def __init__(
        self,
        calculation_date_id,
        book_id,
        identification_description,
        delivery_day_id,
        trading_interval_start_id,
        trading_interval_end_id,
        position_type,
        price,
        quantity,
        reminder_of_quantity,
        trade_date_id,
        product_id,
        market_price_id,
        mtm,
        mtm_previous_year,
        mtm_previous_day,
        trade_closed,
    ):
        self.calculation_date_id = calculation_date_id
        self.book_id = book_id
        self.identification_description = identification_description
        self.delivery_day_id = delivery_day_id
        self.trading_interval_start_id = trading_interval_start_id
        self.trading_interval_end_id = trading_interval_end_id
        self.position_type = position_type
        self.price = price
        self.quantity = quantity
        self.reminder_of_quantity = reminder_of_quantity
        self.trade_date_id = trade_date_id
        self.product_id = product_id
        self.market_price_id = market_price_id
        self.mtm = mtm
        self.mtm_previous_year = mtm_previous_year
        self.mtm_previous_day = mtm_previous_day
        self.trade_closed = trade_closed
