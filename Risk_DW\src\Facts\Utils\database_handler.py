import psycopg

class DatabaseHandler:
    """
    Class to handle database operations.
    """

    @staticmethod
    def read_data_from_database(sel, db_params):
        """
        Read data from the database.

        Parameters:
            guuid (str): Asset GUID.
            db_params (dict): Database connection parameters.

        Returns:
            dict: Dictionary containing guuid and BESS capacity.
        """
        # Establish connection to the database
        conn = psycopg.connect(**db_params)
        cursor = conn.cursor()

        # Define query to fetch data
        query = sel
        cursor.execute(query)
        result = cursor.fetchall()

        # Close database connection
        conn.close()

        # Process query result
        return result