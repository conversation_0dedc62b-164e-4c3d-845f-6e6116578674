from sqlalchemy import  Column, Integer, String
from setup_global import SetupGlobal
from libs.mixin import AuditMixin

class CurrencyModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'currency'
    id = Column(Integer, primary_key=True)
    currency_code = Column(String(3))
    currency_name = Column(String(100))
    currency_numeric_code = Column(Integer)


    def __init__(self,
                currency_code,
                currency_name,
                currency_numeric_code):

        self.currency_code = currency_code
        self.currency_name = currency_name
        self.currency_numeric_code = currency_numeric_code

