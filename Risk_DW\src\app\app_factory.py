from dependency_injector import containers, providers
from libs.setup_orm_utility_v2 import SetupOrmUtilityV2
from Facts.facts_manager import FactsManager
from Dimensions.dimensions_manager import DimensionsManager
from Facts.RealizationService.realization_company_service import RealizationCompanyService
from Facts.RealizationService.realization_trader_service import RealizationTraderService
from Facts.RealizationService.realization_book_service import RealizationBookService
from Facts.RealizationService.realization_cross_service import RealizationCrossService
from Facts.RealizationService.realization_portfolio_service import RealizationPortfolioService
from Facts.MTM.company_mtm import CompanyMTM
from Facts.MTM.trader_mtm import TraderMTM
from Facts.MTM.book_mtm import BookMTM
from Facts.Calculations.calc_service import CalculationHistory
from app.utils.logging_config import setup_logging
from app.utils.mail_service import MailService

class Container(containers.DeclarativeContainer):
    config = providers.Configuration()
    logger = providers.Singleton(setup_logging)

    database = providers.Singleton(SetupOrmUtilityV2, config_name=config.config_name)
    facts_manager = providers.Factory(FactsManager, config_name=config.config_name, database=database)
    dimensions_manager = providers.Factory(DimensionsManager, config_name=config.config_name, database=database)
    realization_company_service = providers.Factory(RealizationCompanyService, database=database)
    realization_trader_service = providers.Factory(RealizationTraderService, database=database)
    realization_book_service = providers.Factory(RealizationBookService, database=database)
    realization_cross_service = providers.Factory(RealizationCrossService, database=database)
    realization_portfolio_service = providers.Factory(RealizationPortfolioService, database=database)
    company_mtm_service = providers.Factory(CompanyMTM, database=database)
    trader_mtm_service = providers.Factory(TraderMTM, database=database)
    book_mtm_service = providers.Factory(BookMTM, database=database)
    calculation_history_service = providers.Factory(CalculationHistory, database=database)
    mail_service = providers.Factory(MailService)