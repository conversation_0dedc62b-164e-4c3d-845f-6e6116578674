import time
import logging
import traceback
from datetime import datetime, timedelta, timezone
from dateutil.relativedelta import relativedelta

logger = logging.getLogger(__name__)

def import_prices(container):
    start_time = time.time()
    container.dimensions_manager().import_prices()
    logger.info('Prices import took %s seconds', round(time.time() - start_time))

def update_prices(container):
    start_time = time.time()
    container.dimensions_manager().update_prices()
    logger.info('Prices import took %s seconds', round(time.time() - start_time))

def import_trades(container):
    try:
        start_exec_time = time.time()
        container.dimensions_manager().import_exchange_rate(datetime(2020, 1, 2, tzinfo=timezone.utc).date())
        logger.info('Exchange rate import took %s seconds', round(time.time() - start_exec_time))
        start_exectime = time.time()

        now = datetime.now(tz=timezone.utc)
        if now.weekday() == 0:  # Monday
            days_to_subtract = 3
        else:
            days_to_subtract = 1
        start_time = now.replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=days_to_subtract)
        end_time = now.replace(hour=23, minute=59, second=59, microsecond=999999) - timedelta(days=0)

        container.facts_manager().import_facts(start_time, end_time)

        logger.info('Trades import took %s seconds', round(time.time() - start_exec_time))
    except Exception as e:
        logger.error('Trading import fails: %s', traceback.format_exc())

def merge_settlement_prices(container):
    start_time = time.time()
    logger.info('Settlement prices import started')
    container.dimensions_manager().merge_settlement_prices()
    logger.info('Settlement prices import took %s seconds', round(time.time() - start_time))
