from sqlalchemy import  Text, Column, Integer, String
from setup_global import SetupGlobal
from libs.mixin import AuditMixin

class BookModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'book'
    id = Column(Integer, primary_key=True)
    book_code = Column(String(50))
    book_name = Column(String(100))
    description = Column(Text)


    def __init__(self,
                book_code,
                book_name,
                description):

        self.book_code = book_code
        self.book_name = book_name
        self.description = description
