from sqlalchemy import Column, Integer, String, ForeignKey
from sqlalchemy.orm import relationship
from setup_global import SetupGlobal
from libs.mixin import AuditMixin


class CodeModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'code'

    id = Column(Integer, primary_key=True, autoincrement=True)
    code_name = Column(String(50), nullable=False, unique=True)
    description = Column(String(255), nullable=True)

    code_values = relationship('CodeValueModel', back_populates='code', cascade='all, delete-orphan')

    def __init__(self, code_name, description=None):
        self.code_name = code_name
        self.description = description
