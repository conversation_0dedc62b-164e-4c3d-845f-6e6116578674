from sqlalchemy import Column, Integer, Numeric, String, <PERSON><PERSON><PERSON>, Text, ForeignKey
from sqlalchemy.orm import relationship
from setup_global import SetupGlobal
from libs.mixin import AuditMixin

class TradePricesModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'trade_prices'
    id = Column(Integer, primary_key=True)
    identification_description = Column(String(13), ForeignKey("trade.identification_description"))
    delivery_day_id = Column(Integer)
    price = Column(Numeric(18, 6))
    quantity = Column(Numeric(18, 6))
    value = Column(Numeric(18, 6))
    netting = Column(String(1))
    trading_interval_id = Column(Integer, ForeignKey("trading_interval.id"))
    reserved_flag = Column(Boolean, default=False)

    # Relationships
    trade = relationship("TradeModel", back_populates="trade_prices")
    trading_interval = relationship("TradingIntervalModel", back_populates="trade_prices")

    def __init__(self, identification_description, delivery_day_id, price, quantity, value, netting,
                 trading_interval_id):
        self.identification_description = identification_description
        self.delivery_day_id = delivery_day_id
        self.price = price
        self.quantity = quantity
        self.value = value
        self.netting = netting
        self.trading_interval_id = trading_interval_id
