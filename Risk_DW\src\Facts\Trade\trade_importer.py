from datetime import datetime, timed<PERSON><PERSON>
from sqlalchemy import and_, or_
from sqlalchemy.orm import Session
from collections import defaultdict
import pandas as pd
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from dateutil import tz
from typing import List, Dict, Set, Optional
from zoneinfo import ZoneInfo

from Facts.Trade.trade_model import TradeModel
from Dimensions.Partner.partner_model import PartnerModel
from Dimensions.Currency.currency_model import CurrencyModel
from Dimensions.DimDate.dim_date_model import DimDateModel
from Dimensions.DimTime.dim_time_model import DimTimeModel
from Dimensions.TradingPeriod.trading_period_model import TradingPeriodModel
from Dimensions.MarketType.market_type_model import MarketTypeModel
from Dimensions.ControlArea.control_area_model import ControlAreaModel
from Dimensions.Book.book_model import BookModel
from Dimensions.Trader.trader_model import TraderModel
from Facts.TradingInterval.trading_interval import TradingIntervalModel
from Dimensions.LoadProfile.load_profile_model import LoadProfileModel
from Dimensions.Product.product_model import ProductModel
from Dimensions.ProductType.product_type_model import ProductTypeModel
from Dimensions.PriceCurve.price_curve_model import PriceCurveModel
from Facts.TradePrices.trade_prices_model import TradePricesModel
from Facts.PeriodIntervals.period_intervals_model import PeriodIntervalsModel
from Facts.TradeMetadataModel.trade_metadata_model import TradeMetadataModel
from Facts.TradeFees.trade_fees_model import TradeFeesModel
from libs.dw_utility import DwUtility
from libs.blue_trader_api import BlueTraderAPI
from libs.interval_prices_service import IntervalPricesService

logger = logging.getLogger(__name__)


class TradeImporter:
    def __init__(self, config_name: str, database):
        self.database = database
        self._blue_trader_api = BlueTraderAPI(config_name)
        self.config_name = config_name
        self.cached_hpfc_prices = defaultdict(dict)
        self.cached_prices = defaultdict(dict)
        self.current_trading_interval_id = 0
        self.interval_prices_service = None
        self._preload_dimensions()

    def _preload_dimensions(self):
        """Preload dimension data to reduce database queries."""
        with Session(self.database.engine) as session:
            self.companies = {c.short_name: c for c in session.query(PartnerModel).all()}
            self.currencies = {c.currency_code: c for c in session.query(CurrencyModel).all()}
            self.trading_periods = {t.id: t for t in session.query(TradingPeriodModel).all()}
            self.control_areas = {ca.control_area_name: ca for ca in session.query(ControlAreaModel).all()}
            self.market_types = {mt.code: mt for mt in session.query(MarketTypeModel).all()}
            self.trade_books = {tb.book_code: tb for tb in session.query(BookModel).all()}
            self.traders = {t.trader_name: t for t in session.query(TraderModel).all()}
            self.load_profiles = {lp.name.strip(): lp for lp in session.query(LoadProfileModel).all()}
            self.products = {p.id: p for p in session.query(ProductModel).all()}
            self.product_types = {pt.id: pt for pt in session.query(ProductTypeModel).all()}
            self.price_curves = {pc.id: pc for pc in session.query(PriceCurveModel).all()}
            self.trading_intervals = session.query(TradingIntervalModel).all()
            self.current_trading_interval_id = max((ti.id for ti in self.trading_intervals), default=0)

    def import_data(self, start_date: datetime, end_date: datetime):

        #physical_trades_data = self._blue_trader_api.get_physical_power_trades_data(start_date, end_date)
        #if physical_trades_data is not None:
        #    self.time_intervals = DwUtility.get_time_intervals(physical_trades_data)
        #    self.interval_prices_service = IntervalPricesService(self.time_intervals.periods_min_time,
        #                                                         self.time_intervals.periods_max_time,
        #                                                         self._blue_trader_api)
        #    self._import_trades_data(physical_trades_data, 'Physical')


        """Import financial trades data."""
        financial_trades_data = self._blue_trader_api.get_financial_power_trades_data(start_date, end_date)
        if financial_trades_data is not None:
            filtered_trades = self.filter_existing_trades(financial_trades_data)

            if filtered_trades:
                self.time_intervals = DwUtility.get_time_intervals(filtered_trades, 'Electricity')
                self.interval_prices_service = IntervalPricesService(
                    self.time_intervals.periods_min_time,
                    self.time_intervals.periods_max_time,
                    self._blue_trader_api
                )
                self._import_trades_data(filtered_trades, 'Electricity')

        """Import Natural gas trades data."""
        financial_gas_trades_data = self._blue_trader_api.get_financial_gas_trades_data(start_date, end_date)
        if financial_gas_trades_data is not None:
            filtered_gas_trades = self.filter_existing_trades(financial_gas_trades_data)

            if filtered_gas_trades:
                self.time_intervals = DwUtility.get_time_intervals(financial_gas_trades_data, 'Gas')
                self.interval_prices_service = IntervalPricesService(
                    self.time_intervals.periods_min_time,
                    self.time_intervals.periods_max_time,
                    self._blue_trader_api
                )
                self._import_trades_data(filtered_gas_trades, 'Gas')

        """Import CO2 trades data."""
        financial_co2_trades_data = self._blue_trader_api.get_financial_co2_trades_data(start_date, end_date)
        if financial_co2_trades_data is not None:
            filtered_co2_trades = self.filter_existing_trades(financial_co2_trades_data)

            if filtered_co2_trades:
                self.time_intervals = DwUtility.get_time_intervals(financial_co2_trades_data, 'CO2')
                self.interval_prices_service = IntervalPricesService(
                    self.time_intervals.periods_min_time,
                    self.time_intervals.periods_max_time,
                    self._blue_trader_api
                )
                self._import_trades_data(filtered_co2_trades, 'CO2')


    def _import_trades_data(self, trades_data: List[Dict], product_type: str):
        """Process and insert trades data."""
        logger.info('Fetching DimDates and DimTimes...')
        with Session(self.database.engine) as session:
            self.trade_dates = {d.date: d for d in session.query(DimDateModel).filter(
                and_(DimDateModel.date >= self.time_intervals.min_time, DimDateModel.date <= self.time_intervals.max_time)
            ).all()}
            self.trade_times = {t.date_hour_minute_start: t for t in session.query(DimTimeModel).filter(
                and_(DimTimeModel.date_hour_minute_start >= self.time_intervals.min_time,
                     DimTimeModel.date_hour_minute_start <= self.time_intervals.max_time)
            ).all()}

        trade_list, trading_intervals_list, periods_list, fees_list = self._process_trades(trades_data, product_type)

        if trade_list or trading_intervals_list or periods_list or fees_list:
            with Session(self.database.engine) as session:
                session.bulk_save_objects(trade_list)
                session.bulk_save_objects(trading_intervals_list)
                session.bulk_save_objects(periods_list)
                session.bulk_save_objects(fees_list)
                session.commit()
                logger.info(
                    'Committed: %s trading intervals, %s periods, %s trades',
                    len(trading_intervals_list), len(periods_list), len(trade_list)
                )
        else:
                logger.error("No data to commit to the database.")

    def _process_trades(self, trades_data: List[Dict], product_type: str):
        """Process trades in parallel."""
        if not trades_data:
            logger.error("No trades data to process.")
            return [], [], []

        trade_list = []
        trading_intervals_list = []
        periods_list = []
        fees_list = []

        #----------FOR TESTING PURPOSES - SNGLE THREAD FOR EASIER DEBUGGING--------------
        #for trade in trades_data:
        #    trade_metadata, periods_trading_intervals, fees = self._process_trade(trade, product_type)

        #    if trade_metadata:
        #        trade_list.append(trade_metadata.trade)
        #        trading_intervals_list.extend(periods_trading_intervals.trading_intervals)
        #        periods_list.extend(periods_trading_intervals.periods)
        #        fees_list.extend(fees)
        #---------------------------------------------------------------------------------

        with ThreadPoolExecutor() as executor:
            futures = [executor.submit(self._process_trade, trade, product_type) for trade in trades_data]
            for future in as_completed(futures):
                trade_metadata, periods_trading_intervals, fees = future.result()
                if trade_metadata:
                    trade_list.append(trade_metadata.trade)
                    trading_intervals_list.extend(periods_trading_intervals.trading_intervals)
                    periods_list.extend(periods_trading_intervals.periods)
                    fees_list.extend(fees)


        logger.info(f"Processed {len(trade_list)} trades, {len(trading_intervals_list)} trading intervals, and {len(periods_list)} periods.")
        return trade_list, trading_intervals_list, periods_list, fees_list

    def _process_trade(self, trade: Dict, product_type: str):
        """Process a single trade."""
        logger.debug('Processing trade "%s"', trade['identificationDescription'])
        trade_metadata = self._get_trade(trade, product_type)
        logger.debug('Metadata for trade "%s" is %s', trade['identificationDescription'], trade_metadata)
        if trade_metadata:
            logger.debug('Processing periods for trade "%s"', trade['identificationDescription'])
            periods_trading_intervals = self._get_periods(trade, trade_metadata.trading_period, product_type)
            logger.debug('Processing fees for trade "%s"', trade['identificationDescription'])
            fees = self._get_trade_fees(trade)
            logger.info('Processed trade "%s"', trade['identificationDescription'])
            return trade_metadata, periods_trading_intervals, fees
        return None, None, None

    def _get_trade(self, trade: Dict, product_type: str) -> Optional[TradeMetadataModel]:
        """Extract trade metadata."""
        load_profile_val = DwUtility.get_mapped_load_profile(trade['deliveryShape'])
        load_profile_record = self.load_profiles.get(load_profile_val)
        if not load_profile_record:
            logger.error('Cannot parse load profile "%s". Skipping trade "%s".', load_profile_val, trade['identificationDescription'])
            return None

        buyer_party_record = self.companies.get(trade['buyerPartyDisplayName'])
        seller_party_record = self.companies.get(trade['sellerPartyDisplayName'])
        if not buyer_party_record or not seller_party_record:
            logger.error('Missing company. Skipping trade "%s"', trade['identificationDescription'])
            return None

        in_control_area_record = self.control_areas.get(trade['inAreaShortCode'])
        out_control_area_record = self.control_areas.get(trade['outAreaShortCode'])
        if product_type == 'Gas':
            in_control_area_record = self.control_areas.get('TTF')
            out_control_area_record = self.control_areas.get('TTF')

        if product_type == product_type == 'CO2':
            in_control_area_record = self.control_areas.get('EUA')
            out_control_area_record = self.control_areas.get('EUA')

        if not in_control_area_record or not out_control_area_record:
            logger.error('Missing control area. Skipping trade "%s"', trade['identificationDescription'])
            return None

        trade_book_val = trade['toCustomerBookDescription'] or trade['fromCustomerBookDescription']
        trade_book_record = self.trade_books.get(trade_book_val)
        if not trade_book_record:
            logger.error('Missing trade book "%s"', trade_book_val)
            return None

        trader_val = trade['buyerRepresentativeName'] or trade['sellerRepresentativeName']
        trader_record = self.traders.get(trader_val)
        if not trader_record:
            logger.error('Missing trader "%s"', trader_val)
            return None

        currency_record = self.currencies.get(trade['currencyCode'])
        if not currency_record:
            logger.error('Missing currency "%s"', trade['currencyCode'])
            return None

        if product_type != 'CO2':
            area = trade['productCode'].split('_')[4]
        else:
            area = trade['productCode'].split('_')[2]
        product_country_code = next((ca.zone_name for ca in self.control_areas.values() if ca.control_area_name == area), None)

        position_type = 'Buy' if buyer_party_record.entity_type == 'LocalParty' else 'Sell'
        market_type_val = trade['sellerMarketSegmentCode'] if trade.get('productCode') is None else DwUtility.get_mapped_market_type(trade['productCode'])
        market_type_record = self.market_types.get(market_type_val)
        if not market_type_record:
            logger.error('Missing market type "%s"', market_type_val)
            return None

        if not trade.get('periods') and product_type != 'Gas' and product_type != 'CO2':
            logger.error('Trade "%s" does not contain periods', trade['identificationDescription'])
            return None
        if product_type == 'Gas':
            resolution_seconds = 86400
            product_type_trade = 'Financial'
            product_country_code = area
        elif product_type == 'CO2':
            resolution_seconds = 2592000
            product_type_trade = 'Financial'
            product_country_code = area
        elif product_type == 'Electricity' or product_type == 'Financial':
            resolution_seconds = trade['periods'][0]['resolutionSeconds']
            product_type_trade = 'Financial'

        product = DwUtility.get_product(
            self.products,
            self.product_types,
            product_type_trade,
            product_country_code,
            market_type_record,
            load_profile_record,
            trade['timeIntervalFrom'],
            trade['timeIntervalTo'],
            resolution_seconds,
            self.trading_periods
        )
        if not product:
            return None

        trade_date_record = self.trade_dates.get(datetime.strptime(trade['clearingDateTime'][:10], '%Y-%m-%d').date())
        if not trade_date_record:
            logger.error('Missing trade date "%s"', trade['clearingDateTime'])
            return None

        return TradeMetadataModel(
            trade=TradeModel(
                identification_description=trade['identificationDescription'],
                trade_date_id=trade_date_record.id,
                company_id=buyer_party_record.id,
                currency_id=currency_record.id,
                counterparty_id=seller_party_record.id,
                position_type=position_type,
                in_control_area_id=in_control_area_record.id,
                out_control_area_id=out_control_area_record.id,
                trade_book_id=trade_book_record.id,
                trader_id=trader_record.id,
                trade_name=trader_record.trader_name,
                trade_type=product_type_trade,
                product_id=product[0].id,
                general_agreement_contract_identification=trade['generalAgreementContractIdentification'],
                transaction_time=datetime.fromisoformat(trade['clearingDateTime']),
            ),
            product_country_code=product_country_code,
            trading_period=product[1],
            position_type=position_type
        )

    def _get_periods(self, trade: Dict, trading_period, type: str = 'Electricity') -> PeriodIntervalsModel:
        """Extract periods and trading intervals."""
        periods = []
        trading_intervals = []
        existing_periods = set()

        if type == 'Electricity':
            for period in trade.get('periods', []):
                start_interval_time = datetime.fromisoformat(period['timeIntervalFrom'])
                end_period_time = datetime.fromisoformat(period['timeIntervalTo'])
                intervals = period.get('intervals', [])
                current_interval_idx = 0
                previous_price = 0

                while start_interval_time < end_period_time:
                    if current_interval_idx < len(intervals):
                        interval = intervals[current_interval_idx]
                        current_interval_idx += 1
                        price = self.interval_prices_service.get_price(trade, start_interval_time, previous_price, interval.get('price'))
                        end_interval_time = datetime.fromisoformat(period['timeIntervalFrom']) + pd.to_timedelta(interval['pos'] * period['resolutionSeconds'], unit='s')
                    else:
                        price = self.interval_prices_service.get_price(trade, start_interval_time, previous_price)
                        end_interval_time = start_interval_time + timedelta(seconds=period['resolutionSeconds'])

                    previous_price = price
                    quantity = interval.get('qty', 0)
                    value = price * quantity

                    if value <= 0:
                        start_interval_time = end_interval_time
                        continue

                    delivery_day_record = self.trade_dates.get(end_interval_time.date())
                    if not delivery_day_record:
                        logger.error('Missing delivery day "%s"', end_interval_time.strftime('%Y-%m-%d'))
                        start_interval_time = end_interval_time
                        continue

                    current_trading_interval = self._get_trading_interval(start_interval_time, end_interval_time, trading_period.id)
                    if not current_trading_interval.id:
                        existing_trading_interval = next((ti for ti in trading_intervals if ti.start_time_id == current_trading_interval.start_time_id and ti.end_time_id == current_trading_interval.end_time_id), None)
                        if existing_trading_interval:
                            current_trading_interval = existing_trading_interval
                        else:
                            self.current_trading_interval_id += 1
                            current_trading_interval.id = self.current_trading_interval_id
                            trading_intervals.append(current_trading_interval)

                    identifier = (trade['identificationDescription'], delivery_day_record.id, current_trading_interval.id, current_trading_interval.start_time_id, current_trading_interval.end_time_id)
                    if identifier in existing_periods:
                        logger.info("Duplicate detected: %s", identifier)
                        start_interval_time = end_interval_time
                        continue

                    existing_periods.add(identifier)
                    periods.append(
                        TradePricesModel(
                            identification_description=trade['identificationDescription'],
                            delivery_day_id=delivery_day_record.id,
                            price=price,
                            quantity=quantity,
                            value=value,
                            netting='N',
                            trading_interval_id=current_trading_interval.id
                        )
                    )

                    start_interval_time = end_interval_time
        else:
            ljubljana_tz = ZoneInfo("Europe/Ljubljana")
            utc_tz = ZoneInfo("UTC")
            start_interval_time = datetime.fromisoformat(trade['timeIntervalFrom'])
            end_period_time = datetime.fromisoformat(trade['timeIntervalTo'])
            if type == 'CO2':
                resolution = 2678400    # Monthly
                quantity = trade.get('totalQty')
            else:
                resolution = 86400      # Daily
                quantity = trade.get('qty') * 24

            price = trade.get('price')
            value = price * quantity

            while start_interval_time < end_period_time:
                end_interval_time = start_interval_time.replace(tzinfo=utc_tz).astimezone(ljubljana_tz) + timedelta(seconds=resolution)
                end_interval_time = end_interval_time.replace(tzinfo=ljubljana_tz).astimezone(utc_tz)

                delivery_day_record = self.trade_dates.get(end_interval_time.date())
                if not delivery_day_record:
                    logger.error('Missing delivery day "%s"', end_interval_time.strftime('%Y-%m-%d'))
                    start_interval_time = end_interval_time
                    continue

                current_trading_interval = self._get_trading_interval(start_interval_time, end_interval_time, trading_period.id)
                if not current_trading_interval.id:
                    existing_trading_interval = next((ti for ti in trading_intervals if ti.start_time_id == current_trading_interval.start_time_id and ti.end_time_id == current_trading_interval.end_time_id), None)
                    if existing_trading_interval:
                        current_trading_interval = existing_trading_interval
                    else:
                        self.current_trading_interval_id += 1
                        current_trading_interval.id = self.current_trading_interval_id
                        trading_intervals.append(current_trading_interval)

                identifier = (trade['identificationDescription'], delivery_day_record.id, current_trading_interval.id, current_trading_interval.start_time_id, current_trading_interval.end_time_id)
                if identifier in existing_periods:
                    logger.info("Duplicate detected: %s", identifier)
                    start_interval_time = end_interval_time
                    continue

                existing_periods.add(identifier)
                periods.append(
                    TradePricesModel(
                        identification_description=trade['identificationDescription'],
                        delivery_day_id=delivery_day_record.id,
                        price=price,
                        quantity=quantity,
                        value=value,
                        netting='N',
                        trading_interval_id=current_trading_interval.id
                    )
                )

                start_interval_time = end_interval_time

        return PeriodIntervalsModel(periods, trading_intervals)

    def _get_trading_interval(self, start_interval: datetime, end_interval: datetime, trading_period_id: int) -> TradingIntervalModel:
        """Get or create a trading interval."""
        trading_interval_start_date = self.trade_dates.get(start_interval.date())
        trading_interval_end_date = self.trade_dates.get(end_interval.date())
        trading_interval_start_time = self.trade_times.get(start_interval)
        trading_interval_end_time = self.trade_times.get(end_interval)

        if not all([trading_interval_start_date, trading_interval_end_date, trading_interval_start_time, trading_interval_end_time]):
            logger.error('Missing trading interval data for start: %s, end: %s', start_interval, end_interval)
            return TradingIntervalModel()

        existing_trading_interval = next(
            (ti for ti in self.trading_intervals if ti.start_time_id == trading_interval_start_time.id and ti.end_time_id == trading_interval_end_time.id),
            None
        )

        if existing_trading_interval:
            return existing_trading_interval

        return TradingIntervalModel(
            trading_period_id=trading_period_id,
            start_date_id=trading_interval_start_date.id,
            end_date_id=trading_interval_end_date.id,
            start_time_id=trading_interval_start_time.id,
            end_time_id=trading_interval_end_time.id,
            description=None
        )

    def filter_existing_trades(self, financial_trades_data: List[Dict]) -> List[Dict]:
        """Filter out trades that already exist in the database."""
        incoming_ids = [trade['identificationDescription'] for trade in financial_trades_data]

        with Session(self.database.engine) as session:
            existing_ids = set(
                result[0] for result in session.query(TradeModel.identification_description)
                .filter(TradeModel.identification_description.in_(incoming_ids))
                .all()
            )
            return [
                trade for trade in financial_trades_data
                if trade['identificationDescription'] not in existing_ids
                and trade['transactionType'] != 'FinancialPowerInternalBookTransfer'
            ]

    def _get_trade_fees(self, trade: Dict) -> List[Optional[TradeFeesModel]]:
        fees = []
        identification_description = trade.get("identificationDescription")

        if "billingPlans" not in trade or trade["billingPlans"] is None:
            logger.error('Missing billing plans. Skipping trade "%s"', identification_description)
            return []

        for billing_plan in trade["billingPlans"]:
            billing_from = self.trade_times.get(datetime.fromisoformat(billing_plan["billingPeriodFrom"]))
            billing_to = self.trade_times.get(datetime.fromisoformat(billing_plan["billingPeriodTo"]))
            payment_due = self.trade_times.get(datetime.fromisoformat(billing_plan["paymentDueDate"]))
            invoice_issue_date = self.trade_times.get(datetime.fromisoformat(invoice_date_str)) if (invoice_date_str := billing_plan.get("invoiceIssueDate")) else None

            if not billing_from or not billing_to or not payment_due:
                continue

            if not ('fee' in (billing_plan.get("generalAgreementIdentification") or '') or billing_plan["issuedByPartyDisplayName"] == 'UniCredit Bank (DE)'):
                continue

            issued_by_party = self.companies.get(billing_plan["issuedByPartyDisplayName"])
            issued_to_party = self.companies.get(billing_plan["issuedToPartyDisplayName"])
            currency = self.currencies.get(billing_plan["invoiceCurrencyCode"])

            if not issued_by_party or not issued_to_party or not currency:
                logger.error('Missing entity reference. Skipping fee for trade "%s"', identification_description)
                continue

            fee = TradeFeesModel(
                identification_description=identification_description,
                billing_period_from_id=billing_from.id,
                billing_period_to_id=billing_to.id,
                total_qty=billing_plan["totalQty"],
                total_amount_excluding_tax=billing_plan["totalAmountExcludingTax"],
                tax_amount=billing_plan["taxAmount"],
                issued_by_party_id=issued_by_party.id,
                issued_to_party_id=issued_to_party.id,
                invoice_issue_date_id=invoice_issue_date.id if invoice_issue_date else None,
                payment_due_date_id=payment_due.id,
                netting_date=datetime.fromisoformat(billing_plan["nettingDate"]) if billing_plan["nettingDate"] else None,
                invoice_currency_code_id=currency.id,
                general_agreement_identification=billing_plan["generalAgreementIdentification"]
            )
            fees.append(fee)

        return fees