from sqlalchemy import  Column, Integer, String, Date
from setup_global import SetupGlobal
from libs.mixin import AuditMixin

class ContractModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'contract'
    id = Column(Integer, primary_key=True)
    contract_label = Column(String(100))
    contract_date = Column(Date)
    year_of_contract_date = Column(Integer)
    month_of_contract_date = Column(Integer)
    week_iso_of_contract_date = Column(Integer)


    def __init__(self,
                contract_label,
                contract_date,
                year_of_contract_date,
                month_of_contract_date,
                week_iso_of_contract_date):

        self.contract_label = contract_label
        self.contract_date = contract_date
        self.year_of_contract_date = year_of_contract_date
        self.month_of_contract_date = month_of_contract_date
        self.week_iso_of_contract_date = week_iso_of_contract_date
