class Queries:
    @staticmethod
    def open_buys():
        query = f"""
        with unrealized as
	        (
        	select a.identification_description_buy,
        		   a.trading_interval_start_id,
        		   a.product_id_buy as product_id,
        		   min(a.quantity_reminder_buy) reminder
        	from trading_dw.realization_company a
        		group by a.identification_description_buy,
        		   	     a.trading_interval_start_id,
        		   	     a.product_id_buy
        		having min(a.quantity_reminder_buy) > 0
	        ),
        	buys as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, b.quantity as reminder_of_quantity, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id,
                a.trader_id,
                b.delivery_day_id,
                a.position_type,
                0 as buy_exists,
				CASE
				    WHEN p.commodity_id = 1 THEN CONCAT('HPFC', ' ', SUBSTRING(p.name FROM 1 FOR 2))
				    WHEN p.commodity_id = 2 THEN
				    	CASE
				            WHEN p.name LIKE 'TTF%' THEN 'GAS HPFC NL'
				            WHEN p.name LIKE 'NCG%' THEN 'GAS HPFC DE'
				            WHEN p.name LIKE 'GPL%' THEN 'GAS HPFC DE'
				            WHEN p.name LIKE 'NBP%' THEN 'GAS HPFC GB'
				            WHEN p.name LIKE 'VTP%' THEN 'GAS HPFC AT'
				            WHEN p.name LIKE 'USDMMBTU%' THEN 'GAS HPFC USDMMBTU NL'
			            ELSE NULL
			        END
				    WHEN p.commodity_id = 3 THEN 'CO2 PFC EU'
				END AS price_curve,
                CASE
                    WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
                        (SELECT x.id
                        FROM trading_dw.dim_date x
                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
                    ELSE
                        (SELECT x.id
                        FROM trading_dw.dim_date x
                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
                END AS calculation_date_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g,
                trading_dw.product p
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Buy'
            and a.trade_date_id = c.id
            and a.product_id = p.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '2 DAY')) - interval '1 hour'
            and not exists (select 1 from trading_dw.realization_company x
            				where x.identification_description_buy = a.identification_description
            					and x.trading_interval_start_id = d.id
                                and x.product_id_buy = a.product_id)
            ),
            unrealized_buys as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, x.reminder as reminder_of_quantity, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id,
                a.trader_id,
                b.delivery_day_id,
                a.position_type,
                1 as buy_exists,
				CASE
				    WHEN p.commodity_id = 1 THEN CONCAT('HPFC', ' ', SUBSTRING(p.name FROM 1 FOR 2))
				    WHEN p.commodity_id = 2 THEN
				    	CASE
				            WHEN p.name LIKE 'TTF%' THEN 'GAS HPFC NL'
				            WHEN p.name LIKE 'NCG%' THEN 'GAS HPFC DE'
				            WHEN p.name LIKE 'GPL%' THEN 'GAS HPFC DE'
				            WHEN p.name LIKE 'NBP%' THEN 'GAS HPFC GB'
				            WHEN p.name LIKE 'VTP%' THEN 'GAS HPFC AT'
				            WHEN p.name LIKE 'USDMMBTU%' THEN 'GAS HPFC USDMMBTU NL'
			            ELSE NULL
			        END
				    WHEN p.commodity_id = 3 THEN 'CO2 PFC EU'
				END AS price_curve,
                CASE
                    WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
                        (SELECT x.id
                        FROM trading_dw.dim_date x
                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
                    ELSE
                        (SELECT x.id
                        FROM trading_dw.dim_date x
                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
                END AS calculation_date_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g,
                trading_dw.product p,
                unrealized x
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Buy'
            and a.trade_date_id = c.id
            and a.product_id = p.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '2 DAY')) - interval '1 hour'
            and x.identification_description_buy = a.identification_description
            and x.trading_interval_start_id = d.id
            and a.product_id = x.product_id
            )
            select a.identification_description, a.transaction_date, a.delivery_date, a.interval_start, a.interval_end,
            a.quantity, a.price, a.reminder_of_quantity, a.position_type, a.interval_start_id, a.interval_end_id,
            a.trade_date_id, a.price_curve,
            a.product_id, a.trader_id, a.delivery_day_id, a.calculation_date_id,
            mtm.mtm as mtm_previous_day, mtm.mtm as mtm_previous_year, a.buy_exists
	        from buys as a
        	left outer join trading_dw.mtm_company as mtm on a.identification_description = mtm.identification_description
        												  and a.interval_start_id = mtm.trading_interval_start_id
        												  and mtm.calculation_date_id =
												                CASE
												                    WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
												                        (SELECT x.id
												                        FROM trading_dw.dim_date x
												                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
												                    ELSE
												                        (SELECT x.id
												                        FROM trading_dw.dim_date x
												                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
												                END
			union all
            select a.identification_description, a.transaction_date, a.delivery_date, a.interval_start, a.interval_end,
            a.quantity, a.price, a.reminder_of_quantity, a.position_type, a.interval_start_id, a.interval_end_id,
            a.trade_date_id, a.price_curve,
            a.product_id, a.trader_id, a.delivery_day_id, a.calculation_date_id,
            mtm.mtm as mtm_previous_day, mtm.mtm as mtm_previous_year, a.buy_exists
	        from unrealized_buys as a
        	left outer join trading_dw.mtm_company as mtm on a.identification_description = mtm.identification_description
        												  and a.interval_start_id = mtm.trading_interval_start_id
        												  and mtm.calculation_date_id =
												                CASE
												                    WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
												                        (SELECT x.id
												                        FROM trading_dw.dim_date x
												                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
												                    ELSE
												                        (SELECT x.id
												                        FROM trading_dw.dim_date x
												                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
												                END
            order by 2, 1, 3, 4
        """
        return query

    @staticmethod
    def open_sells():
        query = f"""
        with unrealized as
	        (
        	select a.identification_description_sell,
        		   a.trading_interval_start_id,
        		   a.product_id_sell as product_id,
        		   min(a.quantity_reminder_sell) reminder
        	from trading_dw.realization_company a
        		group by a.identification_description_sell,
        		   	     a.trading_interval_start_id,
        		   	     a.product_id_sell
        		having min(a.quantity_reminder_sell) > 0
	        ),
        	sells as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, b.quantity as reminder_of_quantity, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id,
                a.trader_id,
                b.delivery_day_id,
                a.position_type,
                0 as sell_exists,
				CASE
				    WHEN p.commodity_id = 1 THEN CONCAT('HPFC', ' ', SUBSTRING(p.name FROM 1 FOR 2))
				    WHEN p.commodity_id = 2 THEN
				    	CASE
				            WHEN p.name LIKE 'TTF%' THEN 'GAS HPFC NL'
				            WHEN p.name LIKE 'NCG%' THEN 'GAS HPFC DE'
				            WHEN p.name LIKE 'GPL%' THEN 'GAS HPFC DE'
				            WHEN p.name LIKE 'NBP%' THEN 'GAS HPFC GB'
				            WHEN p.name LIKE 'VTP%' THEN 'GAS HPFC AT'
				            WHEN p.name LIKE 'USDMMBTU%' THEN 'GAS HPFC USDMMBTU NL'
			            ELSE NULL
			        END
				    WHEN p.commodity_id = 3 THEN 'CO2 PFC EU'
				END AS price_curve,
                CASE
                    WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
                        (SELECT x.id
                        FROM trading_dw.dim_date x
                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
                    ELSE
                        (SELECT x.id
                        FROM trading_dw.dim_date x
                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
                END AS calculation_date_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g,
                trading_dw.product p
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Sell'
            and a.trade_date_id = c.id
            and a.product_id = p.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '2 DAY')) - interval '1 hour'
            and not exists (select 1 from trading_dw.realization_company x
            				where x.identification_description_sell = a.identification_description
            					and x.trading_interval_start_id = d.id
                                and x.product_id_sell = a.product_id)
            ),
            unrealized_sells as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, x.reminder as reminder_of_quantity, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id,
                a.trader_id,
                b.delivery_day_id,
                a.position_type,
                1 as buy_exists,
				CASE
				    WHEN p.commodity_id = 1 THEN CONCAT('HPFC', ' ', SUBSTRING(p.name FROM 1 FOR 2))
				    WHEN p.commodity_id = 2 THEN
				    	CASE
				            WHEN p.name LIKE 'TTF%' THEN 'GAS HPFC NL'
				            WHEN p.name LIKE 'NCG%' THEN 'GAS HPFC DE'
				            WHEN p.name LIKE 'GPL%' THEN 'GAS HPFC DE'
				            WHEN p.name LIKE 'NBP%' THEN 'GAS HPFC GB'
				            WHEN p.name LIKE 'VTP%' THEN 'GAS HPFC AT'
				            WHEN p.name LIKE 'USDMMBTU%' THEN 'GAS HPFC USDMMBTU NL'
			            ELSE NULL
			        END
				    WHEN p.commodity_id = 3 THEN 'CO2 PFC EU'
				END AS price_curve,
                CASE
                    WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
                        (SELECT x.id
                        FROM trading_dw.dim_date x
                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
                    ELSE
                        (SELECT x.id
                        FROM trading_dw.dim_date x
                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
                END AS calculation_date_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g,
                trading_dw.product p,
                unrealized x
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Sell'
            and a.trade_date_id = c.id
            and a.product_id = p.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '2 DAY')) - interval '1 hour'
            and x.identification_description_sell = a.identification_description
            and x.trading_interval_start_id = d.id
            and a.product_id = x.product_id
            )
            select a.identification_description, a.transaction_date, a.delivery_date, a.interval_start, a.interval_end,
            a.quantity, a.price, a.reminder_of_quantity, a.position_type, a.interval_start_id, a.interval_end_id,
            a.trade_date_id, a.price_curve,
            a.product_id, a.trader_id, a.delivery_day_id, a.calculation_date_id,
            mtm.mtm as mtm_previous_day, mtm.mtm as mtm_previous_year, a.sell_exists
	        from sells as a
        	left outer join trading_dw.mtm_company as mtm on a.identification_description = mtm.identification_description
        												  and a.interval_start_id = mtm.trading_interval_start_id
        												  and mtm.calculation_date_id =
												                CASE
												                    WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
												                        (SELECT x.id
												                        FROM trading_dw.dim_date x
												                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
												                    ELSE
												                        (SELECT x.id
												                        FROM trading_dw.dim_date x
												                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
												                END
			union all
            select a.identification_description, a.transaction_date, a.delivery_date, a.interval_start, a.interval_end,
            a.quantity, a.price, a.reminder_of_quantity, a.position_type, a.interval_start_id, a.interval_end_id,
            a.trade_date_id, a.price_curve,
            a.product_id, a.trader_id, a.delivery_day_id, a.calculation_date_id,
            mtm.mtm as mtm_previous_day, mtm.mtm as mtm_previous_year, a.buy_exists
	        from unrealized_sells as a
        	left outer join trading_dw.mtm_company as mtm on a.identification_description = mtm.identification_description
        												  and a.interval_start_id = mtm.trading_interval_start_id
        												  and mtm.calculation_date_id =
												                CASE
												                    WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
												                        (SELECT x.id
												                        FROM trading_dw.dim_date x
												                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
												                    ELSE
												                        (SELECT x.id
												                        FROM trading_dw.dim_date x
												                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
												                END
            order by 2, 1, 3, 4
        """
        return query

    @staticmethod
    def open_buys_trader():
        query = f"""
        with unrealized as
	        (
        	select a.identification_description_buy,
        		   a.trading_interval_start_id,
        		   a.product_id_buy as product_id,
        		   a.trader_id,
        		   min(a.quantity_reminder_buy) reminder
        	from trading_dw.realization_trader a
        		group by a.identification_description_buy,
        		   	     a.trading_interval_start_id,
        		   	     a.product_id_buy, trader_id
        		having min(a.quantity_reminder_buy) > 0
	        ),
        	buys as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, b.quantity as reminder_of_quantity, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id,
                a.trader_id,
                b.delivery_day_id,
                a.position_type,
                0 as buy_exists,
				CASE
				    WHEN p.commodity_id = 1 THEN CONCAT('HPFC', ' ', SUBSTRING(p.name FROM 1 FOR 2))
				    WHEN p.commodity_id = 2 THEN
				    	CASE
				            WHEN p.name LIKE 'TTF%' THEN 'GAS HPFC NL'
				            WHEN p.name LIKE 'NCG%' THEN 'GAS HPFC DE'
				            WHEN p.name LIKE 'GPL%' THEN 'GAS HPFC DE'
				            WHEN p.name LIKE 'NBP%' THEN 'GAS HPFC GB'
				            WHEN p.name LIKE 'VTP%' THEN 'GAS HPFC AT'
				            WHEN p.name LIKE 'USDMMBTU%' THEN 'GAS HPFC USDMMBTU NL'
			            ELSE NULL
			        END
				    WHEN p.commodity_id = 3 THEN 'CO2 PFC EU'
				END AS price_curve,
                CASE
                    WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
                        (SELECT x.id
                        FROM trading_dw.dim_date x
                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
                    ELSE
                        (SELECT x.id
                        FROM trading_dw.dim_date x
                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
                END AS calculation_date_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g,
                trading_dw.product p
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Buy'
            and a.trade_date_id = c.id
            and a.product_id = p.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '2 DAY')) - interval '1 hour'
            and not exists (select 1 from trading_dw.realization_trader x
            				where x.identification_description_buy = a.identification_description
            					and x.trading_interval_start_id = d.id
                                and x.product_id_buy = a.product_id
                                and x.trader_id = a.trader_id)
            ),
            unrealized_buys as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, x.reminder as reminder_of_quantity, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id,
                a.trader_id,
                b.delivery_day_id,
                a.position_type,
                1 as buy_exists,
				CASE
				    WHEN p.commodity_id = 1 THEN CONCAT('HPFC', ' ', SUBSTRING(p.name FROM 1 FOR 2))
				    WHEN p.commodity_id = 2 THEN
				    	CASE
				            WHEN p.name LIKE 'TTF%' THEN 'GAS HPFC NL'
				            WHEN p.name LIKE 'NCG%' THEN 'GAS HPFC DE'
				            WHEN p.name LIKE 'GPL%' THEN 'GAS HPFC DE'
				            WHEN p.name LIKE 'NBP%' THEN 'GAS HPFC GB'
				            WHEN p.name LIKE 'VTP%' THEN 'GAS HPFC AT'
				            WHEN p.name LIKE 'USDMMBTU%' THEN 'GAS HPFC USDMMBTU NL'
			            ELSE NULL
			        END
				    WHEN p.commodity_id = 3 THEN 'CO2 PFC EU'
				END AS price_curve,
                CASE
                    WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
                        (SELECT x.id
                        FROM trading_dw.dim_date x
                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
                    ELSE
                        (SELECT x.id
                        FROM trading_dw.dim_date x
                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
                END AS calculation_date_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g,
                trading_dw.product p,
                unrealized x
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Buy'
            and a.trade_date_id = c.id
            and a.product_id = p.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '2 DAY')) - interval '1 hour'
            and x.identification_description_buy = a.identification_description
            and x.trading_interval_start_id = d.id
            and a.product_id = x.product_id
            and x.trader_id = a.trader_id
            )
            select a.identification_description, a.transaction_date, a.delivery_date, a.interval_start, a.interval_end,
            a.quantity, a.price, a.reminder_of_quantity, a.position_type, a.interval_start_id, a.interval_end_id,
            a.trade_date_id, a.price_curve,
            a.product_id, a.trader_id, a.delivery_day_id, a.calculation_date_id,
            mtm.mtm as mtm_previous_day, mtm.mtm as mtm_previous_year, a.buy_exists
	        from buys as a
        	left outer join trading_dw.mtm_trader as mtm on a.identification_description = mtm.identification_description
        												  and a.interval_start_id = mtm.trading_interval_start_id
        												  and a.trader_id = mtm.trader_id
        												  and mtm.calculation_date_id =
												                CASE
												                    WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
												                        (SELECT x.id
												                        FROM trading_dw.dim_date x
												                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
												                    ELSE
												                        (SELECT x.id
												                        FROM trading_dw.dim_date x
												                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
												                END
			union all
            select a.identification_description, a.transaction_date, a.delivery_date, a.interval_start, a.interval_end,
            a.quantity, a.price, a.reminder_of_quantity, a.position_type, a.interval_start_id, a.interval_end_id,
            a.trade_date_id, a.price_curve,
            a.product_id, a.trader_id, a.delivery_day_id, a.calculation_date_id,
            mtm.mtm as mtm_previous_day, mtm.mtm as mtm_previous_year, a.buy_exists
	        from unrealized_buys as a
        	left outer join trading_dw.mtm_trader as mtm on a.identification_description = mtm.identification_description
        												  and a.interval_start_id = mtm.trading_interval_start_id
        												  and a.trader_id = mtm.trader_id
        												  and mtm.calculation_date_id =
												                CASE
												                    WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
												                        (SELECT x.id
												                        FROM trading_dw.dim_date x
												                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
												                    ELSE
												                        (SELECT x.id
												                        FROM trading_dw.dim_date x
												                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
												                END
            order by 2, 1, 3, 4
        """
        return query

    @staticmethod
    def open_sells_trader():
        query = f"""
        with unrealized as
	        (
        	select a.identification_description_sell,
        		   a.trading_interval_start_id,
        		   a.product_id_sell as product_id,
        		   a.trader_id,
        		   min(a.quantity_reminder_sell) reminder
        	from trading_dw.realization_trader a
        		group by a.identification_description_sell,
        		   	     a.trading_interval_start_id,
        		   	     a.product_id_sell,
        		   	     a.trader_id
        		having min(a.quantity_reminder_sell) > 0
	        ),
        	sells as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, b.quantity as reminder_of_quantity, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id,
                a.trader_id,
                b.delivery_day_id,
                a.position_type,
                0 as sell_exists,
				CASE
				    WHEN p.commodity_id = 1 THEN CONCAT('HPFC', ' ', SUBSTRING(p.name FROM 1 FOR 2))
				    WHEN p.commodity_id = 2 THEN
				    	CASE
				            WHEN p.name LIKE 'TTF%' THEN 'GAS HPFC NL'
				            WHEN p.name LIKE 'NCG%' THEN 'GAS HPFC DE'
				            WHEN p.name LIKE 'GPL%' THEN 'GAS HPFC DE'
				            WHEN p.name LIKE 'NBP%' THEN 'GAS HPFC GB'
				            WHEN p.name LIKE 'VTP%' THEN 'GAS HPFC AT'
				            WHEN p.name LIKE 'USDMMBTU%' THEN 'GAS HPFC USDMMBTU NL'
			            ELSE NULL
			        END
				    WHEN p.commodity_id = 3 THEN 'CO2 PFC EU'
				END AS price_curve,
                CASE
                    WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
                        (SELECT x.id
                        FROM trading_dw.dim_date x
                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
                    ELSE
                        (SELECT x.id
                        FROM trading_dw.dim_date x
                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
                END AS calculation_date_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g,
                trading_dw.product p
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Sell'
            and a.trade_date_id = c.id
            and a.product_id = p.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '2 DAY')) - interval '1 hour'
            and not exists (select 1 from trading_dw.realization_trader x
            				where x.identification_description_sell = a.identification_description
            					and x.trading_interval_start_id = d.id
                                and x.product_id_sell = a.product_id
                                and x.trader_id = a.trader_id)
            ),
            unrealized_sells as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, x.reminder as reminder_of_quantity, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id,
                a.trader_id,
                b.delivery_day_id,
                a.position_type,
                1 as buy_exists,
				CASE
				    WHEN p.commodity_id = 1 THEN CONCAT('HPFC', ' ', SUBSTRING(p.name FROM 1 FOR 2))
				    WHEN p.commodity_id = 2 THEN
				    	CASE
				            WHEN p.name LIKE 'TTF%' THEN 'GAS HPFC NL'
				            WHEN p.name LIKE 'NCG%' THEN 'GAS HPFC DE'
				            WHEN p.name LIKE 'GPL%' THEN 'GAS HPFC DE'
				            WHEN p.name LIKE 'NBP%' THEN 'GAS HPFC GB'
				            WHEN p.name LIKE 'VTP%' THEN 'GAS HPFC AT'
				            WHEN p.name LIKE 'USDMMBTU%' THEN 'GAS HPFC USDMMBTU NL'
			            ELSE NULL
			        END
				    WHEN p.commodity_id = 3 THEN 'CO2 PFC EU'
				END AS price_curve,
                CASE
                    WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
                        (SELECT x.id
                        FROM trading_dw.dim_date x
                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
                    ELSE
                        (SELECT x.id
                        FROM trading_dw.dim_date x
                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
                END AS calculation_date_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g,
                trading_dw.product p,
                unrealized x
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Sell'
            and a.trade_date_id = c.id
            and a.product_id = p.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '2 DAY')) - interval '1 hour'
            and x.identification_description_sell = a.identification_description
            and x.trading_interval_start_id = d.id
            and x.trader_id = a.trader_id
            and a.product_id = x.product_id
            )
            select a.identification_description, a.transaction_date, a.delivery_date, a.interval_start, a.interval_end,
            a.quantity, a.price, a.reminder_of_quantity, a.position_type, a.interval_start_id, a.interval_end_id,
            a.trade_date_id, a.price_curve,
            a.product_id, a.trader_id, a.delivery_day_id, a.calculation_date_id,
            mtm.mtm as mtm_previous_day, mtm.mtm as mtm_previous_year, a.sell_exists
	        from sells as a
        	left outer join trading_dw.mtm_trader as mtm on a.identification_description = mtm.identification_description
        												  and a.interval_start_id = mtm.trading_interval_start_id
        												  and a.trader_id = mtm.trader_id
        												  and mtm.calculation_date_id =
												                CASE
												                    WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
												                        (SELECT x.id
												                        FROM trading_dw.dim_date x
												                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
												                    ELSE
												                        (SELECT x.id
												                        FROM trading_dw.dim_date x
												                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
												                END
			union all
            select a.identification_description, a.transaction_date, a.delivery_date, a.interval_start, a.interval_end,
            a.quantity, a.price, a.reminder_of_quantity, a.position_type, a.interval_start_id, a.interval_end_id,
            a.trade_date_id, a.price_curve,
            a.product_id, a.trader_id, a.delivery_day_id, a.calculation_date_id,
            mtm.mtm as mtm_previous_day, mtm.mtm as mtm_previous_year, a.buy_exists
	        from unrealized_sells as a
        	left outer join trading_dw.mtm_trader as mtm on a.identification_description = mtm.identification_description
        												  and a.interval_start_id = mtm.trading_interval_start_id
        												  and a.trader_id = mtm.trader_id
        												  and mtm.calculation_date_id =
												                CASE
												                    WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
												                        (SELECT x.id
												                        FROM trading_dw.dim_date x
												                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
												                    ELSE
												                        (SELECT x.id
												                        FROM trading_dw.dim_date x
												                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
												                END
            order by 2, 1, 3, 4
        """
        return query

    @staticmethod
    def open_buys_book():
        query = f"""
        with unrealized as
	        (
        	select a.identification_description_buy,
        		   a.trading_interval_start_id,
        		   a.product_id_buy as product_id,
        		   a.book_id,
        		   min(a.quantity_reminder_buy) reminder
        	from trading_dw.realization_book a
        		group by a.identification_description_buy,
        		   	     a.trading_interval_start_id,
        		   	     a.product_id_buy, book_id
        		having min(a.quantity_reminder_buy) > 0
	        ),
        	buys as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, b.quantity as reminder_of_quantity, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id,
                a.trader_id,
                a.trade_book_id as book_id,
                b.delivery_day_id,
                a.position_type,
                0 as buy_exists,
				CASE
				    WHEN p.commodity_id = 1 THEN CONCAT('HPFC', ' ', SUBSTRING(p.name FROM 1 FOR 2))
				    WHEN p.commodity_id = 2 THEN
				    	CASE
				            WHEN p.name LIKE 'TTF%' THEN 'GAS HPFC NL'
				            WHEN p.name LIKE 'NCG%' THEN 'GAS HPFC DE'
				            WHEN p.name LIKE 'GPL%' THEN 'GAS HPFC DE'
				            WHEN p.name LIKE 'NBP%' THEN 'GAS HPFC GB'
				            WHEN p.name LIKE 'VTP%' THEN 'GAS HPFC AT'
				            WHEN p.name LIKE 'USDMMBTU%' THEN 'GAS HPFC USDMMBTU NL'
			            ELSE NULL
			        END
				    WHEN p.commodity_id = 3 THEN 'CO2 PFC EU'
				END AS price_curve,
                CASE
                    WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
                        (SELECT x.id
                        FROM trading_dw.dim_date x
                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
                    ELSE
                        (SELECT x.id
                        FROM trading_dw.dim_date x
                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
                END AS calculation_date_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g,
                trading_dw.product p
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Buy'
            and a.trade_date_id = c.id
            and a.product_id = p.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '2 DAY')) - interval '1 hour'
            and not exists (select 1 from trading_dw.realization_book x
            				where x.identification_description_buy = a.identification_description
            					and x.trading_interval_start_id = d.id
                                and x.product_id_buy = a.product_id
                                and x.book_id = a.trade_book_id)
            ),
            unrealized_buys as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, x.reminder as reminder_of_quantity, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id,
                a.trader_id,
                a.trade_book_id as book_id,
                b.delivery_day_id,
                a.position_type,
                1 as buy_exists,
				CASE
				    WHEN p.commodity_id = 1 THEN CONCAT('HPFC', ' ', SUBSTRING(p.name FROM 1 FOR 2))
				    WHEN p.commodity_id = 2 THEN
				    	CASE
				            WHEN p.name LIKE 'TTF%' THEN 'GAS HPFC NL'
				            WHEN p.name LIKE 'NCG%' THEN 'GAS HPFC DE'
				            WHEN p.name LIKE 'GPL%' THEN 'GAS HPFC DE'
				            WHEN p.name LIKE 'NBP%' THEN 'GAS HPFC GB'
				            WHEN p.name LIKE 'VTP%' THEN 'GAS HPFC AT'
				            WHEN p.name LIKE 'USDMMBTU%' THEN 'GAS HPFC USDMMBTU NL'
			            ELSE NULL
			        END
				    WHEN p.commodity_id = 3 THEN 'CO2 PFC EU'
				END AS price_curve,
                CASE
                    WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
                        (SELECT x.id
                        FROM trading_dw.dim_date x
                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
                    ELSE
                        (SELECT x.id
                        FROM trading_dw.dim_date x
                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
                END AS calculation_date_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g,
                trading_dw.product p,
                unrealized x
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Buy'
            and a.trade_date_id = c.id
            and a.product_id = p.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '2 DAY')) - interval '1 hour'
            and x.identification_description_buy = a.identification_description
            and x.trading_interval_start_id = d.id
            and a.product_id = x.product_id
            and x.book_id = a.trade_book_id
            )
            select a.identification_description, a.transaction_date, a.delivery_date, a.interval_start, a.interval_end,
            a.quantity, a.price, a.reminder_of_quantity, a.position_type, a.interval_start_id, a.interval_end_id,
            a.trade_date_id, a.price_curve,
            a.product_id, a.trader_id, a.book_id, a.delivery_day_id, a.calculation_date_id,
            mtm.mtm as mtm_previous_day,mtm.mtm as mtm_previous_year, a.buy_exists
	        from buys as a
        	left outer join trading_dw.mtm_book as mtm on a.identification_description = mtm.identification_description
        												  and a.interval_start_id = mtm.trading_interval_start_id
        												  and a.book_id = mtm.book_id
        												  and mtm.calculation_date_id =
												                CASE
												                    WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
												                        (SELECT x.id
												                        FROM trading_dw.dim_date x
												                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
												                    ELSE
												                        (SELECT x.id
												                        FROM trading_dw.dim_date x
												                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
												                END
			union all
            select a.identification_description, a.transaction_date, a.delivery_date, a.interval_start, a.interval_end,
            a.quantity, a.price, a.reminder_of_quantity, a.position_type, a.interval_start_id, a.interval_end_id,
            a.trade_date_id, a.price_curve,
            a.product_id, a.trader_id, a.book_id, a.delivery_day_id, a.calculation_date_id,
            mtm.mtm as mtm_previous_day, mtm.mtm as mtm_previous_year, a.buy_exists
	        from unrealized_buys as a
        	left outer join trading_dw.mtm_book as mtm on a.identification_description = mtm.identification_description
        												  and a.interval_start_id = mtm.trading_interval_start_id
        												  and a.book_id = mtm.book_id
        												  and mtm.calculation_date_id =
												                CASE
												                    WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
												                        (SELECT x.id
												                        FROM trading_dw.dim_date x
												                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
												                    ELSE
												                        (SELECT x.id
												                        FROM trading_dw.dim_date x
												                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
												                END
            order by 2, 1, 3, 4
        """
        return query

    @staticmethod
    def open_sells_book():
        query = f"""
        with unrealized as
	        (
        	select a.identification_description_sell,
        		   a.trading_interval_start_id,
        		   a.product_id_sell as product_id,
        		   a.book_id,
        		   min(a.quantity_reminder_sell) reminder
        	from trading_dw.realization_book a
        		group by a.identification_description_sell,
        		   	     a.trading_interval_start_id,
        		   	     a.product_id_sell,
        		   	     a.book_id
        		having min(a.quantity_reminder_sell) > 0
	        ),
        	sells as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, b.quantity as reminder_of_quantity, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id,
                a.trader_id,
                a.trade_book_id as book_id,
                b.delivery_day_id,
                a.position_type,
                0 as sell_exists,
				CASE
				    WHEN p.commodity_id = 1 THEN CONCAT('HPFC', ' ', SUBSTRING(p.name FROM 1 FOR 2))
				    WHEN p.commodity_id = 2 THEN
				    	CASE
				            WHEN p.name LIKE 'TTF%' THEN 'GAS HPFC NL'
				            WHEN p.name LIKE 'NCG%' THEN 'GAS HPFC DE'
				            WHEN p.name LIKE 'GPL%' THEN 'GAS HPFC DE'
				            WHEN p.name LIKE 'NBP%' THEN 'GAS HPFC GB'
				            WHEN p.name LIKE 'VTP%' THEN 'GAS HPFC AT'
				            WHEN p.name LIKE 'USDMMBTU%' THEN 'GAS HPFC USDMMBTU NL'
			            ELSE NULL
			        END
				    WHEN p.commodity_id = 3 THEN 'CO2 PFC EU'
				END AS price_curve,
                CASE
                    WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
                        (SELECT x.id
                        FROM trading_dw.dim_date x
                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
                    ELSE
                        (SELECT x.id
                        FROM trading_dw.dim_date x
                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
                END AS calculation_date_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g,
                trading_dw.product p
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Sell'
            and a.trade_date_id = c.id
            and a.product_id = p.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '2 DAY')) - interval '1 hour'
            and not exists (select 1 from trading_dw.realization_book x
            				where x.identification_description_sell = a.identification_description
            					and x.trading_interval_start_id = d.id
                                and x.product_id_sell = a.product_id
                                and x.book_id = a.trade_book_id)
            ),
            unrealized_sells as
            (
            select a.identification_description, a.transaction_time as transaction_date,
                f.date as delivery_date, d.date_hour_minute_start as interval_start,
                g.date_hour_minute_start as interval_end, b.quantity, x.reminder as reminder_of_quantity, b.price,
                d.id AS interval_start_id,
                g.id AS interval_end_id,
                c.id AS trade_date_id,
                a.product_id,
                a.trader_id,
                a.trade_book_id as book_id,
                b.delivery_day_id,
                a.position_type,
                1 as buy_exists,
				CASE
				    WHEN p.commodity_id = 1 THEN CONCAT('HPFC', ' ', SUBSTRING(p.name FROM 1 FOR 2))
				    WHEN p.commodity_id = 2 THEN
				    	CASE
				            WHEN p.name LIKE 'TTF%' THEN 'GAS HPFC NL'
				            WHEN p.name LIKE 'NCG%' THEN 'GAS HPFC DE'
				            WHEN p.name LIKE 'GPL%' THEN 'GAS HPFC DE'
				            WHEN p.name LIKE 'NBP%' THEN 'GAS HPFC GB'
				            WHEN p.name LIKE 'VTP%' THEN 'GAS HPFC AT'
				            WHEN p.name LIKE 'USDMMBTU%' THEN 'GAS HPFC USDMMBTU NL'
			            ELSE NULL
			        END
				    WHEN p.commodity_id = 3 THEN 'CO2 PFC EU'
				END AS price_curve,
                CASE
                    WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
                        (SELECT x.id
                        FROM trading_dw.dim_date x
                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
                    ELSE
                        (SELECT x.id
                        FROM trading_dw.dim_date x
                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
                END AS calculation_date_id
                from trading_dw.trade a,
                trading_dw.trade_prices b,
                trading_dw.dim_date c,
                trading_dw.dim_time d,
                trading_dw.trading_interval e,
                trading_dw.dim_date f,
                trading_dw.dim_time g,
                trading_dw.product p,
                unrealized x
            where a.identification_description = b.identification_description
            and a.trade_type = 'Financial'
            and a.position_type = 'Sell'
            and a.trade_date_id = c.id
            and a.product_id = p.id
            and b.delivery_day_id = f.id
            and b.trading_interval_id = e.id
            and e.start_time_id = d.id
            and e.end_time_id = g.id
            --and d.date_hour_minute_start >= date_trunc('day', (now() AT TIME ZONE 'UTC' - interval '2 DAY')) - interval '1 hour'
            and x.identification_description_sell = a.identification_description
            and x.trading_interval_start_id = d.id
            and x.book_id = a.trade_book_id
            and a.product_id = x.product_id
            )
            select a.identification_description, a.transaction_date, a.delivery_date, a.interval_start, a.interval_end,
            a.quantity, a.price, a.reminder_of_quantity, a.position_type, a.interval_start_id, a.interval_end_id,
            a.trade_date_id, a.price_curve,
            a.product_id, a.book_id, a.trader_id, a.delivery_day_id, a.calculation_date_id,
            mtm.mtm as mtm_previous_day, mtm.mtm as mtm_previous_year, a.sell_exists
	        from sells as a
        	left outer join trading_dw.mtm_book as mtm on a.identification_description = mtm.identification_description
        												  and a.interval_start_id = mtm.trading_interval_start_id
        												  and a.book_id = mtm.book_id
        												  and mtm.calculation_date_id =
												                CASE
												                    WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
												                        (SELECT x.id
												                        FROM trading_dw.dim_date x
												                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
												                    ELSE
												                        (SELECT x.id
												                        FROM trading_dw.dim_date x
												                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
												                END
			union all
            select a.identification_description, a.transaction_date, a.delivery_date, a.interval_start, a.interval_end,
            a.quantity, a.price, a.reminder_of_quantity, a.position_type, a.interval_start_id, a.interval_end_id,
            a.trade_date_id, a.price_curve,
            a.product_id, a.book_id, a.trader_id, a.delivery_day_id, a.calculation_date_id,
            mtm.mtm as mtm_previous_day, mtm.mtm as mtm_previous_year, a.buy_exists
	        from unrealized_sells as a
        	left outer join trading_dw.mtm_book as mtm on a.identification_description = mtm.identification_description
        												  and a.interval_start_id = mtm.trading_interval_start_id
        												  and a.book_id = mtm.book_id
        												  and mtm.calculation_date_id =
												                CASE
												                    WHEN EXTRACT(DOW FROM NOW() AT TIME ZONE 'UTC') = 1 THEN
												                        (SELECT x.id
												                        FROM trading_dw.dim_date x
												                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '3 days') AT TIME ZONE 'UTC')
												                    ELSE
												                        (SELECT x.id
												                        FROM trading_dw.dim_date x
												                        WHERE x.date = DATE_TRUNC('day', NOW() - INTERVAL '1 day') AT TIME ZONE 'UTC')
												                END
            order by 2, 1, 3, 4
        """
        return query