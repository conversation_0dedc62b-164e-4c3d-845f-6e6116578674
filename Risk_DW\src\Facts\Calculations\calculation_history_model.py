from sqlalchemy import Column, Inte<PERSON>, Numeric, Foreign<PERSON>ey
from sqlalchemy.orm import relationship
from setup_global import SetupGlobal
from libs.mixin import AuditMixin
from Dimensions.CodeValue.code_value_model import CodeValueModel


class CalculationHistoryModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'calculation_history'

    id = Column(Integer, primary_key=True, autoincrement=True)
    calculation_date_id = Column(Integer, ForeignKey('dim_date.id'), nullable=False)
    calculation_type_id = Column(Integer, ForeignKey('code_value.id'), nullable=False)
    calculation_code_id = Column(Integer, ForeignKey('code_value.id'), nullable=False)
    object_id = Column(Integer, nullable=False)
    value = Column(Numeric(18, 6), nullable=False)

    # Relationships
    #calculation_date = relationship('DimDateModel', back_populates='calculation_histories')
    calculation_type = relationship('CodeValueModel', foreign_keys=[calculation_type_id])
    calculation_code = relationship('CodeValueModel', foreign_keys=[calculation_code_id])

    def __init__(self, calculation_date_id, calculation_type_id, calculation_code_id, value):
        self.calculation_date_id = calculation_date_id
        self.calculation_type_id = calculation_type_id
        self.calculation_code_id = calculation_code_id
        self.object_id = object_id
        self.value = value
