import logging
import sys
import os
import time
from os import getenv
from ksLib.environment import Environment
try:
    from app.app_factory import Container
    from app.utils.logging_config import setup_logging
    from app.utils.config_loader import load_config
except ImportError as e:
    print(f"Failed to import modules: {e}")
    sys.exit(1)

logger = setup_logging()
container = Container()
config_data = load_config()
container.config.from_dict(config_data)


def import_dimensions():
    dimensions_manager = container.dimensions_manager()
    st = time.time()
    #dimensions_manager.import_initial_dimensions()
    dimensions_manager.import_dimensions()
    log_time(st, 'Dimensions import')


def log_time(st, area):
    et = time.time()
    elapsed_time = et - st
    logger.info('%s took %s seconds', area, round(elapsed_time))


if __name__ == "__main__" or getenv("RUN_MAIN"):
    environment = (getenv("ENVIRONMENT") or 'PROD').upper()
    logger.info(f"DW v1.0.0 ({environment}) started")
    import_dimensions()
    logger.info("Dimension import completed.")














