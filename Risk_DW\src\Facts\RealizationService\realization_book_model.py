from sqlalchemy import Column, Integer, String, Numeric, Boolean
from setup_global import SetupGlobal
from libs.mixin import AuditMixin

class RealizationBookModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'realization_book'
    book_id = Column(Integer, primary_key=True)
    identification_description_buy = Column(String(13), primary_key=True)
    identification_description_sell = Column(String(13), primary_key=True)
    trading_interval_start_id = Column(Integer, primary_key=True)
    trading_interval_end_id_buy = Column(Integer)
    trading_interval_end_id_sell = Column(Integer)
    price_buy = Column(Numeric(18, 6))
    price_sell = Column(Numeric(18, 6))
    quantity_buy = Column(Numeric(18, 6))
    quantity_sell = Column(Numeric(18, 6))
    quantity_reminder_buy = Column(Numeric(18, 6))
    quantity_reminder_sell = Column(Numeric(18, 6))
    trade_date_id_buy = Column(Integer)
    trade_date_id_sell = Column(Integer)
    product_id_buy = Column(Integer)
    product_id_sell = Column(Integer)
    realization = Column(Numeric(18, 6))
    buy_closed = Column(Boolean)
    sell_closed = Column(Boolean)

    def __init__(self,
                 book_id,
                 identification_description_buy,
                 identification_description_sell,
                 trading_interval_start_id,
                 trading_interval_end_id_buy,
                 trading_interval_end_id_sell,
                 price_buy,
                 price_sell,
                 quantity_buy,
                 quantity_sell,
                 quantity_reminder_buy,
                 quantity_reminder_sell,
                 trade_date_id_buy,
                 trade_date_id_sell,
                 realization,
                 product_id_buy,
                 product_id_sell,
                 buy_closed,
                 sell_closed):
        self.book_id = book_id
        self.identification_description_buy = identification_description_buy
        self.identification_description_sell = identification_description_sell
        self.trading_interval_start_id = trading_interval_start_id
        self.trading_interval_end_id_buy = trading_interval_end_id_buy
        self.trading_interval_end_id_sell = trading_interval_end_id_sell
        self.price_buy = price_buy
        self.price_sell = price_sell
        self.quantity_buy = quantity_buy
        self.quantity_sell = quantity_sell
        self.quantity_reminder_buy = quantity_reminder_buy
        self.quantity_reminder_sell = quantity_reminder_sell
        self.trade_date_id_buy = trade_date_id_buy
        self.trade_date_id_sell = trade_date_id_sell
        self.realization = realization
        self.product_id_buy = product_id_buy
        self.product_id_sell = product_id_sell
        self.buy_closed = buy_closed
        self.sell_closed = sell_closed
