from Dimensions.BalancingGroup.balancing_group_model import BalancingGroupModel
from Dimensions.ControlArea.control_area_model import ControlAreaModel
from libs.setup_orm_utility import SetupOrmUtility
from pyexcel_io.database.querysets import QuerysetsReader

import logging

logger = logging.getLogger(__name__)

class BalancingGroupImporter():

    def __init__(self, config_name):
        self.setup_orm_utility = SetupOrmUtility(config_name)


        query_sets = self.setup_orm_utility.codes_session.query(BalancingGroupModel).all()
        reader = QuerysetsReader(query_sets, ["balancing_group_name"])
        self.existing_balancing_groups = list(reader.to_array())

    def import_data(self):
        self.rows_inserted = 0

        logger.info('Import started')

        self.setup_orm_utility.import_data(self.balancing_group_init_func, 'balancing_group',BalancingGroupModel)

        logger.info('Import ended. Inserted %s rows', self.rows_inserted)

    def balancing_group_init_func(self,row):
            if self.setup_orm_utility.key_exists (self.existing_balancing_groups, row['balancing_group_name']):
                return None

            control_area = self.setup_orm_utility.codes_session.query(ControlAreaModel).filter_by(zone_id=row['control_area_id']).first()
            if control_area is None:
                logger.error('Missing control_area code "%s"', row['control_area_id'])
                return None

            self.rows_inserted += 1

            return BalancingGroupModel(
                row['balancing_group_name'],
                row['balancing_group_eic'],
                row['balancing_subgroup_name'],
                row['balancing_subgroup_eic'],
                control_area.id)