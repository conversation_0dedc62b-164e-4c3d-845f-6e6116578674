import threading
from Dimensions.ControlArea.control_area_importer import ControlAreaImporter
from Dimensions.BalancingGroup.balancing_group_importer import BalancingGroupImporter
from Dimensions.ExchangeRate.exchange_rate_importer import ExchangeRateImporter
from Dimensions.Country.country_importer import CountryImporter
from Dimensions.Currency.currency_importer import CurrencyImporter
from Dimensions.Partner.partner_importer import PartnerImporter
from Dimensions.Holiday.holiday_importer import HolidayImporter
from Dimensions.DimDate.dim_date_importer import DimDateImporter
from Dimensions.DimTime.dim_time_importer import DimTimeImporter
from Dimensions.LimitType.limit_type_importer import LimitTypeImporter
from Dimensions.LimitGroup.limit_group_importer import LimitGroupImporter
from Dimensions.Trader.trader_importer import TraderImporter
from Dimensions.Book.book_importer import BookImporter
from Dimensions.LoadProfile.load_profile_importer import LoadProfileImporter
from Dimensions.ProductType.product_type_importer import ProductTypeImporter
from Dimensions.TradingPeriod.trading_period_importer import TradingPeriodImporter
from Dimensions.MarketType.market_type_importer import MarketTypeImporter
from Dimensions.Product.product_importer import ProductImporter
from Dimensions.Commodity.commodity_importer import CommodityImporter
from Dimensions.Prices.prices_importer import PricesImporter
from Dimensions.PriceCurve.price_curve_importer import PriceCurveImporter
from Dimensions.Contract.contract_importer import ContractImporter
from Dimensions.Portfolio.portfolio_importer import PortfolioImporter
from Dimensions.PortfolioBook.portfolio_book_importer import PortfolioBookImporter
from Dimensions.Limit.limit_importer import LimitImporter
from Config.ConfigReader import ConfigReader

from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class DimensionsManager:

    def __init__(self, config_name, database):
        self.config_name = config_name
        self.config = ConfigReader.get_config_section(self.config_name, "system")
        self.dim_config = ConfigReader.get_config_section(self.config_name, "dimensions")
        self.database = database

    def import_initial_dimensions(self):
        logger.info('Importing initial dimensions')

        country_importer = CountryImporter(self.config_name)
        country_importer.import_data()

        currency_importer = CurrencyImporter(self.config_name)
        currency_importer.import_data()

        partner_importer = PartnerImporter(self.config_name)
        partner_importer.import_data()

        holiday_importer = HolidayImporter(self.config_name)
        holiday_importer.import_data()

    def import_one_time_date_dimensions(self, start_date, end_date):
        dim_time_importer = DimTimeImporter(self.config_name)
        dim_date_importer = DimDateImporter(self.config_name)

        thread_dim_time = threading.Thread(target=dim_time_importer.import_data, args=(start_date, end_date))
        thread_dim_time.start()

        thread_dim_date = threading.Thread(target=dim_date_importer.import_data, args=(start_date, end_date))
        thread_dim_date.start()

        thread_dim_date.join()
        thread_dim_time.join()

    def import_exchange_rate(self, start_date):
        logger.info('Importing exchange rate')
        exchange_rate_importer = ExchangeRateImporter(self.config_name)
        exchange_rate_importer.import_data(start_date)

    def import_prices(self, ):
        prices_importer = PricesImporter(self.config_name, self.database)
        prices_importer.import_data()

    def import_live_prices(self, prices, price_curve):
        prices_importer = PricesImporter(self.config_name, self.database)
        prices_importer.import_live_prices(prices, price_curve)

    def get_live_prices(self, start_date, end_date, price_curve,resolution, inclusive_end_date):
        prices_importer = PricesImporter(self.config_name, self.database)
        return prices_importer._get_pfc_prices(start_date=start_date, end_date=end_date, price_curve=price_curve, resolution=resolution, inclusive_end_date=inclusive_end_date)


    def update_prices(self):
        prices_importer = PricesImporter(self.config_name, self.database)
        prices_importer.update_data()

    def import_dimensions(self):
        logger.info('Importing dimensions')

        #contract_importer = ContractImporter(self.config_name)
        #contract_importer.import_data()

        price_curve_importer = PriceCurveImporter(self.config_name)
        price_curve_importer.import_data()

        market_type_importer = MarketTypeImporter(self.config_name)
        market_type_importer.import_data()

        trading_period_importer = TradingPeriodImporter(self.config_name)
        trading_period_importer.import_data()

        product_type_importer = ProductTypeImporter(self.config_name)
        product_type_importer.import_data()

        load_profile_importer = LoadProfileImporter(self.config_name)
        load_profile_importer.import_data()

        limit_type_importer = LimitTypeImporter(self.config_name)
        limit_type_importer.import_data()

        limit_group_importer = LimitGroupImporter(self.config_name)
        limit_group_importer.import_data()

        trader_importer = TraderImporter(self.config_name)
        trader_importer.import_data()

        book_importer = BookImporter(self.config_name)
        book_importer.import_data()

        portfolio_importer = PortfolioImporter(self.config_name)
        portfolio_importer.import_data()

        commodity_importer = CommodityImporter(self.config_name)
        commodity_importer.import_data()

        control_area_importer = ControlAreaImporter(self.config_name)
        control_area_importer.import_data()

        balancing_group_importer = BalancingGroupImporter(self.config_name)
        balancing_group_importer.import_data()

        # TO DO vezan na porfolio
        #limit_importer = LimitImporter(self.config_name)
        #limit_importer.import_data()

        product_importer = ProductImporter(self.config_name)
        product_importer.import_data()

        portfolio_book_importer = PortfolioBookImporter(self.config_name)
        portfolio_book_importer.import_data()

    def merge_settlement_prices(self):
        prices_merger = PricesImporter(self.config_name, self.database)
        prices_merger.merge_settlement_prices()
