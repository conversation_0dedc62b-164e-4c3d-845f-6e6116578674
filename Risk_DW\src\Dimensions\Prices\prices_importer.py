import re
from libs.setup_orm_utility import SetupOrmUtility
from Dimensions.Prices.prices_model import PricesModel
from Dimensions.DimDate.dim_date_model import DimDateModel
from Dimensions.DimTime.dim_time_model import DimTimeModel
from Dimensions.PriceCurve.price_curve_model import PriceCurveModel
from datetime import timedelta, datetime, timezone
from Facts.Utils.timeseries import Timeseries
from Facts.TradePrices.trade_prices_model import TradePricesModel
from sqlalchemy import select, and_, join
from sqlalchemy import text, update
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy import exists, and_, text, func
from sqlalchemy.orm import Session
from Facts.Utils.utils import deprecated
from dateutil.relativedelta import relativedelta
import pandas as pd

import logging
import pytz

from Facts.TradingInterval.trading_interval import TradingIntervalModel

logger = logging.getLogger(__name__)


class PricesImporter:

    def __init__(self, config_name, database):
        self.setup_orm_utility = SetupOrmUtility(config_name)

        self.config_name = config_name
        self.database = database

    def update_data(self):

        price_updates = []

        start_time = self.setup_orm_utility.codes_session.connection().execute(select(DimTimeModel).where(
            DimTimeModel.date >= (datetime.today() - timedelta(days=4))
        ).fetch(count=1)).one()

        max_date_query = (
            self.setup_orm_utility.codes_session.query(func.max(DimTimeModel.date_hour_minute_end).label('date'))
            .join(PricesModel, DimTimeModel.id == PricesModel.time_id)
        )
        end_time = max_date_query.scalar()

        if end_time:
            price_curves = self.setup_orm_utility.codes_session.query(PriceCurveModel).all()
            for price_curve in price_curves:

                resolution = self._get_price_resolution(price_curve.price_curve_name)
                if resolution is not None:

                    prices = self._get_prices(
                        price_curve.id,
                        start_time.date.strftime('%Y-%m-%d'),
                        end_time.strftime('%Y-%m-%d')
                    )

                    if prices:
                        pfc_prices = self._get_pfc_prices(
                            start_time.date_hour_minute_start,
                            end_time,
                            price_curve,
                            resolution,
                            inclusive_end_date=False,
                            update=True
                        )

                        logger.info("Start updating '%s' prices", price_curve.price_curve_name)

                        # Index PFC prices by timestamp for faster lookup
                        pfc_price_map = {
                            p[0].replace(second=0, minute=0): p[2] for p in pfc_prices[1:]
                        }

                        for price in prices:
                            price_time = price.date_hour_minute_start.replace(second=0, minute=0)

                            if price_time not in pfc_price_map:
                                logger.warning(f"{price_curve.price_curve_name} PFC price for '%s' not found...", price_time)
                                continue

                            # Compare prices
                            pfc_price_value = float(pfc_price_map[price_time])
                            current_price_value = float(price.price)

                            if current_price_value != pfc_price_value:
                                #logger.info('Updating PFC price %s -> %s', current_price_value, pfc_price_value)
                                price_updates.append(
                                    {
                                        "id": price.id,
                                        "price": pfc_price_value,
                                    }
                                )

            if len(price_updates) > 0:
                with Session(self.database.engine) as session:
                    logger.info('Updating prices started...')
                    #session.execute(update(PricesModel), price_updates)
                    session.bulk_update_mappings(PricesModel, price_updates)
                    logger.info('Commiting changes to DW...')
                    session.commit()
            else:
                logger.info('No pfc price to update...')
        else:
            logger.info('No pfc price to update...')

    def import_data(self):
        logger.info('Import started')

        price_curves = (
            self.setup_orm_utility.codes_session
            .query(PriceCurveModel)
            .filter(and_(PriceCurveModel.price_curve_name.like('%PFC%')))
        )

        for price_curve in price_curves:
            resolution = self._get_price_resolution(price_curve.price_curve_name)
            if resolution is not None:

                logger.info("Start importing '%s' prices", price_curve.price_curve_name)

                self.rows_inserted = 0

                start_date = self._get_max_pfc_date(price_curve.price_curve_name)
                if start_date is None:
                    start_date = datetime(2025, 1, 16, tzinfo=pytz.UTC)

                end_date = start_date + relativedelta(years=5)

                #if price_curve.price_curve_name == 'HPFC CZ':
                #    start_date = datetime(2025, 1, 15, 2, 0, 0, tzinfo=pytz.UTC)
                #    end_date = datetime(2027, 1, 16, 0, 0, 0, tzinfo=pytz.UTC)


                prices = self._get_pfc_prices(start_date, end_date, price_curve, resolution, inclusive_end_date=True)

                if len(prices) > 1:
                    self.price_dates = self.setup_orm_utility.codes_session.query(DimDateModel).filter(
                        and_(DimDateModel.date >= prices[1][0].date(),
                             DimDateModel.date <= prices[len(prices) - 1][0].date())
                    ).all()

                    self.price_times = self.setup_orm_utility.codes_session.query(DimTimeModel).filter(
                        and_(DimTimeModel.date_hour_minute_start >= prices[1][0],
                             DimTimeModel.date_hour_minute_start <= prices[len(prices) - 1][0])
                    ).all()

                    self.setup_orm_utility.import_data(self.prices_init_func, 'prices', PricesModel, {'prices': prices})

                logger.info('Imported %s "%s" prices', self.rows_inserted, price_curve.price_curve_name)

    def prices_init_func(self, row):

        start_price_date = next((x for x in self.price_dates if x.date == row['date'].date()), None)
        if start_price_date is None:
            logger.error('Missing start_trade_date "%s"', row['date'])
            return None

        start_price_time = next(
            (x for x in self.price_times if x.date_hour_minute_start == row['date']), None)
        if start_price_time is None:
            logger.error('Missing start_trade_time "%s"', row['date'])
            return None

        self.rows_inserted += 1

        if self.rows_inserted % 100 == 0:
            logger.info('Imported %s prices', self.rows_inserted)

        return PricesModel(
            start_price_date.id,
            start_price_time.id,
            row['price_curve_id'],
            row['price']
        )

    def _get_prices(self, price_curve_id, start_date, end_date):
        get_prices_query = f"""
                    SELECT
                        p.id ,p.price_curve_id , p.price , dt.date_hour_minute_start
                    FROM
                        trading_dw.prices p
                    INNER JOIN
                        trading_dw.dim_time dt ON p.time_id = dt.id
                    WHERE
                        dt.date >='{start_date}' AND dt.date <= '{end_date}' AND price_curve_id={price_curve_id}
                """

        with self.database.engine.connect() as conn:
            #conn.execute(text(get_prices_query)).all()
            return conn.execute(text(get_prices_query)).all()

    def _get_pfc_prices(self, start_date, end_date, price_curve, resolution, inclusive_end_date, update: bool = False):
        timeseries = Timeseries(self.config_name)
        cet_tz = pytz.timezone('Europe/Ljubljana')
        utc_tz = pytz.UTC
        result = [
            [
                'date',
                'price_curve_id',
                'price'
            ]
        ]

        country = price_curve.price_curve_name.split()[-1]
        type = 'Power'
        if 'GAS' in price_curve.price_curve_name:
            type = "Natural Gas"
        if 'CO2' in price_curve.price_curve_name:
            type = "CO2"

        if 'USDMMBTU' in price_curve.price_curve_name:
            cond = 'USDMMBTU'
        else:
            cond = "and 1 = 1"

        logger.info("Start fetching pfc prices for interval %s - %s from timeseries database", start_date, end_date)

        hpfc_prices = timeseries.get_hpfc_price_by_interval(start_date, end_date, country, self.database,
                                                            inclusive_end_date, update, type, cond)
        if len(hpfc_prices) == 0:
            return []

        logger.info("Start processing pfc prices for interval %s - %s", start_date, end_date)
        if 'GAS' in price_curve.price_curve_name:
            hpfc_prices = self.resample_timeranges(hpfc_prices, 'D', 24)

        if not isinstance(start_date, datetime):
            start_date = datetime.fromisoformat(str(start_date))
        if not isinstance(end_date, datetime):
            end_date = datetime.fromisoformat(str(end_date))
        if start_date.tzinfo is None:
            start_date = start_date.replace(tzinfo=utc_tz)
        if end_date.tzinfo is None:
            end_date = end_date.replace(tzinfo=utc_tz)

        current_date = start_date

        processed_filters = set()

        while current_date <= end_date:
            cet_date = current_date.astimezone(cet_tz)

            if resolution == timedelta(days=1):
                normalized_date = cet_date.replace(hour=0, minute=0, second=0, microsecond=0)
            elif resolution == timedelta(hours=1):
                normalized_date = cet_date.replace(minute=0, second=0, microsecond=0)
            elif resolution == relativedelta(years=1):
                normalized_date = cet_date.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
            else:
                logger.warning("Unhandled resolution %s, defaulting to second-level normalization", resolution)
                normalized_date = cet_date.replace(second=0, microsecond=0)

            utc_normalized = normalized_date.astimezone(utc_tz)
            date_time_filter = utc_normalized.strftime('%Y-%m-%d %H:%M:%S+00:00')

            if date_time_filter not in processed_filters:
                hpfc_prices_list = hpfc_prices.loc[(hpfc_prices['date_time_value'] == date_time_filter)]

                if len(hpfc_prices_list) > 0:
                    result.append(
                        [
                            hpfc_prices_list.values[0][0],
                            price_curve.id,
                            hpfc_prices_list.values[0][3]
                        ]
                    )
                    processed_filters.add(date_time_filter)
                else:
                    logger.debug('PFC price for "%s" doesn\'t exist', date_time_filter)

            if resolution == timedelta(days=1):
                cet_next_date = cet_date + timedelta(days=1)
                current_date = cet_next_date.astimezone(utc_tz)
            elif resolution == timedelta(hours=1):
                current_date += resolution
            elif resolution == relativedelta(years=1):
                current_date += resolution
            else:
                logger.warning("Unhandled resolution %s, using direct increment", resolution)
                current_date += resolution

        return result

    def _get_price_resolution(self, price_curve_name):
        if not 'PFC' in price_curve_name:
            return None

        if 'CO2' in price_curve_name:
            return relativedelta(years=1)

        if 'GAS' in price_curve_name:
            return timedelta(days=1)

        return timedelta(hours=1)


    def import_live_prices(self, prices, price_curve):
        logger.info('Import started')

        self.rows_inserted = 0
        if len(prices) > 1:
            self.price_dates = self.setup_orm_utility.codes_session.query(DimDateModel).filter(
                and_(DimDateModel.date >= prices[1][0].date(),
                        DimDateModel.date <= prices[len(prices) - 1][0].date())
            ).all()

            self.price_times = self.setup_orm_utility.codes_session.query(DimTimeModel).filter(
                and_(DimTimeModel.date_hour_minute_start >= prices[1][0],
                        DimTimeModel.date_hour_minute_start <= prices[len(prices) - 1][0])
            ).all()

            self.setup_orm_utility.import_data(self.prices_init_func, 'prices', PricesModel, {'prices': prices})

        logger.info('Imported %s "%s" prices', self.rows_inserted, price_curve.price_curve_name)


    def merge_settlement_prices(self):
        with Session(self.database.engine) as session:
            self.sett_prices = session.query(PriceCurveModel).filter_by(price_curve_type = "Index_Settlement").all()
        country_codes = {
            re.search(r"EEX (\w{2})", price.price_curve_name).group(1)
            for price in self.sett_prices
            if re.search(r"EEX (\w{2})", price.price_curve_name)
        }
        price_entries = []
        i = 0
        for country in country_codes:
            prices_data = self._get_sett_prices(country)
            if i == 0:
                prices_data = prices_data + self._get_sett_coupon_prices()
                i = 1

            for price_record in prices_data:

                price_date = price_record.date_time_value.date()
                price_time = price_record.date_time_value
                price_value = price_record.numeric_value

                if not (price_date and price_time and price_value):
                    continue

                date_id = (
                    self.setup_orm_utility.codes_session.query(DimDateModel.id)
                    .filter(DimDateModel.date == price_date)
                    .scalar()
                )
                time_id = (
                    self.setup_orm_utility.codes_session.query(DimTimeModel.id)
                    .filter(DimTimeModel.date_hour_minute_start == price_time)
                    .scalar()
                )

                if date_id is None or time_id is None:
                    continue

                if 'EEX EU EUA' in price_record.json_value:
                    matching_price_curve = next(
                        (price for price in self.sett_prices if price.price_curve_name == price_record.json_value),
                        None
                    )
                else:
                    matching_price_curve = next(
                        (price for price in self.sett_prices if price.price_curve_name == 'EEX ' + country + ' ' + price_record.json_value),
                        None
                    )
                if not matching_price_curve:
                    continue

                price_entry = {
                    "date_id": date_id,
                    "time_id": time_id,
                    "price_curve_id": matching_price_curve.id,
                    "price": price_value,
                }
                price_entries.append(price_entry)

        if price_entries:
            prices_table = PricesModel.__table__
            stmt = insert(prices_table).values(price_entries)
            stmt = stmt.on_conflict_do_update(
                index_elements=["date_id", "time_id", "price_curve_id"],
                set_={"price": stmt.excluded.price}
            )
            with self.database.engine.connect() as conn:
                conn.execute(stmt)
                conn.commit()


    def _get_sett_prices(self, country):
        get_sett_prices_query = f"""
                SELECT
                    tsv.date_time_tick,
                    tsv.numeric_value,
                    tsv.date_time_value,
                    tsv.date_time_to_value,
				    CASE
				        WHEN SPLIT_PART(ts.name, '_', 4) = 'USDMMBTU'
				            THEN SPLIT_PART(ts.name, '_', 3) || '_USDMMBTU ' || REPLACE(ts.category, 'Natural Gas|Price|Actual|Futures|Financial|Settlement|', '')
				        WHEN ts.category LIKE 'Power|%'
				            THEN REPLACE(ts.category, 'Power|Price|Actual|Futures|Financial|Settlement|', '')
				        WHEN ts.category LIKE 'Natural Gas|%'
				            THEN SPLIT_PART(ts.name, '_', 3) || ' ' || REPLACE(ts.category, 'Natural Gas|Price|Actual|Futures|Financial|Settlement|', '')
				        ELSE ts.category
				    END AS json_value
                FROM
                    timeseries.time_series_value AS tsv
                JOIN
                    timeseries.level_relation_view AS ts
                ON
                    tsv.time_series_id = ts."id"
                WHERE
                    tsv.time_series_id IN (
                        SELECT "id"
                        FROM timeseries.level_relation_view
                        WHERE
                            category LIKE '%Price|Actual|Futures|Financial|Settlement|%'
                            AND geography_from = '{country}|Country'
                            AND provider = 'EEX|Web'
                    )
                    AND tsv.date_time_tick = (
                        SELECT MAX(date_time_tick)
                        FROM timeseries.time_series_value
                        WHERE time_series_id IN (
                            SELECT "id"
                            FROM timeseries.level_relation_view
                            WHERE
                                category LIKE '%Price|Actual|Futures|Financial|Settlement|%'
                                AND geography_from = '{country}|Country'
                                AND provider = 'EEX|Web'
                        )
                    )
                """

        with self.database.engine.connect() as conn:
            return conn.execute(text(get_sett_prices_query)).all()

    def _get_max_pfc_date(self, price_curve):
        get_sett_prices_query = f"""
            select price_curve, max(time) as start_date from trading_dw.v_pfc_utc
            where price_curve = '{price_curve}'
            group by price_curve
        """
        with self.database.engine.connect() as conn:
            result = conn.execute(text(get_sett_prices_query), {"price_curve": price_curve}).fetchone()

        return result.start_date if result else None


    def _get_sett_coupon_prices(self):
        get_sett_prices_query = f"""
                SELECT
                    tsv.date_time_tick,
                    tsv.numeric_value,
                    tsv.date_time_value + interval '1 year' as date_time_value,
                    tsv.date_time_to_value + interval '1 year' as date_time_to_value,
                    REPLACE(ts.category, 'CO2|Price|Actual|Futures|Financial|Settlement|', 'EEX EU EUA ') AS json_value
                FROM
                    timeseries.time_series_value AS tsv
                JOIN
                    timeseries.level_relation_view AS ts
                ON
                    tsv.time_series_id = ts."id"
                WHERE
                    tsv.time_series_id IN (
                        SELECT "id"
                        FROM timeseries.level_relation_view
                        WHERE
                            category LIKE '%CO2|Price|Actual|Futures|Financial|Settlement|%'
                            AND geography_from like '%|Region'
                            AND provider like '%|Web'
                    )
                    AND tsv.date_time_tick = (
                        SELECT MAX(date_time_tick)
                        FROM timeseries.time_series_value
                        WHERE time_series_id IN (
                            SELECT "id"
                            FROM timeseries.level_relation_view
                            WHERE
                                category LIKE '%CO2|Price|Actual|Futures|Financial|Settlement|%'
                            AND geography_from like '%|Region'
                            AND provider like '%|Web'
                        )
                    )
                """

        with self.database.engine.connect() as conn:
            return conn.execute(text(get_sett_prices_query)).all()

    def resample_timeranges(self, df, resolution='h', delta_hours=1):
        utc_tz = pytz.UTC
        cet_tz = pytz.timezone('Europe/Ljubljana')

        original_tick = df['date_time_tick'].min()

        min_time_cet = df['date_time_value'].min().astimezone(cet_tz)
        max_time_cet = df['date_time_to_value'].max().astimezone(cet_tz)

        if resolution == 'D':
            index_cet = pd.date_range(
                start=min_time_cet.floor('D'),
                end=max_time_cet.ceil('D'),
                freq='D',
                inclusive='left',
                tz=cet_tz
            )
            index = index_cet.tz_convert(utc_tz)
        else:
            index = pd.date_range(
                start=df['date_time_value'].min().floor(resolution),
                end=df['date_time_to_value'].max().ceil(resolution),
                freq=resolution,
                inclusive='left',
                tz=utc_tz
            )

        result = pd.DataFrame(index=index, columns=['numeric_value'])
        result['numeric_value'] = 0.0

        for _, row in df.iterrows():
            if resolution == 'D':
                start_cet = row['date_time_value'].astimezone(cet_tz).floor('D')
                end_cet = row['date_time_to_value'].astimezone(cet_tz).ceil('D')
                diffs_cet = pd.date_range(
                    start=start_cet,
                    end=end_cet,
                    freq='D',
                    inclusive='left',
                    tz=cet_tz
                )
                diffs = diffs_cet.tz_convert(utc_tz)
            else:
                diffs = pd.date_range(
                    start=row['date_time_value'].floor(resolution),
                    end=row['date_time_to_value'].ceil(resolution),
                    freq=resolution,
                    inclusive='left',
                    tz=utc_tz
                )

            for diff in diffs:
                diff_start = diff
                diff_end = diff + pd.Timedelta(hours=delta_hours)
                range_start = max(row['date_time_value'], diff_start)
                range_end = min(row['date_time_to_value'], diff_end)

                if range_start < range_end:
                    diff_fraction = (range_end - range_start) / pd.Timedelta(hours=delta_hours)
                    result.loc[diff, 'numeric_value'] += row['numeric_value'] * diff_fraction

        final_df = pd.DataFrame({
            'date_time_value': [dt for dt in result.index],  # Ensure we use the UTC-converted CET day starts
            'date_time_tick': original_tick,
            'numeric_value': result['numeric_value'].values
        })

        final_df['date_time_to_value'] = final_df['date_time_value'].apply(
            lambda x: (x.astimezone(cet_tz) + pd.Timedelta(days=1)).astimezone(utc_tz)
        )

        final_df = final_df.reset_index(drop=True)
        final_df['date_time_value'] = pd.to_datetime(final_df['date_time_value']).dt.tz_convert(utc_tz)
        final_df['date_time_to_value'] = pd.to_datetime(final_df['date_time_to_value']).dt.tz_convert(utc_tz)
        final_df['date_time_tick'] = pd.to_datetime(final_df['date_time_tick']).dt.tz_convert(utc_tz)

        final_df = final_df[['date_time_value', 'date_time_to_value', 'date_time_tick', 'numeric_value']]
        final_df = final_df.drop_duplicates(subset=['date_time_value'])

        return final_df

