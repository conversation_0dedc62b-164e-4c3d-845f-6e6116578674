from sqlalchemy import Column, String, DateTime
from datetime import datetime, timezone

class AuditMixin:
    created_at = Column(DateTime, default=datetime.now(tz=timezone.utc), nullable=True)
    created_by = Column(String(100), default='DWH', nullable=True)
    modified_at = Column(DateTime, default=datetime.now(tz=timezone.utc), onupdate=datetime.now(tz=timezone.utc), nullable=True)
    modified_by = Column(String(100), default='DWH', nullable=True)
