from pyexcel_io.database.querysets import QuerysetsReader
from Dimensions.Limit.limit_model import LimitModel
from Dimensions.LimitGroup.limit_group_model import LimitGroupModel
from Dimensions.LimitType.limit_type_model import LimitTypeModel
from Dimensions.Book.book_model import BookModel
from Dimensions.Partner.partner_model import PartnerModel
from Dimensions.Portfolio.portfolio_model import PortfolioModel
from Dimensions.Trader.trader_model import TraderModel
from Dimensions.ProductType.product_type_model import ProductTypeModel
from libs.setup_orm_utility import SetupOrmUtility

import logging

logger = logging.getLogger(__name__)


class LimitImporter():

    def __init__(self, config_name):
        self.setup_orm_utility = SetupOrmUtility(config_name)


        query_sets = self.setup_orm_utility.codes_session.query(LimitModel).all()
        reader = QuerysetsReader(query_sets, ["name"])
        self.existing_limits = list(reader.to_array())

    def import_data(self):
        self.rows_inserted = 0

        logger.info('Import started')

        self.limit_groups = self.setup_orm_utility.codes_session.query(LimitGroupModel).all()
        self.limit_types = self.setup_orm_utility.codes_session.query(LimitTypeModel).all()
        self.portfolios = self.setup_orm_utility.codes_session.query(PortfolioModel).all()
        self.partners = self.setup_orm_utility.codes_session.query(PartnerModel).all()
        self.traders = self.setup_orm_utility.codes_session.query(TraderModel).all()
        self.books = self.setup_orm_utility.codes_session.query(BookModel).all()
        self.product_types = self.setup_orm_utility.codes_session.query(ProductTypeModel).all()

        self.setup_orm_utility.import_data(self.limit_init_func, 'limit', LimitModel)

        logger.info('Import ended. Inserted %s rows', self.rows_inserted)

    def limit_init_func(self, row):

        if self.setup_orm_utility.key_exists(self.existing_limits, row['name']):
            return None

        product_type = next((x for x in self.product_types if x.name == row['product_type_id']), None)

        limit_group = next((x for x in self.limit_groups if x.name == row['limit_group_id']), None)
        if limit_group is None:
            logger.error('Missing limit_group "%s"', row['limit_group_id'])
            return None

        limit_type = next((x for x in self.limit_types if x.name == row['limit_type_id']), None)
        if limit_type is None:
            logger.error('Missing limit_type "%s"', row['limit_type_id'])
            return None

        match limit_group.name:
            case 'portfolio':
                portfolio = next((x for x in self.portfolios if x.portfolio_code == row['subject_id']), None)
                if portfolio is None:
                    logger.error('Missing portfolio with code "%s"', row['subject_id'])
                    return None

                subject_id = portfolio.id

            case 'counterparty':
                counterparty = next((x for x in self.partners if x.short_name == row['subject_id']), None)
                if counterparty is None:
                    logger.error('Missing counterparty with short name "%s"', row['subject_id'])
                    return None

                subject_id = counterparty.id

            case 'company':
                company = next((x for x in self.partners if x.short_name == row['subject_id']), None)
                if company is None:
                    logger.error('Missing company with short name "%s"', row['subject_id'])
                    return None

                subject_id = company.id

            case 'trader':
                trader = next((x for x in self.traders if x.trader_name == row['subject_id']), None)
                if trader is None:
                    logger.error('Missing trader with name "%s"', row['subject_id'])
                    return None

                subject_id = trader.id

            case 'tradebook':
                book = next((x for x in self.books if x.book_code == row['subject_id']), None)
                if book is None:
                    logger.error('Missing book with code "%s"', row['subject_id'])
                    return None

                subject_id = book.id

            case _:
                logger.error('Cannot assign subject id. Possible values: portfolio, counterparty, trader, tradebook')
                return None

        self.rows_inserted += 1

        return LimitModel(
            row['name'],
            limit_group.id,
            limit_type.id,
            subject_id,
            row['time_period'],
            row['area'],
            row['unit'],
            row['limit_value'],
            product_type.id if product_type is not None else None)
