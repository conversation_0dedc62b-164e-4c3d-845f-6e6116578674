import os
import psycopg
import pandas as pd
from openpyxl import load_workbook

# Database connection parameters
db_connection_params = {
    "dbname": "fms-prod",
    "user": "fmsReadWrite",
    "password": "rwFM$9172!!",
    "host": "db-prod.kolektorsetup.si",
    "port": "5432"
}

# Tables to exclude from the Excel file
excluded_tables = ['dim_date', 'dim_time', 'trades', 'var_scenarios', 'prices', 'contract', 'contract_value', 'partner']

# File path for the Excel file
excel_file_path = 'trading_dw_data.xlsx'

# Remove the existing Excel file if it exists
if os.path.exists(excel_file_path):
    os.remove(excel_file_path)

# Connect to the PostgreSQL database
conn = psycopg.connect(**db_connection_params)
cursor = conn.cursor()

# Query to get all table names in the schema 'trading_dw'
cursor.execute("""
    SELECT table_name
    FROM information_schema.tables
    WHERE table_schema = 'trading_dw' AND table_type = 'BASE TABLE';
""")
tables = cursor.fetchall()

# Initialize a dictionary to hold DataFrames for each table
dataframes = {}

# Loop through each table and fetch its columns
for table in tables:
    table_name = table[0]
    if table_name in excluded_tables:
        continue

    cursor.execute(f"""
        SELECT column_name
        FROM information_schema.columns
        WHERE table_schema = 'trading_dw' AND table_name = '{table_name}';
    """)
    columns = cursor.fetchall()

    # Filter out columns based on exclusion criteria
    filtered_columns = [col[0] for col in columns if col[0] not in ['id', 'created_by', 'modified_by', 'created_at', 'modified_at', 'is_deleted']]

    if filtered_columns:
        # Fetch the data for the filtered columns
        cursor.execute(f"""
            SELECT {', '.join(filtered_columns)}
            FROM trading_dw.{table_name};
        """)
        data = cursor.fetchall()

        # Create a DataFrame and add it to the dictionary
        df = pd.DataFrame(data, columns=filtered_columns)
        dataframes[table_name] = df

# Close the cursor and the connection
cursor.close()
conn.close()

# Create an Excel writer object
with pd.ExcelWriter(excel_file_path, engine='openpyxl') as writer:
    for table_name, df in dataframes.items():
        # Write each DataFrame to a separate sheet
        df.to_excel(writer, sheet_name=table_name, index=False)

# Load the workbook to adjust column widths
wb = load_workbook(excel_file_path)

for sheet in wb.sheetnames:
    worksheet = wb[sheet]
    for col in worksheet.columns:
        max_length = 0
        column = col[0].column_letter  # Get the column name
        for cell in col:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = (max_length + 2)
        worksheet.column_dimensions[column].width = adjusted_width

# Save the workbook
wb.save(excel_file_path)

print("Excel file created successfully.")
