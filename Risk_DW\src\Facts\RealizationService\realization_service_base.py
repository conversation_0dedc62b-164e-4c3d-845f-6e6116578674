import logging
from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Tuple, Generator
from sqlalchemy import text, insert
from sqlalchemy.orm import Session
from decimal import Decimal

logger = logging.getLogger(__name__)

class RealizationServiceBase(ABC):
    def __init__(self, database):
        self.database = database
        self.batch_size = 10000

    @abstractmethod
    def fetch_entities(self) -> Tuple[List, List]:
        pass

    @abstractmethod
    def get_sells_generator(self, entity_id: int, product_id: int) -> Generator[Dict, None, None]:
        pass

    @abstractmethod
    def get_buys_generator(self, entity_id: int, product_id: int) -> Generator[Dict, None, None]:
        pass

    @abstractmethod
    def get_model(self):
        pass

    def process(self):
        logger.info(f"Starting process for: {self.__class__.__name__}")
        entities, products = self.fetch_entities()

        all_realizations = []
        for entity in entities:
            for product in products:
                sells = self.get_sells_generator(entity.id, product.id)
                buys = list(self.get_buys_generator(entity.id, product.id))

                #sells = self._filter_retail_hedge_trades(sells)
                #buys = self._filter_retail_hedge_trades(buys)


                entity_realizations = self._process_entity_product(sells, buys)
                all_realizations.extend(entity_realizations)

                if len(all_realizations) >= self.batch_size:
                    self._commit_realizations_to_db(all_realizations)
                    all_realizations = []

        if all_realizations:
            self._commit_realizations_to_db(all_realizations)

        logging.info("Process completed.")

    def _process_entity_product(self, sells: Generator[Dict, None, None], buys: List[Dict]) -> List[Dict]:
        entity_realizations = []
        for sell in sells:
            remaining_sells = sell['quantity_reminder_sell']
            while remaining_sells > 0:
                buy = self._get_matching_buy_pair(buys, sell)
                if not buy:
                    logging.info(f"No matching buy pair found for sell: {sell['identification_description']}.")
                    break

                realization, reminder_buy, remainder_sell = self._create_realization(buy, sell, remaining_sells)

                entity_realizations.append(realization)

                buy['quantity_reminder_buy'] = reminder_buy
                sell['quantity_reminder_sell'] = remainder_sell

                remaining_sells = remainder_sell
                if remaining_sells == 0:
                    break

        return entity_realizations

    def _create_realization(self, buy: Dict, sell: Dict, remaining_sells: float) -> Tuple[Dict, float, float]:
        processed_quantity = min(buy['quantity_reminder_buy'], remaining_sells)

        reminder_buy = buy['quantity_reminder_buy'] - processed_quantity
        remainder_sell = remaining_sells - processed_quantity

        new_realization = {
            **({f'{key}': buy[key] for key in ['book_id', 'trader_id', 'portfolio_id'] if key in buy}),
            'identification_description_buy': buy['identification_description'],
            'trading_interval_end_id_buy': buy['interval_end_id'],
            'price_buy': buy['price'],
            'quantity_buy': buy['quantity'],
            'trade_date_id_buy': buy['trade_date_id'],
            'trade_date_id_sell': sell['trade_date_id'],
            'identification_description_sell': sell['identification_description'],
            'trading_interval_start_id': sell['interval_start_id'],
            'trading_interval_end_id_sell': sell['interval_end_id'],
            'price_sell': sell['price'],
            'quantity_sell': sell['quantity'],
            'quantity_reminder_buy': reminder_buy,
            'quantity_reminder_sell': remainder_sell,
            'realization': (sell['price'] - buy['price']) * processed_quantity,
            'product_id_buy': sell.get('product_id'),
            'product_id_sell': sell.get('product_id'),
            'buy_closed': buy['quantity_reminder_buy'] == processed_quantity,
            'sell_closed': remaining_sells == processed_quantity
        }

        return new_realization, reminder_buy, remainder_sell

    def _get_matching_buy_pair(self, buys: List[Dict], sell: Dict) -> Optional[Dict]:
        for buy in buys:
            if (buy['interval_start'] == sell['interval_start'] and
                buy['interval_end'] == sell['interval_end'] and
                not buy.get('is_processed', False) and
                buy['quantity_reminder_buy'] > 0):
                return buy
        return None

    def _commit_realizations_to_db(self, realizations: List[Dict]):
        with Session(self.database.engine) as session:
            try:
                session.execute(insert(self.get_model()), realizations)
                session.commit()
            except Exception as e:
                logging.error(f"Failed to commit realizations to the database: {e}")
                session.rollback()


    def _execute_query(self, query: str) -> List[Dict]:
        logging.debug(f"Executing query: {query}")
        with self.database.engine.connect() as conn:
            try:
                result = conn.execute(text(query))
                columns = result.keys()
                rows = result.fetchall()
                result = [dict(zip(columns, row)) for row in rows]
                logging.debug(f"Query returned {len(result)} rows.")
                return result
            except Exception as e:
                logging.error(f"Query execution failed: {e}")
                return []


    def _filter_retail_hedge_trades(self, trades_generator):
        """
        Filter out trades with book code 'Retail_Hedge'
        """
        if self.__class__.__name__ not in ['RealizationCompanyService', 'RealizationTraderService']:
            return trades_generator

        logging.debug(f"Filtering out Retail_Hedge trades for {self.__class__.__name__}")

        retail_hedge_cache = set()
        checked_identifications = set()

        def filtered_generator():
            for trade in trades_generator:
                identification = trade.get('identification_description')

                if not identification:
                    yield trade
                    continue

                if identification in checked_identifications:
                    if identification not in retail_hedge_cache:
                        yield trade
                    continue

                try:
                    with Session(self.database.engine) as session:
                        query = text("""
                            SELECT b.book_code
                            FROM trading_dw.trade t
                            LEFT JOIN trading_dw.book b ON t.trade_book_id = b.id
                            WHERE t.identification_description = :identification
                        """)

                        results = session.execute(query, {"identification": identification}).fetchall()

                        checked_identifications.add(identification)

                        # Handle case with no results
                        if not results:
                            yield trade
                            continue

                        # Check if any non-null book code is 'Retail_Hedge'
                        if any(result.book_code == 'Retail_Hedge' for result in results if result.book_code is not None):
                            retail_hedge_cache.add(identification)
                            logging.debug(f"Filtered out Retail_Hedge trade: {identification}")
                            continue

                        yield trade

                except Exception as e:
                    logging.error(f"Error checking if '{identification}' is a Retail_Hedge trade: {e}")
                    yield trade

        return filtered_generator()

    def _convert_decimal_to_float(self, raw_results: List[Dict]) -> List[Dict]:
        for result in raw_results:
            for key, value in result.items():
                if isinstance(value, Decimal):
                    result[key] = float(value)
        return raw_results