from sqlalchemy import  Column, Integer, Numeric, String, Text
from setup_global import SetupGlobal
from libs.mixin import AuditMixin

class PortfolioBookModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'portfolio_book'
    id = Column(Integer, primary_key=True)
    portfolio_id = Column(String(50))
    book_id = Column(String(100))
    allocation_percent = Column(Integer)
    description = Column(Text)

    def __init__ (self,
                portfolio_id,
                book_id,
                allocation_percent,
                description):

        self.portfolio_id = portfolio_id
        self.book_id = book_id
        self.allocation_percent = allocation_percent
        self.description = description
