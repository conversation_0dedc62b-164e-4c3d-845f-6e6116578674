import pytz
from datetime import datetime, timezone
from Dimensions.ExchangeRate.exchange_rate_model import ExchangeRateModel
from Dimensions.Currency.currency_model import CurrencyModel
from Dimensions.DimDate.dim_date_model import DimDateModel
from libs.setup_orm_utility import SetupOrmUtility
from urllib.request import Request, urlopen
from xml.dom.minidom import parseString
from sqlalchemy import and_, func
from datetime import  timedelta

import logging

logger = logging.getLogger(__name__)

class ExchangeRateImporter():

    def __init__(self, config_name):
        self.setup_orm_utility = SetupOrmUtility(config_name)


    def import_data(self, start_date):

        end_date = datetime.now(tz=timezone.utc).date()

        self.rows_inserted = 0

        logger.info('Import started')

        current_start_date_id = self.setup_orm_utility.codes_session.query(func.max(ExchangeRateModel.exchange_date_id)).scalar()

        if current_start_date_id is not None:
            current_start_date = self.setup_orm_utility.codes_session.query(DimDateModel).filter(
                DimDateModel.id == current_start_date_id).one_or_none()

            if current_start_date is None:
                logger.error("Missing dim date '%s' for initializing exchange rates.")
                return

            start_date = current_start_date.date + timedelta(days=1)

        if start_date < end_date:
            data = self._get_data(start_date, end_date)
            if data is not None:
                self.setup_orm_utility.import_data(self.exchange_rate_init_func, 'exchange_rate',ExchangeRateModel, data)

        logger.info('Import ended. Inserted %s rows', self.rows_inserted)

    def exchange_rate_init_func(self,row):

        self.rows_inserted += 1

        return ExchangeRateModel(
            row['currency_id'],
            row['rate'],
            row['exchange_date_id'])

    def _get_data(self, start_date, end_date):

        # avoid scrapping detection
        req = Request(
        url='https://www.bsi.si/_data/tecajnice/dtecbs-l.xml',
            headers={'User-Agent': 'Mozilla/5.0'}
        )

        document = parseString(urlopen(req).read())
        root = document.getElementsByTagName('DtecBS')[0]

        result  = [['currency_id', 'rate', 'exchange_date_id']]

        currencies = self.setup_orm_utility.codes_session.query(CurrencyModel).all()
        exchange_dates = self.setup_orm_utility.codes_session.query(DimDateModel).filter(
            and_(DimDateModel.date >= start_date, DimDateModel.date <= end_date)
        ).all()

        for exch_rate in root.getElementsByTagName('tecajnica'):
            for exch_rate_details in exch_rate.childNodes:
                exch_rate_date_cet = datetime.strptime(exch_rate.getAttribute('datum'), '%Y-%m-%d')
                utc_tz = pytz.utc
                exch_rate_date = utc_tz.localize(exch_rate_date_cet).date()

                if exch_rate_date >= start_date and exch_rate_date <= end_date:
                    currency = next((x for x in currencies if str(x.currency_numeric_code) == exch_rate_details.getAttribute('sifra')), None)
                    if currency is not None:
                        exchange_date = next((x for x in exchange_dates if x.date == exch_rate_date), None)
                        if exchange_date is None:
                            logger.error('Missing trade_date for currency exchange "%s"', exch_rate_date)
                        else:
                            result.append([
                                    currency.id,
                                    exch_rate_details.childNodes[0].nodeValue,
                                    exchange_date.id])

        return {'exchange_rate' : result}