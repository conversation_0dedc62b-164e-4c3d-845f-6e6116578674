from typing import List, Dict, Generator
from types import SimpleNamespace
from Facts.RealizationService.realization_cross_model import RealizationCrossModel
from Facts.RealizationService.realization_service_base import RealizationServiceBase
from Facts.RealizationService.service_queries import Queries

class RealizationCrossService(RealizationServiceBase):
    def fetch_entities(self):
        products = [SimpleNamespace(id=1)]
        partners = [SimpleNamespace(id=1)]
        return partners, products
    
    def get_sells_generator(self, partner_id: int, product_id: int) -> Generator[Dict, None, None]:
        raw_results = self._execute_query(Queries.open_sells())
        converted_results = self._convert_decimal_to_float(raw_results)
        for sell in converted_results:
            yield sell

    def get_buys_generator(self, partner_id: int, product_id: int) -> Generator[Dict, None, None]:
        raw_results = self._execute_query(Queries.open_buys())
        converted_results = self._convert_decimal_to_float(raw_results)
        for buy in converted_results:
            yield buy

    def get_sells(self, partner_id: int, product_id: int) -> List[Dict]:
        return list(self.get_sells_generator())

    def get_buys(self, partner_id: int, product_id: int) -> List[Dict]:
        return list(self.get_buys_generator())

    def get_model(self):
        return RealizationCrossModel