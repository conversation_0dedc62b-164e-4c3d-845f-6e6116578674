WITH sum_buy AS (
    SELECT
  		SUM(value) as buy, date as date_buy
	FROM
  		trading_dw.report_trades 		
  	WHERE 
  		position_type = 'Buy'
	GROUP BY
  		date
),
sum_sell AS (
    SELECT
  		SUM(value) as sell, date as date_sell
	FROM
  		trading_dw.report_trades  		
  	WHERE 
  		position_type = 'Sell'
	GROUP BY
  		date
),
sum_mtm AS(
	
	SELECT
  		SUM(mtm) as mtm, date 
	FROM
	  	trading_dw.report_trades
	group by date
),
sum_limit AS(

	SELECT 
		SUM(limit_value) AS limit, date
	FROM 
		trading_dw.report_limits 
	WHERE 
		limit_group_name = 'company' and limit_type_name='mtm' and unit='EUR'
	GROUP BY date
)

SELECT 
	b.date_buy AS date,
	b.buy - s.sell AS realization,
	m.mtm,
	l.limit,
	b.buy - s.sell + m.mtm as stop_loss,
	CASE
    	WHEN 
			m.mtm < "limit" THEN 'MTM PREKORACITEV'
    	ELSE 
			'MTM OK'
	END AS mtm_status,
  	CASE
    	WHEN b.buy - s.sell + m.mtm < "limit" THEN 'STOP LOSS PREKORACITEV'
    	ELSE 
			'STOP LOSS OK'
  	END AS skupaj_status
FROM 
	sum_buy b
INNER JOIN 
	sum_sell s ON b.date_buy = s.date_sell
INNER JOIN 
	sum_mtm m ON  b.date_buy = m.date
INNER JOIN 
	sum_limit l ON EXTRACT(YEAR FROM b.date_buy)::text = l.date
ORDER BY date