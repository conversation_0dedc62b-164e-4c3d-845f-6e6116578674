from sqlalchemy import  Column, Integer, String
from setup_global import SetupGlobal
from libs.mixin import AuditMixin

class PartnerModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'partner'
    id = Column(Integer, primary_key=True)
    long_name = Column(String(255))
    short_name = Column(String(100))
    vat = Column(String(50))
    country_name = Column(String(100))
    country_code2 = Column(String(2))
    street = Column(String(255))
    city = Column(String(100))
    zip = Column(String(20))
    entity_type = Column(String(50))


    def __init__ (self,
                long_name,
                short_name,
                vat,
                country_name,
                country_code2,
                street,
                city,
                zip,
                entity_type):

        self.long_name = long_name
        self.short_name = short_name
        self.vat = vat
        self.country_name = country_name
        self.country_code2 = country_code2
        self.street = street
        self.city = city
        self.zip = zip
        self.entity_type = entity_type
