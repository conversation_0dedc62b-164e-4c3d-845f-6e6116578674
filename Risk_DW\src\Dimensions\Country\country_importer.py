from Dimensions.Country.country_model import CountryModel
from pyexcel_io.database.querysets import QuerysetsReader
from libs.setup_orm_utility import SetupOrmUtility

import logging

logger = logging.getLogger(__name__)


class CountryImporter():

    def __init__(self, config_name):
        self.setup_orm_utility = SetupOrmUtility(config_name)
        query_sets = self.setup_orm_utility.codes_session.query(CountryModel).all()

        reader = QuerysetsReader(query_sets, ["country_code2"])
        self.existing_countries = list(reader.to_array())

    def import_data(self):
        self.rows_inserted = 0

        logger.info('Import started')

        self.setup_orm_utility.import_data(self.country_init_func, 'country', CountryModel)

        logger.info('Import ended. Inserted %s rows', self.rows_inserted)

    def country_init_func(self,row):

        if self.setup_orm_utility.key_exists (self.existing_countries, row['country_code2']):
            return None

        self.rows_inserted += 1

        return CountryModel(row['country_name'],
                        row['official_country_name'],
                        row['local_country_name'],
                        row['country_name_in_slovene'],
                        row['country_code2'],
                        row['country_code3'],
                        row['country_numeric_code'],
                        row['currency_numeric_code'],
                        row['currency_code'])
