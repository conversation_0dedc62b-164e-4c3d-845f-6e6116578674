import time
import logging
import traceback

logger = logging.getLogger(__name__)

def calculate_company_realization(container):
    try:
        start_time = time.time()
        realization_service = container.realization_company_service().process()
        logger.info('Company realization took %s seconds', round(time.time() - start_time))
    except Exception as e:
        logger.error('Company realization failed: %s', traceback.format_exc())

def calculate_realization_for_trader(container):
    try:
        start_time = time.time()
        realization_service = container.realization_trader_service().process()
        logger.info('Realization for trader took %s seconds', round(time.time() - start_time))
    except Exception as e:
        logger.error('Realization for trader failed: %s', traceback.format_exc())

def calculate_realization_for_book(container):
    try:
        start_time = time.time()
        realization_service = container.realization_book_service().process()
        logger.info('Realization for book took %s seconds', round(time.time() - start_time))
    except Exception as e:
        logger.error('Realization for book failed: %s', traceback.format_exc())

def calculate_realization_for_portfolio(container):
    try:
        start_time = time.time()
        realization_service = container.realization_portfolio_service().process()
        logger.info('Realization for portfolio took %s seconds', round(time.time() - start_time))
    except Exception as e:
        logger.error('Realization for portfolio failed: %s', traceback.format_exc())

def calculate_realization_cross(container):
    try:
        start_time = time.time()
        realization_service = container.realization_cross_service().process()
        logger.info('Realization cross took %s seconds', round(time.time() - start_time))
    except Exception as e:
        logger.error('Realization cross failed: %s', traceback.format_exc())