
import threading
from Config.ConfigReader import ConfigReader
import requests
from dateutil.relativedelta import relativedelta
from datetime import datetime, timedelta

import logging
logger = logging.getLogger(__name__)

class BlueTraderAPI:

    def __init__(self, config_name):
        self._lock = threading.Lock()
        config = ConfigReader.get_config_section(config_name,"blue_trader")
        self.login_url = config.get('loginurl')
        self.grant_type = config.get('grantype')
        self.username = config.get('username')
        self.password = config.get('password')
        self.physical_power_trades_url = config.get('physicalpowertradesurl')
        self.financial_power_trades_url = config.get('financialpowertradesurl')
        self.financial_gas_trades_url = config.get('financialnaturalgastradesurl')
        self.financial_co2_trades_url = config.get('financialemmissiontradesurl')
        self.parties_url = config.get('partiesurl')
        self.power_spot_prices_url = config.get('powerspotpricesurl')
        self.contracts_url = config.get('contractsurl')

    def get_physical_power_spot_prices(self, delivery_date_from, delivery_date_to):

        params = {
            'deliveryDateFrom': delivery_date_from.strftime("%Y-%m-%d"),
            'deliveryDateTo': delivery_date_to.strftime("%Y-%m-%d"),
        }

        return self.get_trades_data(self.power_spot_prices_url, params)

    def get_parties(self):
        params = {
            #'partyTypes': 'CounterParty',
        }
        return self.get_trades_data(self.parties_url, params)

    def get_contracts(self):
        params = {
            #'partyTypes': 'CounterParty',
        }
        return self.get_trades_data(self.contracts_url, params)

    def get_physical_power_trades_data(self, start_date, end_date):
        logger.info('Inserting physical power trades started')

        trades_data = self._get_physical_power_trades(start_date, end_date)

        if trades_data is None or len(trades_data) == 0:
            logger.error('Physical power trades api returned empty list for interval ' + start_date.strftime("%Y-%m-%d") + ' - ' + end_date.strftime("%Y-%m-%d"))
            return None

        return trades_data

    def get_financial_power_trades_data(self, start_date, end_date):
        logger.info('Inserting financial power trades started')
        start_date = start_date - timedelta(days=1)
        trades_data = self._get_financial_power_trades(start_date, end_date)

#        FOR TESTING PURPOSES: BT can not get just individual trade
#        target_id = '2024-00117487'
#        filtered_trades = [trade for trade in trades_data if trade.get('identificationDescription') == target_id]
#        trades_data = filtered_trades

        if trades_data is None or len(trades_data) == 0:
            logger.error('Financial power trades api returned empty list for interval ' + start_date.strftime("%Y-%m-%d") + ' - ' + end_date.strftime("%Y-%m-%d"))
            return None

        return trades_data
    
    def get_financial_gas_trades_data(self, start_date, end_date):
        logger.info('Inserting financial natural gas trades started')
        start_date = start_date - timedelta(days=1)
        trades_data = self._get_financial_gas_trades(start_date, end_date)

#        FOR TESTING PURPOSES: BT can not get just individual trade
#        target_id = '2025-00001932'
#        filtered_trades = [trade for trade in trades_data if trade.get('identificationDescription') == target_id]
#        trades_data = filtered_trades

        if trades_data is None or len(trades_data) == 0:
            logger.error('Financial  natural gas trades api returned empty list for interval ' + start_date.strftime("%Y-%m-%d") + ' - ' + end_date.strftime("%Y-%m-%d"))
            return None

        return trades_data

    def get_financial_co2_trades_data(self, start_date, end_date):
        logger.info('Inserting financial emmission coupons - CO2 trades started')
        start_date = start_date - timedelta(days=1)
        trades_data = self._get_financial_co2_trades(start_date, end_date)

#        FOR TESTING PURPOSES: BT can not get just individual trade
#        target_id = '2024-00117487'
#        filtered_trades = [trade for trade in trades_data if trade.get('identificationDescription') == target_id]
#        trades_data = filtered_trades

        if trades_data is None or len(trades_data) == 0:
            logger.error('Financial emmission coupons - CO2 trades api returned empty list for interval ' + start_date.strftime("%Y-%m-%d") + ' - ' + end_date.strftime("%Y-%m-%d"))
            return None

        return trades_data


    def _get_physical_power_trades(self, start_date, end_date):
        params = {
            'timeIntervalFrom': start_date.strftime("%Y-%m-%d"),
            'timeIntervalTo': (end_date + relativedelta(years=5)).strftime("%Y-%m-%d"),
            'transactionDateTimeFrom': start_date.strftime("%Y-%m-%d"),
            'transactionDateTimeTo': end_date.strftime("%Y-%m-%d"),
            'IncludeActualRealization': 'true',
            'includeBillingPlans': 'true'
        }
        return self.get_trades_data(self.physical_power_trades_url, params)

    def _get_financial_power_trades(self, start_date, end_date):
        params = {
            'timeIntervalFrom': start_date.strftime("%Y-%m-%d"),
            'timeIntervalTo': (end_date + relativedelta(years=5)).strftime("%Y-%m-%d"),
            'transactionDateTimeFrom': start_date.strftime("%Y-%m-%d"),
            'transactionDateTimeTo': end_date.strftime("%Y-%m-%d"),
            'IncludeActualRealization': 'true',
            'includeBillingPlans': 'true'
        }
        return self.get_trades_data(self.financial_power_trades_url, params)

    def _get_financial_gas_trades(self, start_date, end_date):
        params = {
            'timeIntervalFrom': start_date.strftime("%Y-%m-%d"),
            'timeIntervalTo': (end_date + relativedelta(years=5)).strftime("%Y-%m-%d"),
            'transactionDateTimeFrom': start_date.strftime("%Y-%m-%d"),
            'transactionDateTimeTo': end_date.strftime("%Y-%m-%d"),
            'IncludeActualRealization': 'true',
            'includeBillingPlans': 'true'
        }
        return self.get_trades_data(self.financial_gas_trades_url, params)

    def _get_financial_co2_trades(self, start_date, end_date):
        params = {
            'timeIntervalFrom': start_date.strftime("%Y-%m-%d"),
            'timeIntervalTo': (end_date + relativedelta(years=5)).strftime("%Y-%m-%d"),
            'transactionDateTimeFrom': start_date.strftime("%Y-%m-%d"),
            'transactionDateTimeTo': end_date.strftime("%Y-%m-%d"),
            'IncludeActualRealization': 'true',
            'includeBillingPlans': 'true'
        }
        return self.get_trades_data(self.financial_co2_trades_url, params)


    def get_trades_data(self, api, params):
        with self._lock:
            token = self.get_token()

            headers = {
                'Authorization': f'Bearer {token}'
            }

            response = requests.get(api, headers=headers, params=params)

            if response.status_code == 200:
                return response.json()
            logger.error(f"Failed to get trades data: {response.status_code}.%s",response.text)
            return None

    def get_token(self):

        payload = {
            'grant_type': self.grant_type,
            'username': self.username,
            'password': self.password
        }

        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }

        response = requests.post(self.login_url, data=payload, headers=headers)
        if response.status_code == 200:
            return response.json().get('access_token')

        logger.error(f"Failed to login: {response.status_code}. %s", response.text)
        return None
