from sqlalchemy import  Column, Integer, String, Text
from setup_global import SetupGlobal
from libs.mixin import AuditMixin

class CommodityModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'commodity'
    id = Column(Integer, primary_key=True)
    name = Column(String(100))
    type = Column(String(100))
    description = Column(Text)
    unit = Column(String(50))
    currency_id = Column(Integer)


    def __init__(self,
                name,
                type,
                description,
                unit,
                currency_id):

        self.name = name
        self.type = type
        self.description = description
        self.unit = unit
        self.currency_id = currency_id
