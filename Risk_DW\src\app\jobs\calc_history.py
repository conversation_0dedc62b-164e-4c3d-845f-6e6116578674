import time, pytz
import logging
import traceback
from datetime import datetime, timedelta, timezone
from dateutil.relativedelta import relativedelta
from Config.ConfigReader import ConfigReader

logger = logging.getLogger(__name__)

def calculation_history(container):
    try:
        start_time = time.time()
        calc_manager = container.calculation_history_service().process()
        logger.info('Calculation history import took %s seconds', round(time.time() - start_time))
    except Exception as e:
        logger.error('Calculation history import failed: %s', traceback.format_exc())
