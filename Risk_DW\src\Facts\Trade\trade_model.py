from sqlalchemy import Column, Integer, Numeric, String, Boolean, Text, ForeignKey, TIMESTAMP, func
from sqlalchemy.orm import relationship
from setup_global import SetupGlobal
from libs.mixin import AuditMixin

class TradeModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'trade'
    identification_description = Column(String(13), primary_key=True)
    trade_date_id = Column(Integer)
    company_id = Column(Integer)
    currency_id = Column(Integer)
    counterparty_id = Column(Integer)
    position_type = Column(String(10))
    in_control_area_id = Column(Integer)
    out_control_area_id = Column(Integer)
    trade_book_id = Column(Integer)
    trader_id = Column(Integer)
    trade_name = Column(String(100))
    trade_type = Column(String(50))
    product_id = Column(Integer)
    general_agreement_contract_identification = Column(String(20))
    reserved_flag = Column(Boolean, default=False)

    # New column for transaction timestamp in UTC
    transaction_time = Column(TIMESTAMP(timezone=True), server_default=func.now(), nullable=False)

    trade_prices = relationship("TradePricesModel", back_populates="trade", cascade="all, delete-orphan")
    trade_fees = relationship("TradeFeesModel", back_populates="trade", cascade="all, delete-orphan")

    def __init__(self, identification_description, trade_date_id, company_id, currency_id, counterparty_id,
                 position_type, in_control_area_id, out_control_area_id, trade_book_id, trader_id, trade_name,
                 trade_type, product_id, general_agreement_contract_identification, transaction_time=None):
        self.identification_description = identification_description
        self.trade_date_id = trade_date_id
        self.company_id = company_id
        self.currency_id = currency_id
        self.counterparty_id = counterparty_id
        self.position_type = position_type
        self.in_control_area_id = in_control_area_id
        self.out_control_area_id = out_control_area_id
        self.trade_book_id = trade_book_id
        self.trader_id = trader_id
        self.trade_name = trade_name
        self.trade_type = trade_type
        self.product_id = product_id
        self.general_agreement_contract_identification = general_agreement_contract_identification
        self.transaction_time = transaction_time or func.now()
