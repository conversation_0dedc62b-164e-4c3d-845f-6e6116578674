from Dimensions.DimDate.dim_date_model import DimDateModel
from libs.setup_orm_utility import SetupOrmUtility
from libs.setup_date_utility import SetupDateUtility
from sqlalchemy import func
from datetime import  timedelta
from datetime import datetime

import logging

logger = logging.getLogger(__name__)

class DimDateImporter():

    def __init__(self, config_name):
        self.setup_orm_utility = SetupOrmUtility(config_name)


    def import_data(self, start_date, end_date):
        self.rows_inserted = 0

        logger.info('Import started')

        current_end_date = self.setup_orm_utility.codes_session.query(func.max(DimDateModel.date)).scalar()

        if current_end_date is not None:
            start_date = datetime.combine(current_end_date + timedelta(days=1), datetime.min.time())

        dates = self._get_data(start_date, end_date)
        self.setup_orm_utility.import_data(self.dim_date_init_func, 'dim_date',DimDateModel, dates)

        logger.info('Import ended. Inserted %s rows', self.rows_inserted)

    def dim_date_init_func(self,row):

        self.rows_inserted += 1

        return DimDateModel(
            row['date'],
            row['year'],
            row['half_year'],
            row['half_year_number'],
            row['quarter'],
            row['quarter_number'],
            row['month'],
            row['month_number'],
            row['month_name'],
            row['week_iso'],
            row['week_eex'],
            row['day_of_month'],
            row['day_of_week'],
            row['is_weekend'],
            row['summer_time']
        )

    def _get_data(self, start_date, end_date):

        result  = [
            [
                'date',
                'year',
                'half_year',
                'half_year_number',
                'quarter',
                'quarter_number',
                'month',
                'month_number',
                'month_name',
                'week_iso',
                'week_eex',
                'day_of_month',
                'day_of_week',
                'is_weekend',
                'summer_time']
            ]

        date_util = SetupDateUtility(start_date)

        while date_util.current_date < end_date:
            result.append(
                [
                date_util.current_date,
                date_util.current_date.year,
                date_util.get_half_year(),
                date_util.get_half_year_number(),
                date_util.get_quarter(),
                date_util.get_quarter_number(),
                date_util.get_month(),
                date_util.get_month_number(),
                date_util.get_month_name(),
                date_util.get_iso_week(),
                date_util.get_eex_week(),
                date_util.current_date.day,
                date_util.current_date.weekday(),
                date_util.is_weekend(),
                date_util.get_summer_time()
            ])
            date_util.increment_day()

        return {'dim_date' : result}
