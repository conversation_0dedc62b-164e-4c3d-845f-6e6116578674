from sqlalchemy import  Column, Integer, Numeric, String
from setup_global import SetupGlobal
from libs.mixin import AuditMixin

class PricesModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'prices'
    id = Column(Integer, primary_key=True)
    date_id = Column(Integer)
    time_id = Column(Integer)
    price_curve_id = Column(Integer)
    price = Column(Numeric(18,6))


    def __init__(self,
                date_id,
                time_id,
                price_curve_id,
                price,
                id = None):

        self.date_id = date_id
        self.time_id = time_id
        self.price_curve_id = price_curve_id
        self.price = price

        self.id = id