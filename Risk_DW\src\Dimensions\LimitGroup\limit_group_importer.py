from pyexcel_io.database.querysets import QuerysetsReader
from libs.setup_orm_utility import SetupOrmUtility
from Dimensions.LimitGroup.limit_group_model import LimitGroupModel

import logging

logger = logging.getLogger(__name__)

class LimitGroupImporter():

    def __init__(self, config_name):
        self.setup_orm_utility = SetupOrmUtility(config_name)


        query_sets = self.setup_orm_utility.codes_session.query(LimitGroupModel).all()
        reader = QuerysetsReader(query_sets, ["name"])
        self.existing_limit_groups = list(reader.to_array())

    def import_data(self):
        self.rows_inserted = 0

        logger.info('Import started')

        self.setup_orm_utility.import_data(self.limit_group_init_func, 'limit_group',LimitGroupModel)

        logger.info('Import ended. Inserted %s rows', self.rows_inserted)

    def limit_group_init_func(self,row):

        if self.setup_orm_utility.key_exists (self.existing_limit_groups, row['name']):
            return None

        self.rows_inserted += 1

        return LimitGroupModel(
            row['name'],
            row['description'])
