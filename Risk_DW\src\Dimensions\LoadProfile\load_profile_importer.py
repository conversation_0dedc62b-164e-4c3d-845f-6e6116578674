from pyexcel_io.database.querysets import QuerysetsReader
from libs.setup_orm_utility import SetupOrmUtility
from Dimensions.LoadProfile.load_profile_model import LoadProfileModel

import logging

logger = logging.getLogger(__name__)

class LoadProfileImporter():

    def __init__(self, config_name):
        self.setup_orm_utility = SetupOrmUtility(config_name)


        query_sets = self.setup_orm_utility.codes_session.query(LoadProfileModel).all()
        reader = QuerysetsReader(query_sets, ["name"])
        self.existing_load_profiles = list(reader.to_array())

    def import_data(self):
        self.rows_inserted = 0

        logger.info('Import started')

        self.setup_orm_utility.import_data(self.load_profile_init_func, 'load_profile', LoadProfileModel)

        logger.info('Import ended. Inserted %s rows', self.rows_inserted)

    def load_profile_init_func(self,row):

        if self.setup_orm_utility.key_exists (self.existing_load_profiles, row['name']):
            return None

        self.rows_inserted += 1

        return LoadProfileModel(
            row['name'],
            row['description'])
