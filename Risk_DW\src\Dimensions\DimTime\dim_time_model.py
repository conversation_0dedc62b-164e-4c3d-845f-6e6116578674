from sqlalchemy import  Column, Integer, String, Date,DateTime, Boolean, Time
from setup_global import SetupGlobal
from libs.mixin import AuditMixin

class DimTimeModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'dim_time'
    id = Column(Integer, primary_key=True)
    date = Column(Date)
    hour_interval = Column(Integer)
    date_hour_start = Column(String(13))
    date_hour_end = Column(String(13))
    date_hour_minute_start = Column(DateTime)
    date_hour_minute_end = Column(DateTime)
    date_hour_minute_start_end = Column(String(50))
    hour_minute_start  = Column(Time)
    hour_minute_end = Column(Time)
    hour_minute_start_end = Column(String(11))
    minute_start = Column(Integer)
    minute_end = Column(Integer)
    minute_start_end = Column(String(5))
    year = Column(Integer)
    half_year = Column(String(2))
    half_year_number = Column(Integer)
    quarter = Column(String(2))
    quarter_number = Column(Integer)
    month = Column(String(3))
    month_number = Column(Integer)
    month_name = Column(String(20))
    week_iso = Column(String(3))
    week_eex = Column(String(4))
    day_of_month = Column(Integer)
    day_of_week = Column(String(10))
    is_weekend = Column(Boolean)


    def __init__ (self,
                date,
                hour_interval,
                date_hour_start,
                date_hour_end,
                date_hour_minute_start,
                date_hour_minute_end,
                date_hour_minute_start_end,
                hour_minute_start,
                hour_minute_end,
                hour_minute_start_end,
                minute_start,
                minute_end,
                minute_start_end,
                year,
                half_year,
                half_year_number,
                quarter,
                quarter_number,
                month,
                month_number,
                month_name,
                week_iso,
                week_eex,
                day_of_month,
                day_of_week,
                is_weekend):

        self.date = date

        self.hour_interval = hour_interval
        self.date_hour_start = date_hour_start
        self.date_hour_end = date_hour_end
        self.date_hour_minute_start = date_hour_minute_start
        self.date_hour_minute_end = date_hour_minute_end
        self.date_hour_minute_start_end = date_hour_minute_start_end
        self.hour_minute_start = hour_minute_start
        self.hour_minute_end = hour_minute_end
        self.hour_minute_start_end = hour_minute_start_end
        self.minute_start = minute_start
        self.minute_end = minute_end
        self.minute_start_end = minute_start_end
        self.year = year
        self.half_year = half_year
        self.half_year_number = half_year_number
        self.quarter = quarter
        self.quarter_number = quarter_number
        self.month = month
        self.month_number = month_number
        self.month_name = month_name
        self.week_iso = week_iso
        self.week_eex = week_eex
        self.day_of_month = day_of_month
        self.day_of_week = day_of_week
        self.is_weekend = is_weekend
