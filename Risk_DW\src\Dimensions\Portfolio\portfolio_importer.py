from Dimensions.Portfolio.portfolio_model import PortfolioModel
from Dimensions.Book.book_model import BookModel
from Dimensions.Currency.currency_model import CurrencyModel
from Dimensions.Trader.trader_model import TraderModel
from libs.setup_orm_utility import SetupOrmUtility
from pyexcel_io.database.querysets import QuerysetsReader

import logging

logger = logging.getLogger(__name__)

class PortfolioImporter():

    def __init__(self,config_name):
        self.setup_orm_utility = SetupOrmUtility(config_name)
        # Get existing portfolios and store as dictionary for easier lookup
        self.existing_portfolios = {
            p.portfolio_code: p for p in
            self.setup_orm_utility.codes_session.query(PortfolioModel).all()
        }

    def import_data(self):
        self.rows_inserted = 0
        self.rows_updated = 0
        logger.info('Import started')
        self.setup_orm_utility.import_data(self.portfolio_init_func, 'portfolio',PortfolioModel)
        logger.info('Import ended. Inserted %s rows, Updated %s rows', self.rows_inserted, self.rows_updated)

    def portfolio_init_func(self,row):
        # Check if portfolio already exists
        existing_portfolio = self.existing_portfolios.get(row['portfolio_code'])
        if self.setup_orm_utility.key_exists (self.existing_portfolios, row['portfolio_code']):
            return None

        currency = self.setup_orm_utility.codes_session.query(CurrencyModel).filter_by(currency_code=row['currency_id']).first()
        if currency is None:
            logger.error('Missing currency "%s"', row['currency_id'])
            return None

        if existing_portfolio:
            # Check if relevant fields have changed
            fields_to_check = [
               "portfolio_name", "portfolio_type", "risk_profile", "parent_portfolio_id",
                "business_unit","strategy", "is_active", "var_limit", "position_limit", "description"
            ]
            has_changes = any(getattr(existing_portfolio, field) != row[field] for field in fields_to_check) or \
                  existing_portfolio.currency_id != currency.id

            if has_changes:
                for field in fields_to_check:
                    setattr(existing_portfolio, field, row[field])

                existing_portfolio.currency_id = currency.id

                self.setup_orm_utility.codes_session.commit()  # ✔ SQLAlchemy saves changes
                self.rows_updated += 1
                return None
            else:
                return None

        self.rows_inserted += 1

        return PortfolioModel(
            row['portfolio_code'],
            row['portfolio_name'],
            row['portfolio_type'],
            row['risk_profile'],
            row['business_unit'],
            row['strategy'],
            currency.id,
            row['is_active'],
            row['var_limit'],
            row['position_limit'],
            row['description'],
            '')