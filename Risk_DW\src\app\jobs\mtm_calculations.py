import time
import logging
import traceback
from datetime import datetime, timedelta, timezone
from dateutil.relativedelta import relativedelta

logger = logging.getLogger(__name__)

def calculate_mtm_for_company(container):
    try:
        start_time = time.time()
        mtm_service = container.company_mtm_service().process()
        logger.info('MTM for company took %s seconds', round(time.time() - start_time))
    except Exception as e:
        logger.error('MTM for company failed: %s', traceback.format_exc())

def calculate_mtm_for_trader(container):
    try:
        start_time = time.time()
        mtm_service = container.trader_mtm_service().process()
        logger.info('MTM for trader took %s seconds', round(time.time() - start_time))
    except Exception as e:
        logger.error('MTM for trader failed: %s', traceback.format_exc())

def calculate_mtm_for_book(container):
    try:
        start_time = time.time()
        mtm_service = container.book_mtm_service().process()
        logger.info('MTM for book took %s seconds', round(time.time() - start_time))
    except Exception as e:
        logger.error('MTM for book failed: %s', traceback.format_exc())