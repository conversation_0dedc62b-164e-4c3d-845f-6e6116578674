import sys
import os
from os import getenv
from ksLib.environment import Environment
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))
try:
    from app.app_factory import Container
    from app.utils.logging_config import setup_logging
    from app.utils.config_loader import load_config
    from app.utils.mail_service import MailService
    from app.jobs.data_imports import import_prices, import_trades, update_prices, merge_settlement_prices
    from app.jobs.realization_calculations import calculate_company_realization, calculate_realization_cross
    from app.jobs.realization_calculations import calculate_realization_for_book, calculate_realization_for_portfolio, calculate_realization_for_trader
    from app.jobs.mtm_calculations import calculate_mtm_for_company, calculate_mtm_for_trader, calculate_mtm_for_book
    from app.jobs.calc_history import calculation_history
    from app.jobs.send_mail import send_mail
except ImportError as e:
    print(f"Failed to import modules: {e}")
    sys.exit(1)

container = Container()
config_data = load_config()
container.config.from_dict(config_data)
logger = setup_logging()

if __name__ == "__main__" or getenv("RUN_MAIN"):
    environment = (getenv("ENVIRONMENT") or 'PROD').upper()
    e = Environment()
    e.load_environment_file(config_filename=".env")
    logger.info(f"DW v1.0.0 ({environment}) started")

#-------------------INITIAL PRICES---------------------------------------
    #import_prices(container=container)

#-------------------TRADES---------------------------------------
    #import_trades(container=container)

#-------------------PRICES---------------------------------------
    update_prices(container=container)
    merge_settlement_prices(container=container)

#-------------------REALIZATIONS---------------------------------
    calculate_company_realization(container=container)
    ##calculate_realization_cross(container=container)
    calculate_realization_for_book(container=container)
    calculate_realization_for_trader(container=container)
    ##calculate_realization_for_portfolio(container=container)
#-------------------MTMS---------------------------------
    calculate_mtm_for_company(container=container)
    calculate_mtm_for_trader(container=container)
    calculate_mtm_for_book(container=container)

#-------------------HISTORY---------------------------------
    calculation_history(container=container)

    send_mail(container=container)

    logger.info("All tests completed.")
