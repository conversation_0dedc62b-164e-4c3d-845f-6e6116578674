from Dimensions.PortfolioBook.portfolio_book_model import PortfolioBookModel
from Dimensions.Book.book_model import BookModel
from Dimensions.Portfolio.portfolio_model import PortfolioModel
from libs.setup_orm_utility import SetupOrmUtility
from pyexcel_io.database.querysets import QuerysetsReader

import logging

logger = logging.getLogger(__name__)

class PortfolioBookImporter():

    def __init__(self,config_name):
        self.setup_orm_utility = SetupOrmUtility(config_name)

        self.existing_portfolio_books = {
            (pb.portfolio_id, pb.book_id): pb  for pb in
            self.setup_orm_utility.codes_session.query(PortfolioBookModel).all()
        }

    def import_data(self):
        self.rows_inserted = 0
        self.rows_updated = 0
        logger.info('Import started')
        self.setup_orm_utility.import_data(self.portfolio_book_init_func, 'portfolio_book',PortfolioBookModel)
        logger.info('Import ended. Inserted %s rows, Updated %s rows', self.rows_inserted, self.rows_updated)

    def portfolio_book_init_func(self,row):
        portfolio = self.setup_orm_utility.codes_session.query(PortfolioModel).filter_by(portfolio_code=row['portfolio_code']).first()
        if portfolio is None:
            logger.error('Missing portfolio "%s"', row['portfolio_code'])
            return None

        book = self.setup_orm_utility.codes_session.query(BookModel).filter_by(book_code=row['book_code']).first()
        if book is None:
            logger.error('Missing book "%s"', row['book_code'])
            return None

         # Check if portfolio book already exists
        existing_pb = self.existing_portfolio_books.get((portfolio.id, book.id))

        if existing_pb :
            # Check if relevant fields have changed
            fields_to_check = [
                "allocation_percent", "description"
            ]
            has_changes = any(
                getattr(existing_pb , field) != row[field]
                for field in fields_to_check)

            if has_changes:
                for field in fields_to_check:
                    setattr(existing_pb , field, row[field])

                existing_pb .portfolio_id = portfolio.id
                existing_pb.book_id = book.id

                self.setup_orm_utility.codes_session.commit()  # ✔ SQLAlchemy saves changes
                self.rows_updated += 1
                return None
            else:
                return None

        self.rows_inserted += 1

        return PortfolioBookModel(
            portfolio.id,
            book.id,
            row['allocation_percent'],
            row['description'])
