import time, pytz
import logging
import traceback
import locale
from datetime import datetime, timedelta, timezone
from dateutil.relativedelta import relativedelta
from Config.ConfigReader import ConfigReader

logger = logging.getLogger(__name__)

def send_mail(container):
    try:
        calc_manager = container.calculation_history_service()
        pnl = calc_manager.get_last_pnl()

        locale.setlocale(locale.LC_ALL, 'sl_SI.UTF-8')
        pnl_value = pnl[0]["pnl"] if isinstance(pnl, list) and pnl else pnl
        formatted_pnl = locale.currency(pnl_value, grouping=True, symbol="€")

        tz = pytz.timezone('Europe/Ljubljana')
        date = datetime.now(tz)
        formatted_date = date.strftime('%Y-%m-%d %H:%M:%S %Z')

        mail_service = container.mail_service()

        subject = f"Data warehouse run on {formatted_date}"
        body = f"""
            Dear reader,

            Your dearest data warehouse finished working for today.
            The company PnL is {formatted_pnl}.

            With kind regards,

            Yours DWH\n\n

        """

        mail_config = ConfigReader.get_config_section(container.config.get("config_name"), "mail")
        recipients = mail_config.get("users", "").split(",")
        recipients = [email.strip() for email in recipients if email.strip()]

        mail_service.send_mail(subject, body, recipients)
    except Exception as e:
        logger.error('Send mail failed: %s', traceback.format_exc())