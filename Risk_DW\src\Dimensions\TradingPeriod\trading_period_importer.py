from pyexcel_io.database.querysets import QuerysetsReader
from libs.setup_orm_utility import SetupOrmUtility
from Dimensions.TradingPeriod.trading_period_model import TradingPeriodModel

import logging

logger = logging.getLogger(__name__)

class TradingPeriodImporter():

    def __init__(self, config_name):
        self.setup_orm_utility = SetupOrmUtility(config_name)


        query_sets = self.setup_orm_utility.codes_session.query(TradingPeriodModel).all()
        reader = QuerysetsReader(query_sets, ["resolution_seconds"])
        self.existing_trading_periods = list(reader.to_array())

    def import_data(self):
        self.rows_inserted = 0

        logger.info('Import started')

        self.setup_orm_utility.import_data(self.trading_period_init_func, 'trading_period', TradingPeriodModel)

        logger.info('Import ended. Inserted %s rows', self.rows_inserted)

    def trading_period_init_func(self,row):

        if self.setup_orm_utility.key_exists (self.existing_trading_periods, row['resolution_seconds']):
            return None

        self.rows_inserted += 1

        return TradingPeriodModel(
            row['name'],
            row['description'],
            row['resolution_seconds'])
