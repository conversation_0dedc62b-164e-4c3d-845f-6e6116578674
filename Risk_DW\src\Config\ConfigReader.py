import os
from configparser import ConfigParser
class ConfigReader:

    @staticmethod
    def get_config_section(filename, section):
        dir_path = os.path.dirname(os.path.realpath(__file__))
        full_path = dir_path + '/' + filename
        parser = ConfigParser()
        parser.read(full_path)

        section_dict = {}
        if parser.has_section(section):
            key_val_tuple = parser.items(section)
            for item in key_val_tuple:
                section_dict[item[0]] = item[1]

        return section_dict
