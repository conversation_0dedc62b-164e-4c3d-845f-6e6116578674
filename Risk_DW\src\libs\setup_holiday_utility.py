from datetime import  timedelta
import holidays
import holidays.countries

import logging

logger = logging.getLogger(__name__)

class SetupHolidayUtility:
    def __init__(self, country_code):
        global is_initialized
        
        try:
            if country_code == 'EU':
                country_code = 'DE'

            self.country_holidays = holidays.country_holidays(country_code)
            is_initialized = True
            
        except Exception as inst:
            is_initialized = False
            logger.error ("%s", inst)
        
    def is_initialized(self):
        return is_initialized
    
    def is_holiday(self, current_date):
        return current_date in self.country_holidays
    
    def is_holiday_bridge(self, current_date):
        prev_day = current_date - timedelta(days=1)
        next_day = current_date + timedelta(days=1)
        
        is_prev_day_holiday = False
        is_next_day_holiday = False
        
        if prev_day in self.country_holidays or prev_day.weekday() == 6:
            is_prev_day_holiday = True
        
        if next_day in self.country_holidays or next_day.weekday() == 5:
            is_next_day_holiday = True
            
        if is_prev_day_holiday and is_next_day_holiday:
            return True
        else:
            return False
    
    def is_pre_work_free_day(self, current_date):
        prev_day = current_date - timedelta(days=1)
        return prev_day in self.country_holidays
    
    def is_post_work_free_day(self, current_date):
        next_day = current_date + timedelta(days=1)
        return next_day in self.country_holidays