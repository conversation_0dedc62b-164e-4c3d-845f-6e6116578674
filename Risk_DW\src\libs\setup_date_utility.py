from datetime import  timedelta
import calendar
import time
import holidays

from libs.setup_calendar import SetupCalendar

class SetupDateUtility:
    def __init__(self, current_date):
        self.current_date = current_date
        self.sl_holidays = holidays.country_holidays('SI')
    
    def increment_minutes(self, minutes):
        self.current_date += timedelta(minutes=minutes)
        
    def increment_day(self):
        self.current_date += timedelta(days=1)
    
    def is_weekend(self):
        if self.current_date.weekday() < 5:
            return False
        else:
            return True
    
    def get_year(self):
        return self.current_date.year
    
    def get_half_year(self):
        if self.current_date.month < 6:
            return 'H1'
        else:
            return 'H2'
    
    def get_half_year_number(self):
        if self.current_date.month < 6:
            return 1
        else:
            return 2
        
    def get_quarter(self):
        return 'Q' + str( self.get_quarter_number())
    
    def get_quarter_number(self):
        return self.current_date.month//4 + 1
    
    def get_month_number(self):
        return self.current_date.month
    
    def get_month(self):
        return "M{month:02d}".format(month = self.current_date.month)
    
    def get_month_name(self):
        return calendar.month_name[self.current_date.month]
    
    def get_iso_week(self):
        return 'W' + str(self.get_iso_week_number())
    
    def get_iso_week_number(self):
        return self.current_date.isocalendar().week
    
    def get_eex_week_number(self):
        calendar = SetupCalendar(start_weekday=SetupCalendar.WEEKDAY.MON)
        return calendar.calculate(self.current_date)[1]
    
    def get_eex_week(self):
        return 'EW' + str(self.get_eex_week_number())
    
    def get_summer_time(self):
        previous_date = self.current_date - timedelta(days=1)
        
        previous = (previous_date.year, previous_date.month, previous_date.day,
            previous_date.hour, previous_date.minute, previous_date.second,
            previous_date.weekday(), 0, 0)
        
        stamp = time.mktime(previous)
        previous = time.localtime(stamp).tm_isdst
        
        current = (self.current_date.year, self.current_date.month, self.current_date.day,
            self.current_date.hour, self.current_date.minute, self.current_date.second,
            self.current_date.weekday(), 0, 0)
        
        stamp = time.mktime(current)
        current = time.localtime(stamp).tm_isdst
        
        if previous == 0 and current == 1:
            return 'WinterToSummerSwitch'
        elif previous==1 and current == 0:
            return 'SummerToWinterSwitch'
        elif current == 1:
            return 'SummerTime'
        elif current == 0:
            return 'WinterTime' 
    
    def get_is_summer_time(self):
        
        current = (self.current_date.year, self.current_date.month, self.current_date.day,
            self.current_date.hour, self.current_date.minute, self.current_date.second,
            self.current_date.weekday(), 0, 0)
        
        stamp = time.mktime(current)
        current = time.localtime(stamp).tm_isdst
        
        return current == 1
        
    def get_date_hour_start(self):
        return self.current_date.strftime("%d.%m.%Y %H")
    
    def get_date_hour_end(self):
        return (self.current_date + timedelta(hours=1)).strftime("%d.%m.%Y %H")
    
    def get_date_hour_minute_start(self):
        return self.current_date.strftime("%m.%d.%Y %H:%M")
    
    def get_date_hour_minute_end(self):
        return (self.current_date + timedelta(minutes=15)).strftime("%m.%d.%Y %H:%M")
    
    def get_date_hour_minute_start_end(self):
        return self.current_date + ' ' + self.get_hour_minute_start() + '-' + self.get_hour_minute_end()
    
    def get_hour_minute_start(self):
        return self.current_date.strftime("%H:%M")
    
    def get_hour_minute_end(self):
        return (self.current_date + timedelta(minutes=15)).strftime("%H:%M")
    
    def get_hour_minute_start_end(self):
        return self.get_date_hour_minute_start() + '-' + self.get_date_hour_minute_end()
    
    def get_minute_start(self):
        return self.current_date.strftime("%M")
    
    def get_minute_end(self):
        return (self.current_date + timedelta(minutes=15)).strftime("%M")
    
    def get_minute_start_end(self):
        return self.get_minute_start() + '-' + self.get_minute_end()