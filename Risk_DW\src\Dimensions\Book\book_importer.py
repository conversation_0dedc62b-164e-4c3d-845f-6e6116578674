from pyexcel_io.database.querysets import QuerysetsReader
from libs.setup_orm_utility import SetupOrmUtility
from Dimensions.Book.book_model import BookModel

import logging

logger = logging.getLogger(__name__)

class BookImporter():

    def __init__(self, config_name):
        self.setup_orm_utility = SetupOrmUtility(config_name)


        query_sets = self.setup_orm_utility.codes_session.query(BookModel).all()
        reader = QuerysetsReader(query_sets, ["book_code"])
        self.existing_books = list(reader.to_array())

    def import_data(self):
        self.rows_inserted = 0

        logger.info('Import started')

        self.setup_orm_utility.import_data(self.book_init_func, 'book',BookModel)

        logger.info('Import ended. Inserted %s rows', self.rows_inserted)

    def book_init_func(self,row):

        if self.setup_orm_utility.key_exists (self.existing_books, row['book_code']):
            return None

        self.rows_inserted += 1

        return BookModel(
                        row['book_code'],
                        row['book_name'],
                        row['description'])
