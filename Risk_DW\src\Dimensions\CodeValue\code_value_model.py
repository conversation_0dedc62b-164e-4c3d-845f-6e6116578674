from sqlalchemy import Column, Inte<PERSON>, String, ForeignKey
from sqlalchemy.orm import relationship
from setup_global import SetupGlobal
from libs.mixin import AuditMixin
from Dimensions.Code.code_model import CodeModel

class CodeValueModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'code_value'

    id = Column(Integer, primary_key=True, autoincrement=True)
    code_id = Column(Integer, ForeignKey('code.id'), nullable=False)
    value = Column(String(100), nullable=False)
    description = Column(String(255), nullable=True)

    code = relationship('CodeModel', back_populates='code_values')

    def __init__(self, code_id, value, description=None):
        self.code_id = code_id
        self.value = value
        self.description = description
