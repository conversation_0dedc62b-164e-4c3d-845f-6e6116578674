import psycopg
from psycopg import sql
#from psycopg.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import psycopg
ISOLATION_LEVEL_AUTOCOMMIT = psycopg.extensions.ISOLATION_LEVEL_AUTOCOMMIT
from Config.ConfigReader import ConfigReader

class SetupDbUtility:
    def __init__(self, config_name):
        self.config = ConfigReader.get_config_section(config_name,"db")

    def read_data_from_database(self, query):
        # Establish connection to the database
        conn = psycopg.connect(**self.config)
        cursor = conn.cursor()

        # Define query to fetch data
        cursor.execute(query)
        result = cursor.fetchall()

        # Close database connection
        conn.close()

        # Process query result
        return result

    def insert_into_table(self, table_name, data):
        try:

            conn = psycopg.connect(**self.config)
            conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)

            with conn.cursor() as cursor:
                columns = ", ".join(data[0].keys())
                placeholders = ", ".join(['%s'] * len(data[0]))
                insert_query = sql.SQL("INSERT INTO trading_dw.{} ({}) VALUES ({})").format(
                    sql.Identifier(table_name),
                    sql.SQL(columns),
                    sql.SQL(placeholders)
                )

                cursor.executemany(insert_query, [tuple(row.values()) for row in data])
                print(f"Inserted data into {table_name} successfully!")

        except psycopg.Error as e:
            print(f"Error inserting data: {e}")

        finally:
            if conn is not None:
                conn.close()