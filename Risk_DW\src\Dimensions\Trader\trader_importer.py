from pyexcel_io.database.querysets import QuerysetsReader
from libs.setup_orm_utility import SetupOrmUtility
from Dimensions.Trader.trader_model import TraderModel

import logging

logger = logging.getLogger(__name__)

class TraderImporter():

    def __init__(self, config_name):
        self.setup_orm_utility = SetupOrmUtility(config_name)


        query_sets = self.setup_orm_utility.codes_session.query(TraderModel).all()
        reader = QuerysetsReader(query_sets, ["trader_name"])
        self.existing_traders = list(reader.to_array())

    def import_data(self):
        self.rows_inserted = 0

        logger.info('Import started')

        self.setup_orm_utility.import_data(self.trader_init_func, 'trader',TraderModel)

        logger.info('Import ended. Inserted %s rows', self.rows_inserted)

    def trader_init_func(self,row):

        if self.setup_orm_utility.key_exists (self.existing_traders, row['trader_name']):
            return None

        self.rows_inserted += 1

        return TraderModel(
                        row['trader_name'],
                        row['department'],
                        row['business_unit'],
                        row['active'],
                        row['contact_info'],
                        row['trader_classification'])
