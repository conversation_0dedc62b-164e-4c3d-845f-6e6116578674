import os
import sys
import json
import asyncio
from datetime import date, timedelta
import uvicorn
import logging
from fastapi import FastAPI
from contextlib import asynccontextmanager
from Config.ConfigReader import ConfigReader
from os import getenv
from azure.eventhub.aio import EventHubConsumerClient
from app.jobs.data_imports import import_prices, import_trades, update_prices, merge_settlement_prices
from app.jobs.realization_calculations import (
    calculate_company_realization,
    calculate_realization_for_book,
    calculate_realization_for_trader
)
from app.jobs.mtm_calculations import calculate_mtm_for_company, calculate_mtm_for_trader, calculate_mtm_for_book
from app.jobs.calc_history import calculation_history
from app.jobs.send_mail import send_mail

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger(__name__)

logging.basicConfig(level=logging.INFO)  # Change to DEBUG if needed
logger = logging.getLogger(__name__)

app = FastAPI()

process_running = False
event_hub_client = None
container = None

async def run_data_processing_async():
    global process_running
    process_running = True
    logger.info("Starting data processing...")
    try:
        for task_name, task_func in [
            ("import_prices", import_prices),
            ("import_trades", import_trades),
            ("update_prices", update_prices),
            ("merge_settlement_prices", merge_settlement_prices),
            ("calculate_company_realization", calculate_company_realization),
            ("calculate_realization_for_book", calculate_realization_for_book),
            ("calculate_realization_for_trader", calculate_realization_for_trader),
            ("calculate_mtm_for_company", calculate_mtm_for_company),
            ("calculate_mtm_for_trader", calculate_mtm_for_trader),
            ("calculate_mtm_for_book", calculate_mtm_for_book),
            ("calculation_history", calculation_history),
            ("send_mail", send_mail),
        ]:
            try:
                await asyncio.to_thread(task_func, container=container)
                logger.info(f"Task '{task_name}' completed successfully.")
            except Exception as e:
                logger.error(f"Task '{task_name}' failed: {str(e)}", exc_info=True)
                process_running = False
                break
        logger.info("Data processing completed successfully.")
    except Exception as e:
        logger.error(f"Unexpected error in data processing: {str(e)}", exc_info=True)
    finally:
        process_running = False

async def on_event_async(partition_context, event):
    global process_running
    logger.info(f"Event received from partition {partition_context.partition_id}")
    try:
        if event is None:
            return
        message_str = event.body_as_str(encoding='UTF-8')
        logger.debug(f"Received event: {message_str}")
        message_dict = json.loads(message_str)

        title = message_dict.get("title")
        trade_day_str = message_dict.get("message", {}).get("tradeDay")
        if not title or not trade_day_str:
            logger.error(f"Malformed event data: missing 'title' or 'tradeDay'. Event: {message_str}")
            return

        today = date.today()
        if today.weekday() == 0:  # Monday is 0
            yesterday = today - timedelta(days=3)  # Friday
        else:
            yesterday = today - timedelta(days=1)

        trade_day = date.fromisoformat(trade_day_str)

        if (title == "Flexa - Confirmation of Settlement" and
            trade_day == yesterday and
            not process_running):
            logger.info("Event matches criteria, starting data processing.")
            asyncio.create_task(run_data_processing_async())
            await partition_context.update_checkpoint(event)
        else:
            reason = []
            if title != "Flexa - Confirmation of Settlement":
                reason.append(f"Title mismatch: '{title}'")
            if trade_day != yesterday:
                reason.append(f"Date mismatch: {trade_day} != {yesterday}")
            if process_running:
                reason.append("Processing already running")
            logger.info(f"Event does not match criteria: {', '.join(reason)}")

    except json.JSONDecodeError as e:
        logger.error(f"Failed to decode JSON from event body: {str(e)}", exc_info=True)
    except ValueError as e:
        logger.error(f"Invalid date format in tradeDay: {str(e)}", exc_info=True)
    except Exception as e:
        logger.error(f"Unexpected error processing event: {str(e)}", exc_info=True)

async def on_error_async(partition_context, error):
    logger.error(
        f"Error in EventProcessor for partition {partition_context.partition_id}: {str(error)}",
        exc_info=True
    )

@asynccontextmanager
async def lifespan(app: FastAPI):
    global event_hub_client, container
    receive_task = None
    try:
        from ksLib.environment import Environment
        environment = (getenv("ENVIRONMENT") or 'PROD').upper()
        e = Environment()
        e.load_environment_file(config_filename=".env")
        logger.info(f"DW v1.0.0 ({environment}) started")

        from app.app_factory import Container
        from app.utils.config_loader import load_config
        container = Container()
        config_data = load_config()
        container.config.from_dict(config_data)
        conf = ConfigReader.get_config_section(container.config.get("config_name"), "event_hub")

        # Initialize Event Hub client
        logger.info("Starting Event Hub client...")
        event_hub_client = EventHubConsumerClient.from_connection_string(
            conn_str=conf["connection"],
            consumer_group=conf["consumer_group"]
        )
        logger.info(f"Event Hub client created for {event_hub_client.eventhub_name}")

        try:
            partitions = await event_hub_client.get_partition_ids()
            logger.info(f"Connected to Event Hub. Partitions: {partitions}")
        except Exception as e:
            logger.error(f"Failed to connect to Event Hub or get partitions: {str(e)}", exc_info=True)
            raise

        async def start_event_hub():
            global event_hub_client
            try:
                logger.info("Starting Event Hub receive loop...")
                await event_hub_client.receive(
                    on_event=on_event_async,
                    on_error=on_error_async,
                    starting_position="@latest"
                )
            except Exception as e:
                logger.error(f"Error in Event Hub consumer: {str(e)}", exc_info=True)
            finally:
                logger.info("Event Hub consumer stopped.")

        receive_task = asyncio.create_task(start_event_hub())
        logger.info("Event Hub receive task started.")

        yield

    except Exception as e:
        logger.error(f"Error during startup: {str(e)}", exc_info=True)
        raise
    finally:
        logger.info("Shutting down Event Hub client...")
        if event_hub_client:
            try:
                await event_hub_client.close()
                logger.info("Event Hub client closed successfully.")
            except Exception as e:
                logger.error(f"Error closing Event Hub client: {str(e)}", exc_info=True)
        if receive_task:
            receive_task.cancel()
            try:
                await receive_task
            except asyncio.CancelledError:
                logger.info("Event Hub receive task cancelled successfully.")
        logger.info("Shutdown complete.")

app = FastAPI(lifespan=lifespan)

@app.get('/health')
async def health_check():
    try:
        return {"status": "OK"}
    except Exception as e:
        logger.error(f"Error occurred during health check: {str(e)}", exc_info=True)
        return {"status": "Error"}

if __name__ == "__main__" or getenv("RUN_MAIN"):
    try:
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=int(os.getenv("APP_PORT", 8125)),
            log_level=os.getenv("LOG_LEVEL", "info")
        )
    except Exception as e:
        logger.error(f"Error starting Uvicorn server: {str(e)}", exc_info=True)
        sys.exit(1)