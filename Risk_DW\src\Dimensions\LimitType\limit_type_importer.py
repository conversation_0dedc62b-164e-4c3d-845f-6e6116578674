from pyexcel_io.database.querysets import QuerysetsReader
from libs.setup_orm_utility import SetupOrmUtility
from Dimensions.LimitType.limit_type_model import LimitTypeModel

import logging

logger = logging.getLogger(__name__)

class LimitTypeImporter():

    def __init__(self, config_name):
        self.setup_orm_utility = SetupOrmUtility(config_name)


        query_sets = self.setup_orm_utility.codes_session.query(LimitTypeModel).all()
        reader = QuerysetsReader(query_sets, ["name"])
        self.existing_limit_types = list(reader.to_array())

    def import_data(self):
        self.rows_inserted = 0

        logger.info('Import started')

        self.setup_orm_utility.import_data(self.limit_type_init_func, 'limit_type',LimitTypeModel)

        logger.info('Import ended. Inserted %s rows', self.rows_inserted)

    def limit_type_init_func(self,row):

        if self.setup_orm_utility.key_exists (self.existing_limit_types, row['name']):
            return None

        self.rows_inserted += 1

        return LimitTypeModel(
            row['name'],
            row['description'])
