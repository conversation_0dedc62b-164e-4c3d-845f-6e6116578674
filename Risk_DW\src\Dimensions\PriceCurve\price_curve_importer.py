from Dimensions.PriceCurve.price_curve_model import PriceCurveModel
from libs.setup_orm_utility import SetupOrmUtility
from pyexcel_io.database.querysets import QuerysetsReader

import logging

logger = logging.getLogger(__name__)

class PriceCurveImporter():

    def __init__(self, config_name):
        self.setup_orm_utility = SetupOrmUtility(config_name)


        query_sets = self.setup_orm_utility.codes_session.query(PriceCurveModel).all()
        reader = QuerysetsReader(query_sets, ["price_curve_name"])
        self.existing_price_curves = list(reader.to_array())

    def import_data(self):
        self.rows_inserted = 0

        logger.info('Import started')

        self.setup_orm_utility.import_data(self.price_curve_init_func, 'price_curve',PriceCurveModel)

        logger.info('Import ended. Inserted %s rows', self.rows_inserted)

    def price_curve_init_func(self,row):
        if self.setup_orm_utility.key_exists (self.existing_price_curves, row['price_curve_name']):
            return None

        self.rows_inserted += 1

        return PriceCurveModel(
            row['price_curve_name'],
            row['price_curve_type'],
            row['description'],
            row['currency_id'],
            row['last_modified'],
            row['used_by_sales'] == 'Y',
            row['used_by_trading'] == 'Y',
            row['time_zone']
        )