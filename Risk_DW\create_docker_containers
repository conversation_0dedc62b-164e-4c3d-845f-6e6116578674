docker run -v ./trading_dw_data:/home/<USER>/src/Risk_DW/trading_dw_data -d --name=kolektor-risk-dw  kolektor-risk-dw

az login

az acr login --name kolektorsetup

docker build -t kolektor-risk-dw .

docker build -f Dockerfile.dev -t kolektor-risk-dw .

docker buildx build --load --platform linux/amd64 -t kolektor-risk-dw .

docker tag sha256:bb363dcb514ff8a783dcaa65b8b8a724fa4b4a43679628511087c24312ac20cb kolektorsetup.azurecr.io/fsi/kolektor-risk-dw:kolektor-risk-dw-1.0.0

docker push kolektorsetup.azurecr.io/fsi/kolektor-risk-dw:kolektor-risk-dw-1.0.0


az aks get-credentials --resource-group setup-flexibility --name flexibility-dev

az aks get-credentials --resource-group setup-flexibility --name flexibility-prod


az login --tenant 8541dae1-904b-433f-a318-030780dbfe80

kubectl config set-context --current --namespace=data-warehouse

kubectl describe telegraf-deployment-frr-mo-5456996577-gcmc5 -n data-warehouse

kubectl logs kolektor-risk-dw-5b696f9789-2q74w -c kolektor-risk-dw -n data-warehouse


kubectl apply -f ./k8s-prod/kolektor-risk-dw.yaml -n data-warehouse

kubectl delete -f ./k8s-prod/kolektor-risk-dw.yaml -n data-warehouse

kubectl delete pod telegraf-deployment-frr-mo-55cc5bc889-ct7nx -n data-warehouse

kubectl rollout restart deploy/kolektor-risk-dw -n data-warehouse

Monitor:

kubectl get pods -n data-warehouse

