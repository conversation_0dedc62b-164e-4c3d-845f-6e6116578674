from Facts.MTM.mtm_trader_model import MtmTraderModel
from typing import List, Dict
from Facts.MTM.mtm_service_queries import Queries
from Facts.MTM.base_mtm import BaseMTM

class TraderMTM(BaseMTM):
    def _get_mtm_type(self) -> str:
        return "trader"

    def _get_mtm_model(self):
        return MtmTraderModel

    def _get_buys(self) -> List[Dict]:
        raw_results = self._execute_query(Queries.open_buys_trader())
        return self._convert_decimal_values(raw_results)

    def _get_sells(self) -> List[Dict]:
        raw_results = self._execute_query(Queries.open_sells_trader())
        return self._convert_decimal_values(raw_results)

    def _create_new_mtm(self, trade: dict, mtm: float, pfc: dict) -> dict:
        base_mtm = self._create_base_mtm(trade, mtm, pfc)
        base_mtm["trader_id"] = trade["trader_id"]
        return base_mtm