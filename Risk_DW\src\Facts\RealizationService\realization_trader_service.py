from typing import List, Dict, Generator
from types import SimpleNamespace
from sqlalchemy.orm import Session
from Dimensions.Product.product_model import ProductModel
from Dimensions.Trader.trader_model import TraderModel
from Facts.RealizationService.realization_trader_model import RealizationTraderModel
from Facts.RealizationService.realization_service_base import RealizationServiceBase
from Facts.RealizationService.service_queries import Queries

class RealizationTraderService(RealizationServiceBase):
    def fetch_entities(self):
        with Session(self.database.engine) as session:
            products = session.query(ProductModel).all()
            traders = session.query(TraderModel).all()
        return traders, products
    
    def get_sells_generator(self, trader_id: int, product_id: int) -> Generator[Dict, None, None]:
        raw_results = self._execute_query(Queries.open_sells_trader(trader_id, product_id))
        converted_results = self._convert_decimal_to_float(raw_results)
        for sell in converted_results:
            yield sell

    def get_buys_generator(self, trader_id: int, product_id: int) -> Generator[Dict, None, None]:
        raw_results = self._execute_query(Queries.open_buys_trader(trader_id, product_id))
        converted_results = self._convert_decimal_to_float(raw_results)
        for buy in converted_results:
            yield buy

    def get_sells(self, trader_id: int, product_id: int) -> List[Dict]:
        return list(self.get_sells_generator(trader_id, product_id))

    def get_buys(self, trader_id: int, product_id: int) -> List[Dict]:
        return list(self.get_buys_generator(trader_id, product_id))

    def get_model(self):
        return RealizationTraderModel