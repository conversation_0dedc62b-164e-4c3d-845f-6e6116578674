from pyexcel_io.database.querysets import QuerysetsReader
from libs.setup_orm_utility import SetupOrmUtility
from Dimensions.Product.product_model import ProductModel
from Dimensions.ProductType.product_type_model import ProductTypeModel
from Dimensions.LoadProfile.load_profile_model import LoadProfileModel
from Dimensions.MarketType.market_type_model import MarketTypeModel
from Dimensions.Commodity.commodity_model import CommodityModel
from Dimensions.TradingPeriod.trading_period_model import TradingPeriodModel

import logging

logger = logging.getLogger(__name__)

class ProductImporter():

    def __init__(self, config_name):
        self.setup_orm_utility = SetupOrmUtility(config_name)


        query_sets = self.setup_orm_utility.codes_session.query(ProductModel).all()
        reader = QuerysetsReader(query_sets, ["name", "product_type_id","load_profile_id","market_type_id", "trading_period_id"])
        self.existing_products = list(reader.to_array())

    def import_data(self):
        self.rows_inserted = 0

        logger.info('Import started')

        self.trading_periods = self.setup_orm_utility.codes_session.query(TradingPeriodModel).all()
        self.product_types = self.setup_orm_utility.codes_session.query(ProductTypeModel).all()
        self.load_profiles = self.setup_orm_utility.codes_session.query(LoadProfileModel).all()
        self.market_types = self.setup_orm_utility.codes_session.query(MarketTypeModel).all()
        self.commodities = self.setup_orm_utility.codes_session.query(CommodityModel).all()

        self.setup_orm_utility.import_data(self.product_init_func, 'product', ProductModel)

        logger.info('Import ended. Inserted %s rows', self.rows_inserted)

    def product_init_func(self,row):

        product_type = next((x for x in self.product_types if x.name == row['product_type_id']), None)
        if product_type is None:
            logger.error('Missing product_type code "%s"', row['product_type_id'])
            return None

        load_profile = next((x for x in self.load_profiles if x.name == row['load_profile_id']), None)
        if load_profile is None:
            logger.error('Missing load_profile code "%s"', row['load_profile_id'])
            return None

        market_type = next((x for x in self.market_types if x.code == row['market_type_id']), None)
        if market_type is None:
            logger.error('Missing market_type code "%s"', row['market_type_id'])
            return None

        commodity = next((x for x in self.commodities if x.name == row['commodity_id']), None)
        if commodity is None:
            logger.error('Missing commodity code "%s"', row['commodity_id'])
            return None

        trading_period = next((x for x in self.trading_periods if x.name == row['trading_period_id']), None)
        if trading_period is None:
            logger.error('Missing trading_period code "%s"', row['trading_period_id'])
            return None

        for product in self.existing_products[1:]:
            if  product[0] == row["name"] and \
                product[1] == product_type.id and \
                product[2] == load_profile.id and \
                product[3] == market_type.id and \
                product[4] == trading_period.id:
                    return None

        self.rows_inserted += 1

        return ProductModel(
            row['name'],
            product_type.id,
            load_profile.id,
            market_type.id,
            commodity.id,
            trading_period.id)
