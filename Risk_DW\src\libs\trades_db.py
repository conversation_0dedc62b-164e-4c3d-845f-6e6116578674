
'''
from libs.setup_db_utility import SetupDbUtility

class TradesDb:
    
    def __init__(self, config_name):
        self.__setupDbUtility = SetupDbUtility(config_name)
    
    def get_dim_date(self, start_date, end_date):
        query = f"""
            SELECT id, date
            FROM trading_dw.dim_date
            WHERE date between '{start_date.strftime('%Y-%m-%d')}' AND '{end_date.strftime('%Y-%m-%d')}'
            """
            
        result = self.__setupDbUtility.read_data_from_database(query)
        
        dim_dates = result
        return dim_dates
    
    def get_countries(self):
        query = f"""
            SELECT id, country_code2
            FROM trading_dw.country
            """
        result = self.__setupDbUtility.read_data_from_database(query)
        
        countries = result
        return countries
    
    def get_parties(self):
        query = f"""
            SELECT vat
            FROM trading_dw.partner
            """
        result = self.__setupDbUtility.read_data_from_database(query)
        
        parties = {row[0] for row in result}
        return parties
        
    def get_day_ahead_prices(self, start_time,end_time):
        query = f"""
            SELECT tsv.date_time_value AS "time", tsv.numeric_value AS "value"
            FROM timeseries.time_series_value tsv, timeseries.level_relation_view ts
            WHERE tsv.time_series_id = ts.id 
                AND category = 'Power|Price|Actual|Day Ahead'
                AND provider = 'Entsoe|API'
                AND geography_from = 'SI|Country'
                AND ts.value_interval = 'Hour_1' 
                AND tsv.date_time_value BETWEEN '{start_time}' AND '{end_time}'
            ORDER BY time ASC
            """
        result = self.__setupDbUtility.read_data_from_database(query)
        
        prices = {row[0]: row[1] for row in result}
        return prices
    
    def get_hpfc_price(self, dt):
        query = f"""select date_time_tick as tick,
                date_time_value  "Delivery day", 
                numeric_value as Price
            FROM timeseries.time_series_value a, timeseries.level_relation_view b
            WHERE a.time_series_id = b.id
            and category = 'Power|Price|Synthetic|Day Ahead'
            and geography_from = 'SI|Country'
            AND date_time_value='{dt}'
            and date_time_tick < date_time_value
            order by date_time_tick desc
            LIMIT 1;

    """
    
        result = self.__setupDbUtility.read_data_from_database(query)
        if result:
            return float(result[0][2])
        return None
'''