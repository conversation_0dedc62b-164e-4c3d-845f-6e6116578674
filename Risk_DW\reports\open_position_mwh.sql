WITH sum_buy_mwh AS (
    SELECT
  		SUM(quantity) as quantity, date, country_code
	FROM
  		trading_dw.report_trades 		
  	WHERE 
  		position_type = 'Buy' and delivery_date > CURRENT_DATE
  	
	GROUP BY
  		date, country_code
),
sum_limit AS(
	SELECT 
		SUM(limit_value) AS limit, date, area
	FROM 
		trading_dw.report_limits 
	WHERE 
		limit_group_name = 'company' and limit_type_name='open_position_all' and unit='MWh'
	GROUP BY date, area
)

SELECT 
	b.quantity,
	l.area,
	b.date AS date,
	l.limit,
  	CASE
    	WHEN b.quantity > "limit" THEN 'PREKORACITEV'
    	ELSE 
			'OK'
  	END AS status
FROM 
	sum_buy_mwh b
INNER JOIN 
	sum_limit l ON EXTRACT(YEAR FROM b.date)::text = l.date and l.area = b.country_code
ORDER BY date