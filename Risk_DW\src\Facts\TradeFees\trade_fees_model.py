from sqlalchemy import Column, Integer, Numeric, String, Boolean, Foreign<PERSON>ey, DateTime
from sqlalchemy.orm import relationship
from setup_global import SetupGlobal
from libs.mixin import AuditMixin

class TradeFeesModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'trade_fees'

    id = Column(Integer, primary_key=True)
    identification_description = Column(String(100), ForeignKey('trade.identification_description'), nullable=False)
    billing_period_from_id = Column(Integer, ForeignKey('dim_time.id'), nullable=False)
    billing_period_to_id = Column(Integer, ForeignKey('dim_time.id'), nullable=False)
    total_qty = Column(Numeric(18, 6), nullable=False)
    total_amount_excluding_tax = Column(Numeric(18, 6), nullable=False)
    tax_amount = Column(Numeric(18, 6), nullable=True)
    issued_by_party_id = Column(Integer, Foreign<PERSON>ey('partner.id'), nullable=False)
    issued_to_party_id = Column(Integer, ForeignKey('partner.id'), nullable=False)
    invoice_issue_date_id = Column(Integer, ForeignKey('dim_time.id'), nullable=True)
    payment_due_date_id = Column(Integer, ForeignKey('dim_time.id'), nullable=False)
    netting_date = Column(DateTime(timezone=True), nullable=True)
    invoice_currency_code_id = Column(Integer, ForeignKey('currency.id'), nullable=False)
    general_agreement_identification = Column(String(50), nullable=True)
    is_deleted = Column(Boolean, default=False, nullable=False)

    # Relationships
    trade = relationship("TradeModel", back_populates="trade_fees")
    billing_period_from = relationship("DimTimeModel", foreign_keys=[billing_period_from_id])
    billing_period_to = relationship("DimTimeModel", foreign_keys=[billing_period_to_id])
    issued_by_party = relationship("PartnerModel", foreign_keys=[issued_by_party_id])
    issued_to_party = relationship("PartnerModel", foreign_keys=[issued_to_party_id])
    invoice_issue_date = relationship("DimTimeModel", foreign_keys=[invoice_issue_date_id])
    payment_due_date = relationship("DimTimeModel", foreign_keys=[payment_due_date_id])
    invoice_currency = relationship("CurrencyModel", foreign_keys=[invoice_currency_code_id])

    def __init__(
        self,
        identification_description,
        billing_period_from_id,
        billing_period_to_id,
        total_qty,
        total_amount_excluding_tax,
        tax_amount,
        issued_by_party_id,
        issued_to_party_id,
        invoice_issue_date_id,
        payment_due_date_id,
        netting_date,
        invoice_currency_code_id,
        general_agreement_identification,
        is_deleted=False
    ):
        self.identification_description = identification_description
        self.billing_period_from_id = billing_period_from_id
        self.billing_period_to_id = billing_period_to_id
        self.total_qty = total_qty
        self.total_amount_excluding_tax = total_amount_excluding_tax
        self.tax_amount = tax_amount
        self.issued_by_party_id = issued_by_party_id
        self.issued_to_party_id = issued_to_party_id
        self.invoice_issue_date_id = invoice_issue_date_id
        self.payment_due_date_id = payment_due_date_id
        self.netting_date = netting_date
        self.invoice_currency_code_id = invoice_currency_code_id
        self.general_agreement_identification = general_agreement_identification
        self.is_deleted = is_deleted
