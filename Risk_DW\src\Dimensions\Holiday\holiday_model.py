from sqlalchemy import  Column, Integer, String, Boolean, DateTime
from setup_global import SetupGlobal
from libs.mixin import AuditMixin

class HolidayModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'holiday'
    date = Column(DateTime, primary_key=True)
    country_id = Column(Integer, primary_key=True)
    is_work_free_day = Column(Boolean)
    is_pre_work_free_day = Column(Boolean)
    is_post_work_free_day = Column(Boolean)
    is_bridge = Column(Boolean)


    def __init__ (self,
                date,
                country_id,
                is_work_free_day,
                is_pre_work_free_day,
                is_post_work_free_day,
                is_bridge):

        self.date = date
        self.country_id = country_id
        self.is_work_free_day = is_work_free_day
        self.is_pre_work_free_day = is_pre_work_free_day
        self.is_post_work_free_day = is_post_work_free_day
        self.is_bridge = is_bridge
