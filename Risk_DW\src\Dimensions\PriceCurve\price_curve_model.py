from sqlalchemy import Column, DateTime, Integer, String, Text, Boolean
from setup_global import SetupGlobal
from libs.mixin import AuditMixin

class PriceCurveModel(SetupGlobal.TradingDwBase, AuditMixin):
    __tablename__ = 'price_curve'
    id = Column(Integer, primary_key=True)
    price_curve_name = Column(String(100))
    price_curve_type = Column(String(50))
    description = Column(Text)
    currency_id = Column(String(3))
    last_modified = Column(DateTime)
    used_by_sales = Column(Boolean)
    used_by_trading = Column(Boolean)
    time_zone = Column(String(50))


    def __init__(self,
            price_curve_name,
            price_curve_type,
            description,
            currency_id,
            last_modified,
            used_by_sales,
            used_by_trading,
            time_zone):

        self.price_curve_name = price_curve_name
        self.price_curve_type = price_curve_type
        self.description = description
        self.currency_id = currency_id
        self.last_modified = last_modified
        self.used_by_sales = used_by_sales
        self.used_by_trading = used_by_trading
        self.time_zone = time_zone
